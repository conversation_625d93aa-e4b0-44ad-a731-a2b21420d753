from mobility.funky import ImmutableDict
from mobility.ir.study import BaseCommuteData, TimedCommuteData, TimedStudy
from mobility.ir.transport import TransportMode


def fill_missing_emissions(study: TimedStudy) -> TimedStudy:
    filled_scenarios = study.scenarios.update_commutes(
        compute_missing_emissions_from_distance_with_alternatives
    )
    filled_poi_scenarios = study.poi_scenarios.update_commutes(
        compute_missing_emissions_from_distance_with_alternatives
    )
    coworking_scenario = study.coworking_scenario.update_commutes(
        compute_missing_emissions_from_distance_with_alternatives
    )
    return TimedStudy(
        scenarios=filled_scenarios,
        poi_scenarios=filled_poi_scenarios,
        infrastructure=study.infrastructure,
        data=study.data,
        geocode_failed_sites=study.geocode_failed_sites,
        geocode_failed_employees=study.geocode_failed_employees,
        geocode_failed_both=study.geocode_failed_both,
        isochrones=study.isochrones,
        coworking_scenario=coworking_scenario,
    )


def compute_missing_emissions_from_distance_with_alternatives(
    commute_data: TimedCommuteData,
) -> TimedCommuteData:
    base_commute_data = compute_missing_emissions_from_distance(
        BaseCommuteData(
            duration=commute_data.duration,
            distance=commute_data.distance,
            emission=commute_data.emission,
        )
    )
    alternative_arrival_times = {}
    for time, alt_commute_data in commute_data.alternative_arrival_time.items():
        alternative_arrival_times[time] = compute_missing_emissions_from_distance(
            alt_commute_data
        )
    return TimedCommuteData(
        duration=base_commute_data.duration,
        distance=base_commute_data.distance,
        emission=base_commute_data.emission,
        alternative_arrival_time=ImmutableDict(alternative_arrival_times),
    )


def compute_missing_emissions_from_distance(
    commute_data: BaseCommuteData,
) -> BaseCommuteData:
    filled_emission = {}
    zero_emission_mode = {TransportMode.WALK, TransportMode.BICYCLE}
    for mode in commute_data.duration:
        if mode in commute_data.emission:
            emission = commute_data.emission[mode]
        elif mode in zero_emission_mode:
            emission = 0
        elif mode in commute_data.distance:
            emission = compute_mode_emission_from_distance(
                mode, commute_data.distance[mode]
            )
        else:
            raise ValueError(
                f"Missing distance to compute {mode} emission in {commute_data}"
            )
        filled_emission[mode] = emission
    return BaseCommuteData(
        duration=commute_data.duration,
        distance=commute_data.distance,
        emission=ImmutableDict(filled_emission),
    )


def compute_mode_emission_from_distance(mode: TransportMode, distance: int) -> int:
    if mode == TransportMode.WALK or mode == TransportMode.BICYCLE:
        return 0
    elif mode == TransportMode.CAR:
        return 1932 * distance // 10000
    elif mode == TransportMode.PUBLIC_TRANSPORT:
        """We use 29gEC/km as a mean of emission from various modes:
        Bus: 103 gEC/km
        TER: 29 gEC/km
        RER: 5.4 gEC/km
        Metro: 3.8 gEC/km
        Tramway: 3.1 gEC/km
        """
        return 29 * distance // 1000
    elif mode == TransportMode.CARPOOLING:
        return 960 * distance // 10000
    elif mode == TransportMode.ELECTRIC_BICYCLE:
        return 22 * distance // 10000
    elif mode == TransportMode.ELECTRIC_CAR:
        return 198 * distance // 10000
    elif mode == TransportMode.MOTORCYCLE:
        return 1650 * distance // 10000
    elif mode == TransportMode.ELECTRIC_MOTORCYCLE:
        return 198 * distance // 10000
    elif mode == TransportMode.AIRPLANE:
        return 230 * distance // 1000
    elif mode == TransportMode.FAST_BICYCLE:
        return 22 * distance // 10000
    elif mode == TransportMode.CAR_PUBLIC_TRANSPORT:
        car_distance = distance // 5
        pt_distance = distance - car_distance
        return compute_mode_emission_from_distance(
            TransportMode.CAR, car_distance
        ) + compute_mode_emission_from_distance(
            TransportMode.PUBLIC_TRANSPORT, pt_distance
        )
    elif mode == TransportMode.BICYCLE_PUBLIC_TRANSPORT:
        bike_distance = distance // 8
        pt_distance = distance - bike_distance
        return compute_mode_emission_from_distance(
            TransportMode.BICYCLE, bike_distance
        ) + compute_mode_emission_from_distance(
            TransportMode.PUBLIC_TRANSPORT, pt_distance
        )
    else:
        raise ValueError(
            f"Unable to compute emission from distance {distance} in mode {mode}"
        )

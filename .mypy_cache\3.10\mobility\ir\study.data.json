{".class": "MypyFile", "_fullname": "mobility.ir.study", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.BaseCommuteData", "kind": "Gdef"}, "BicycleAmenitiesScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.BicycleAmenitiesScenario", "line": 149, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "BicycleAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleAmenity", "kind": "Gdef"}, "BicycleLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleLine", "kind": "Gdef"}, "CarAmenitiesScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.CarAmenitiesScenario", "line": 150, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "CarAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarAmenity", "kind": "Gdef"}, "CarWay": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarWay", "kind": "Gdef"}, "Commute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Commute", "kind": "Gdef"}, "ConsolidatedCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ConsolidatedCommute", "line": 193, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "ConsolidatedScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ConsolidatedScenario", "line": 194, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "ConsolidatedScenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ConsolidatedScenarios", "line": 195, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "ConsolidatedStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.ConsolidatedStudy", "name": "ConsolidatedStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.ConsolidatedStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 201, "name": "scenarios", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 202, "name": "poi_scenarios", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 203, "name": "infrastructure", "type": "mobility.ir.study.TimedInfrastructure"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 204, "name": "data", "type": "mobility.ir.study.StudyData"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 205, "name": "time_failed", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 206, "name": "geocode_failed_sites", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 207, "name": "geocode_failed_employees", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 208, "name": "geocode_failed_both", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 209, "name": "isochrones", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 210, "name": "coworking_scenario", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 211, "name": "coworking_failed_scenario", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.ConsolidatedStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "time_failed", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.ConsolidatedStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "time_failed", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConsolidatedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.ConsolidatedStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "poi_scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "infrastructure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_sites"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "isochrones"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_failed_scenario"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "time_failed", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.ConsolidatedStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "time_failed", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsolidatedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "time_failed", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsolidatedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "coworking_failed_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.coworking_failed_scenario", "name": "coworking_failed_scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}}}, "coworking_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.coworking_scenario", "name": "coworking_scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CoworkingScenario"}}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.StudyData"}}, "extract_employee_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "employee"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.extract_employee_id", "name": "extract_employee_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "employee"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "UnionType", "items": ["mobility.ir.employee.GeoEmployee", "mobility.ir.employee.GeoEmployee", "mobility.ir.employee.FailedGeoEmployee"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract_employee_id of ConsolidatedStudy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_scenario_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.extract_scenario_id", "name": "extract_scenario_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", "mobility.ir.scenario_data.ScenarioData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract_scenario_id of ConsolidatedStudy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_site_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "site"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.extract_site_id", "name": "extract_site_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "site"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.FailedGeoSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract_site_id of ConsolidatedStudy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "geocode_failed_both": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.geocode_failed_both", "name": "geocode_failed_both", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.geocode_failed_employees", "name": "geocode_failed_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_sites": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.geocode_failed_sites", "name": "geocode_failed_sites", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "get_pois": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.get_pois", "name": "get_pois", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_pois of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.get_prefix", "name": "get_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_prefix of ConsolidatedStudy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "infrastructure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.infrastructure", "name": "infrastructure", "setter_type": null, "type": "mobility.ir.study.TimedInfrastructure"}}, "is_diagnosis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.is_diagnosis", "name": "is_diagnosis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_diagnosis of ConsolidatedStudy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isochrones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.isochrones", "name": "isochrones", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}}, "iterate_on_all_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.iterate_on_all_employees", "name": "iterate_on_all_employees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_on_all_employees of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["mobility.ir.employee.GeoEmployee", "mobility.ir.employee.FailedGeoEmployee"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iterate_on_all_scenarios_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.iterate_on_all_scenarios_data", "name": "iterate_on_all_scenarios_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_on_all_scenarios_data of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iterate_on_all_sites": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.iterate_on_all_sites", "name": "iterate_on_all_sites", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_on_all_sites of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.FailedGeoSite"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_bicycle_amenity_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_bicycle_amenity_scenario", "name": "jsonify_bicycle_amenity_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_bicycle_amenity_scenario of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_car_amenity_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_car_amenity_scenario", "name": "jsonify_car_amenity_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_car_amenity_scenario of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_commute_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_commute_data", "name": "jsonify_commute_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commute_data"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "UnionType", "items": [{".class": "NoneType"}, "mobility.ir.commute_data.ModalCommuteData", "mobility.ir.commute_data.TimedCommuteData", "mobility.ir.commute_data.BaseCommuteData"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_commute_data of ConsolidatedStudy", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_coworking_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_coworking_scenario", "name": "jsonify_coworking_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_coworking_scenario of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "isochrones"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_isochrone", "name": "jsonify_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "isochrones"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_isochrone of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_poi_scenarios", "name": "jsonify_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_poi_scenarios of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_public_transport_stations_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_public_transport_stations_scenario", "name": "jsonify_public_transport_stations_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_public_transport_stations_scenario of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jsonify_scenarios_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.jsonify_scenarios_data", "name": "jsonify_scenarios_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.ir.study.ConsolidatedStudy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jsonify_scenarios_data of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poi_scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.poi_scenarios", "name": "poi_scenarios", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenarios"}}}, "time_failed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ConsolidatedStudy.time_failed", "name": "time_failed", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.ConsolidatedStudy.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_json of ConsolidatedStudy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.ConsolidatedStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.ConsolidatedStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Constraints": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_constraints.Constraints", "kind": "Gdef"}, "CoworkingScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.CoworkingScenario", "line": 196, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "CoworkingSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.CoworkingSite", "kind": "Gdef"}, "CsvEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.CsvEmployee", "kind": "Gdef"}, "CsvSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.CsvSite", "kind": "Gdef"}, "CsvStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.CsvStudy", "name": "CsvStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.CsvStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.CsvEmployee", "mobility.ir.site.CsvSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "data", "type": "mobility.ir.study.CsvStudyDataParameter"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "pois", "type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.CsvStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.CsvStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scenarios", "data", "pois"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.CsvStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scenarios", "data", "pois"], "arg_types": ["mobility.ir.study.CsvStudy", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.CsvEmployee", "mobility.ir.site.CsvSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CsvStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.CsvStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pois"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["scenarios", "data", "pois"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.CsvStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["scenarios", "data", "pois"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.CsvEmployee", "mobility.ir.site.CsvSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CsvStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.CsvStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["scenarios", "data", "pois"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.CsvEmployee", "mobility.ir.site.CsvSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CsvStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.CsvStudyDataParameter"}}, "pois": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudy.pois", "name": "pois", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.CsvEmployee", "mobility.ir.site.CsvSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.CsvStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.CsvStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CsvStudyDataParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.CsvStudyDataParameter", "name": "CsvStudyDataParameter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.CsvStudyDataParameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "company", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "mission_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "arrival_time", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "agency", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "constraints", "type": "mobility.ir.mode_constraints.Constraints"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.CsvStudyDataParameter", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "company", "mission_id", "arrival_time", "agency", "constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.CsvStudyDataParameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "company", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["mobility.ir.study.CsvStudyDataParameter", "builtins.str", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CsvStudyDataParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.CsvStudyDataParameter.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "company"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mission_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arrival_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "agency"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constraints"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["company", "mission_id", "arrival_time", "agency", "constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.CsvStudyDataParameter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["company", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["builtins.str", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CsvStudyDataParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["company", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["builtins.str", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CsvStudyDataParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "agency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.agency", "name": "agency", "setter_type": null, "type": "builtins.str"}}, "arrival_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.arrival_time", "name": "arrival_time", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "company": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.company", "name": "company", "setter_type": null, "type": "builtins.str"}}, "constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.constraints", "name": "constraints", "setter_type": null, "type": "mobility.ir.mode_constraints.Constraints"}}, "from_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.study.CsvStudyDataParameter.from_json", "name": "from_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.study.CsvStudyDataParameter"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of CsvStudyDataParameter", "ret_type": "mobility.ir.study.CsvStudyDataParameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.study.CsvStudyDataParameter.from_json", "name": "from_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.study.CsvStudyDataParameter"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of CsvStudyDataParameter", "ret_type": "mobility.ir.study.CsvStudyDataParameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mission_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.CsvStudyDataParameter.mission_id", "name": "mission_id", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.CsvStudyDataParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.CsvStudyDataParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FailedGeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.FailedGeoEmployee", "kind": "Gdef"}, "FailedGeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.FailedGeoSite", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoScenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.GeoScenarios", "line": 56, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "GeoStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.GeoStudy", "name": "GeoStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.GeoStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "scenarios", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.GeoScenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "poi_scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "data", "type": "mobility.ir.study.CsvStudyDataParameter"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "geocode_failed_sites", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "geocode_failed_employees", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "geocode_failed_both", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.GeoStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.GeoStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both"], "arg_types": ["mobility.ir.study.GeoStudy", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.GeoScenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of GeoStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.GeoStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "poi_scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_sites"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_both"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.GeoStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.GeoScenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of GeoStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.GeoScenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.CsvStudyDataParameter", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of GeoStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.CsvStudyDataParameter"}}, "geocode_failed_both": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.geocode_failed_both", "name": "geocode_failed_both", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.geocode_failed_employees", "name": "geocode_failed_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_sites": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.geocode_failed_sites", "name": "geocode_failed_sites", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "poi_scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.poi_scenarios", "name": "poi_scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.GeoStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.GeoScenarios"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.GeoStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.GeoStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Infrastructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.Infrastructure", "name": "Infrastructure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.Infrastructure", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 108, "name": "public_transport_stations_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 111, "name": "public_transport_stops", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 112, "name": "public_transport_lines", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 113, "name": "bicycle_amenity_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 114, "name": "bicycle_amenity", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 115, "name": "bicycle_lines", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 116, "name": "car_amenity_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 117, "name": "car_amenity", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 118, "name": "car_ways", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.Infrastructure", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.Infrastructure.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": ["mobility.ir.study.Infrastructure", {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Infrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.Infrastructure.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_stations_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_stops"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_amenity_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_amenity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_amenity_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_amenity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_ways"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.Infrastructure.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": [{".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Infrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": [{".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Infrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bicycle_amenity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.bicycle_amenity", "name": "bicycle_amenity", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "bicycle_amenity_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.bicycle_amenity_scenario", "name": "bicycle_amenity_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "bicycle_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.bicycle_lines", "name": "bicycle_lines", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "car_amenity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.car_amenity", "name": "car_amenity", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "car_amenity_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.car_amenity_scenario", "name": "car_amenity_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "car_ways": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.car_ways", "name": "car_ways", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "public_transport_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.public_transport_lines", "name": "public_transport_lines", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "public_transport_stations_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.public_transport_stations_scenario", "name": "public_transport_stations_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "public_transport_stops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.Infrastructure.public_transport_stops", "name": "public_transport_stops", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.Infrastructure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.Infrastructure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "JSON_VERSION": {".class": "SymbolTableNode", "cross_ref": "mobility.constants.JSON_VERSION", "kind": "Gdef"}, "Localised": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Localised", "kind": "Gdef"}, "LocalisedCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.LocalisedCommute", "line": 102, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.geo_study.Localised", "mobility.ir.geo_study.Localised", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "LocalisedStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.LocalisedStudy", "name": "LocalisedStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.LocalisedStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 123, "name": "scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 124, "name": "poi_scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 125, "name": "infrastructure", "type": "mobility.ir.study.Infrastructure"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 126, "name": "data", "type": "mobility.ir.study.StudyData"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 127, "name": "geocode_failed_sites", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 128, "name": "geocode_failed_employees", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 129, "name": "geocode_failed_both", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 130, "name": "coworking_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.LocalisedStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.LocalisedStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "coworking_scenario"], "arg_types": ["mobility.ir.study.LocalisedStudy", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.Infrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LocalisedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.LocalisedStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "poi_scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "infrastructure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_sites"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_scenario"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.LocalisedStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.Infrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of LocalisedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "mobility.ir.study.Infrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of LocalisedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "coworking_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.coworking_scenario", "name": "coworking_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.StudyData"}}, "geocode_failed_both": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.geocode_failed_both", "name": "geocode_failed_both", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.geocode_failed_employees", "name": "geocode_failed_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_sites": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.geocode_failed_sites", "name": "geocode_failed_sites", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "infrastructure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.infrastructure", "name": "infrastructure", "setter_type": null, "type": "mobility.ir.study.Infrastructure"}}, "poi_scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.poi_scenarios", "name": "poi_scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.LocalisedStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.LocalisedStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.LocalisedStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModalCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ModalCommute", "line": 141, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "ModalCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.ModalCommuteData", "kind": "Gdef"}, "ModalScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ModalScenario", "line": 142, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "ModalScenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.ModalScenarios", "line": 143, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "ModalStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.ModalStudy", "name": "ModalStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.ModalStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 182, "name": "scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 183, "name": "poi_scenarios", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 184, "name": "infrastructure", "type": "mobility.ir.study.TimedInfrastructure"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 185, "name": "data", "type": "mobility.ir.study.StudyData"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 186, "name": "geocode_failed_sites", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 187, "name": "geocode_failed_employees", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 188, "name": "geocode_failed_both", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 189, "name": "isochrones", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 190, "name": "coworking_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.ModalStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.ModalStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": ["mobility.ir.study.ModalStudy", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModalStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.ModalStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "poi_scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "infrastructure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_sites"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "isochrones"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_scenario"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.ModalStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModalStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModalStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "coworking_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.coworking_scenario", "name": "coworking_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.StudyData"}}, "geocode_failed_both": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.geocode_failed_both", "name": "geocode_failed_both", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.geocode_failed_employees", "name": "geocode_failed_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_sites": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.geocode_failed_sites", "name": "geocode_failed_sites", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "infrastructure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.infrastructure", "name": "infrastructure", "setter_type": null, "type": "mobility.ir.study.TimedInfrastructure"}}, "isochrones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.isochrones", "name": "isochrones", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}}, "poi_scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.poi_scenarios", "name": "poi_scenarios", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.ModalStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.ModalStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.ModalStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PoiScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.PoiScenario", "line": 144, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "PoiScenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.PoiScenarios", "line": 145, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "PointOfInterest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.poi.PointOfInterest", "kind": "Gdef"}, "PublicTransportLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportLine", "kind": "Gdef"}, "PublicTransportStationsScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.PublicTransportStationsScenario", "line": 146, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "PublicTransportStop": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportStop", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "Scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenario", "kind": "Gdef"}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "SiteIsochrone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.SiteIsochrone", "line": 151, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.site.GeoSite", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "Instance", "args": ["mobility.quantity.Quantity", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "StudyData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.StudyData", "name": "StudyData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.StudyData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "company", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 72, "name": "territory", "type": "mobility.ir.territory.Territory"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "mission_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "arrival_time", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "agency", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "constraints", "type": "mobility.ir.mode_constraints.Constraints"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.StudyData", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.StudyData.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "company", "territory", "mission_id", "arrival_time", "agency", "constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.StudyData.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "company", "territory", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["mobility.ir.study.StudyData", "builtins.str", "mobility.ir.territory.Territory", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StudyData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.StudyData.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "company"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "territory"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mission_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arrival_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "agency"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constraints"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["company", "territory", "mission_id", "arrival_time", "agency", "constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.StudyData.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["company", "territory", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["builtins.str", "mobility.ir.territory.Territory", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of StudyData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.StudyData.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["company", "territory", "mission_id", "arrival_time", "agency", "constraints"], "arg_types": ["builtins.str", "mobility.ir.territory.Territory", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "mobility.ir.mode_constraints.Constraints"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of StudyData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "agency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.agency", "name": "agency", "setter_type": null, "type": "builtins.str"}}, "arrival_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.arrival_time", "name": "arrival_time", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "company": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.company", "name": "company", "setter_type": null, "type": "builtins.str"}}, "constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.constraints", "name": "constraints", "setter_type": null, "type": "mobility.ir.mode_constraints.Constraints"}}, "from_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.study.StudyData.from_json", "name": "from_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.study.StudyData"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of StudyData", "ret_type": "mobility.ir.study.StudyData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.study.StudyData.from_json", "name": "from_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.study.StudyData"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of StudyData", "ret_type": "mobility.ir.study.StudyData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mission_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.mission_id", "name": "mission_id", "setter_type": null, "type": "builtins.str"}}, "territory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.StudyData.territory", "name": "territory", "setter_type": null, "type": "mobility.ir.territory.Territory"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.study.StudyData.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.study.StudyData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_json of StudyData", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.StudyData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.StudyData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TimedBicycleCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedBicycleCommute", "line": 136, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.site.GeoSite", "mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TimedCarCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedCarCommute", "line": 137, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.site.GeoSite", "mobility.ir.infrastructure.CarAmenity", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TimedCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedCommute", "line": 133, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "TimedCommutes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedCommutes", "line": 138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.TimedCommute"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.TimedPoiCommute"}], "uses_pep604_syntax": false}}}, "TimedInfrastructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.TimedInfrastructure", "name": "TimedInfrastructure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.TimedInfrastructure", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 156, "name": "public_transport_stations_scenario", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 157, "name": "public_transport_stops", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 158, "name": "public_transport_lines", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 159, "name": "bicycle_amenity_scenario", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 160, "name": "bicycle_amenity", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 161, "name": "bicycle_lines", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 162, "name": "car_amenity_scenario", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 163, "name": "car_amenity", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 164, "name": "car_ways", "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.TimedInfrastructure", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.TimedInfrastructure.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": ["mobility.ir.study.TimedInfrastructure", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TimedInfrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.TimedInfrastructure.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_stations_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_stops"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "public_transport_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_amenity_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_amenity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_amenity_scenario"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_amenity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "car_ways"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.TimedInfrastructure.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimedInfrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["public_transport_stations_scenario", "public_transport_stops", "public_transport_lines", "bicycle_amenity_scenario", "bicycle_amenity", "bicycle_lines", "car_amenity_scenario", "car_amenity", "car_ways"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimedInfrastructure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bicycle_amenity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.bicycle_amenity", "name": "bicycle_amenity", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "bicycle_amenity_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.bicycle_amenity_scenario", "name": "bicycle_amenity_scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}}}, "bicycle_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.bicycle_lines", "name": "bicycle_lines", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "car_amenity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.car_amenity", "name": "car_amenity", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "car_amenity_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.car_amenity_scenario", "name": "car_amenity_scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}}}, "car_ways": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.car_ways", "name": "car_ways", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "public_transport_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.public_transport_lines", "name": "public_transport_lines", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "public_transport_stations_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.public_transport_stations_scenario", "name": "public_transport_stations_scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}}}, "public_transport_stops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedInfrastructure.public_transport_stops", "name": "public_transport_stops", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.TimedInfrastructure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.TimedInfrastructure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimedLocalisedCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedLocalisedCommute", "line": 103, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.geo_study.Localised", "mobility.ir.geo_study.Localised", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TimedPoiCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedPoiCommute", "line": 134, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TimedScenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedScenario", "line": 139, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "TimedScenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedScenarios", "line": 140, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "TimedStudy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.study.TimedStudy", "name": "TimedStudy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.study.TimedStudy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 169, "name": "scenarios", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 170, "name": "poi_scenarios", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 171, "name": "infrastructure", "type": "mobility.ir.study.TimedInfrastructure"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 172, "name": "data", "type": "mobility.ir.study.StudyData"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 173, "name": "geocode_failed_sites", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 174, "name": "geocode_failed_employees", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 175, "name": "geocode_failed_both", "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 176, "name": "isochrones", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 177, "name": "coworking_scenario", "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.study", "mro": ["mobility.ir.study.TimedStudy", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.study.TimedStudy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": ["mobility.ir.study.TimedStudy", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TimedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.study.TimedStudy.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "poi_scenarios"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "infrastructure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_sites"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geocode_failed_both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "isochrones"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_scenario"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.study.TimedStudy.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["scenarios", "poi_scenarios", "infrastructure", "data", "geocode_failed_sites", "geocode_failed_employees", "geocode_failed_both", "isochrones", "coworking_scenario"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}, "mobility.ir.study.TimedInfrastructure", "mobility.ir.study.StudyData", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimedStudy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "coworking_scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.coworking_scenario", "name": "coworking_scenario", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.data", "name": "data", "setter_type": null, "type": "mobility.ir.study.StudyData"}}, "geocode_failed_both": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.geocode_failed_both", "name": "geocode_failed_both", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.geocode_failed_employees", "name": "geocode_failed_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "geocode_failed_sites": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.geocode_failed_sites", "name": "geocode_failed_sites", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "infrastructure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.infrastructure", "name": "infrastructure", "setter_type": null, "type": "mobility.ir.study.TimedInfrastructure"}}, "isochrones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.isochrones", "name": "isochrones", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}}}, "poi_scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.poi_scenarios", "name": "poi_scenarios", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenarios"}}}, "scenarios": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.study.TimedStudy.scenarios", "name": "scenarios", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.study.TimedStudy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.study.TimedStudy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimedTransportStopCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.study.TimedTransportStopCommute", "line": 135, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.site.GeoSite", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.study.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}}, "path": "mobility\\ir\\study.py"}
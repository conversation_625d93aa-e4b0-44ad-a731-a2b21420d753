{"data_mtime": 1751444420, "dep_lines": [5, 1, 1, 1, 1, 1, 1, 3], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 5], "dependencies": ["mobility.workers.color_picker", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "mobility.workers"], "hash": "f9916acb5b7a6e38cf7daaf69a65a35883c0399e", "id": "mobility.serializers.plotly_theme", "ignore_all": false, "interface_hash": "fc5e7eef6ff3193d886a06507e0d20fa6e42c785", "mtime": 1722327418, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\plotly_theme.py", "plugin_data": null, "size": 11360, "suppressed": ["plotly"], "version_id": "1.16.1"}
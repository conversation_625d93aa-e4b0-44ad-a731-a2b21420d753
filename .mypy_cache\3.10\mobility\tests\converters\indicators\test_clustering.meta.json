{"data_mtime": 1751444477, "dep_lines": [6, 4, 12, 13, 19, 2, 5, 11, 20, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.clustering", "api_abstraction.api.geocode_api", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.transport", "unittest.mock", "mobility.constants", "mobility.funky", "mobility.quantity", "typing", "unittest", "builtins", "_frozen_importlib", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.clustering_indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "typing_extensions"], "hash": "2bb6c39775895424505eab69cf96320531b21a9d", "id": "mobility.tests.converters.indicators.test_clustering", "ignore_all": false, "interface_hash": "b6c5972675af099a34279b2b1f492ee8e8c20d39", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\converters\\indicators\\test_clustering.py", "plugin_data": null, "size": 10235, "suppressed": [], "version_id": "1.16.1"}
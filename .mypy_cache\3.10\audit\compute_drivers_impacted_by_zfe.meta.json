{"data_mtime": 1752154445, "dep_lines": [15, 16, 17, 18, 20, 21, 22, 23, 24, 19, 1, 2, 3, 4, 5, 6, 10, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 9, 11], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5], "dependencies": ["mobility.builders.territory_database", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.serializers.chart_writer", "mobility.serializers.plotly_map_maker", "mobility.serializers.plotly_theme", "mobility.workers.distance_computer", "mobility.workers.emission_computer", "mobility.quantity", "csv", "functools", "json", "os", "collections", "typing", "numpy", "mobility", "builtins", "_collections_abc", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "enum", "io", "json.decoder", "mobility.builders", "mobility.ir", "mobility.ir.map_elements", "mobility.serializers", "mobility.serializers.base_map_maker", "mobility.serializers.charters", "mobility.serializers.charters.svg_map_addons", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "typing_extensions", "mobility.workers"], "hash": "10fbcacca81b09efca01e59adcb935c038b0978c", "id": "audit.compute_drivers_impacted_by_zfe", "ignore_all": false, "interface_hash": "4b3d45391fa00bfcb265d529a9bd0f33d6cf6d44", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "audit\\compute_drivers_impacted_by_zfe.py", "plugin_data": null, "size": 16293, "suppressed": ["fiona", "g<PERSON><PERSON><PERSON>", "plotly"], "version_id": "1.16.1"}
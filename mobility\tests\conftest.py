import dataclasses
from typing import Any, Callable, Dict, FrozenSet, List, Optional, Tuple

import factory
import pandas
import pytest
from pytest_factoryboy import register

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.trivial.trivial_geocoder import TrivialGeocoder
from api_abstraction.trivial.trivial_travel_time import TrivialTravelTimer
from mobility.builders.territory_builder import TerritoryBuilder
from mobility.constants import (
    EMPLOYEE_ADDRESSES_SHEET_NAME,
    PARAMETERS_SHEET_NAME,
    SITE_ADDRESSES_SHEET_NAME,
)
from mobility.converters.indicators.mode_shift_scenario import ModeShiftCommutesSplit
from mobility.funky import ImmutableDict, make_dir_for
from mobility.ir.commute_data import BaseCommuteData
from mobility.ir.crit_air import CritAirCategory
from mobility.ir.error import Error
from mobility.ir.geo_study import Address, GeoCoordinates, Trip
from mobility.ir.indicators.mode_shift_scenario_indicators import NoModeShiftReason
from mobility.ir.indicators.scenario_indicators import ScenarioIndicators
from mobility.ir.infrastructure import (
    AmenityType,
    BicycleAmenity,
    BicycleLine,
    CarAmenity,
    CarWay,
    PublicTransportLine,
    PublicTransportStop,
)
from mobility.ir.map_elements import ArcGISIdentity
from mobility.ir.mode_constraints import Constraints
from mobility.ir.poi import PointOfInterest
from mobility.ir.site import CoworkingSite
from mobility.ir.study import (
    ConsolidatedCommute,
    ConsolidatedScenario,
    ConsolidatedScenarios,
    ConsolidatedStudy,
    CoworkingScenario,
    CsvStudyDataParameter,
    FailedGeoEmployee,
    FailedGeoSite,
    GeoEmployee,
    GeoSite,
    GeoStudy,
    Infrastructure,
    LocalisedStudy,
    ModalCommute,
    ModalCommuteData,
    ModalScenario,
    ModalStudy,
    PoiScenario,
    PublicTransportStationsScenario,
    ScenarioData,
    SiteIsochrone,
    StudyData,
    TimedCommute,
    TimedCommuteData,
    TimedInfrastructure,
    TimedPoiCommute,
    TimedScenario,
    TimedStudy,
    TimedTransportStopCommute,
)
from mobility.ir.study_types import Commute, Scenario, Scenarios
from mobility.ir.territory import BoundingBox, Territory, TerritoryData
from mobility.ir.transport import TransportMode
from mobility.serializers import StudyJsonSerializer
from mobility.serializers.geopackage_serializer import (
    ModalShare,
    POIFeature,
    PostCodeModalShareFeature,
    SiteCommuteProperties,
)
from mobility.workers.carpool_computer import CarpoolingCluster, CarpoolingClusters


@pytest.fixture
def write_file():
    def _write_file(file_path: str, content: str):
        make_dir_for(file_path)
        with open(file_path, "w+") as f:
            f.write(content)

    return _write_file


empty_distribution_dict = {
    (0, 600): 0,
    (600, 1200): 0,
    (1200, 1800): 0,
    (1800, 2400): 0,
    (2400, 3000): 0,
    (3000, 3600): 0,
    (3600, 4200): 0,
    (4200, 4800): 0,
    (4800, 5400): 0,
    (5400, 6000): 0,
    (6000, 6600): 0,
    (6600, 7200): 0,
}


empty_diff_distribution_dict = {
    (-3300, -2700): 0,
    (-2700, -2100): 0,
    (-2100, -1500): 0,
    (-1500, -900): 0,
    (-900, -300): 0,
    (-300, 300): 0,
    (300, 900): 0,
    (900, 1500): 0,
    (1500, 2100): 0,
    (2100, 2700): 0,
    (2700, 3300): 0,
}


empty_repartition_dict = {mode: 0 for mode in TransportMode}

empty_time_by_mode_dict = {mode: None for mode in TransportMode}


@pytest.fixture
def empty_distribution():
    return empty_distribution_dict


@pytest.fixture
def empty_diff_distribution():
    return empty_diff_distribution_dict


@pytest.fixture
def empty_repartition():
    return empty_repartition_dict


@register
class GeoCoordinatesFactory(factory.Factory):
    class Meta:
        model = GeoCoordinates

    longitude = 4.5
    latitude = 45.2


@register
class AddressFactory(factory.Factory):
    class Meta:
        model = Address

    full = "fake address"
    normalized = "normalized fake address"
    city = "fake city name"
    postcode = "12123"
    citycode = "12345"
    coordinates = GeoCoordinatesFactory(longitude=1.0, latitude=2.0)


@register
class GeoEmployeeFactory(factory.Factory):
    class Meta:
        model = GeoEmployee

    id = 1
    nickname = "employee_01"
    address = "emlpoyee_address_01"
    address_details = AddressFactory()
    transport_mode = TransportMode.WALK
    coordinates = GeoCoordinatesFactory(longitude=1.0, latitude=2.0)
    remote = True
    crit_air = CritAirCategory.CRITAIR_NON_CLASSE


@register
class GeoSiteFactory(factory.Factory):
    class Meta:
        model = GeoSite

    id = 1
    nickname = "site_01"
    address = "site_address_01"
    address_details = AddressFactory()
    coordinates = GeoCoordinatesFactory(longitude=5.0, latitude=46.0)
    car_parking_cost = None
    bicycle_parking_cost = None


@register
class PointOfInterestFactory(factory.Factory):
    class Meta:
        model = PointOfInterest

    name = "Gare Part-Dieu"
    coordinates = GeoCoordinatesFactory(latitude=45.760749, longitude=4.861171)


@register
class BaseCommuteDataFactory(factory.Factory):
    class Meta:
        model = BaseCommuteData

    duration = ImmutableDict(
        {
            TransportMode.BICYCLE: 1600,
            TransportMode.CAR: 800,
            TransportMode.PUBLIC_TRANSPORT: 1200,
            TransportMode.WALK: 3200,
            TransportMode.CARPOOLING: 900,
            TransportMode.ELECTRIC_BICYCLE: 1500,
            TransportMode.ELECTRIC_CAR: 850,
            TransportMode.MOTORCYCLE: 700,
            TransportMode.AIRPLANE: 3600,
            TransportMode.ELECTRIC_MOTORCYCLE: 750,
            TransportMode.FAST_BICYCLE: 1400,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
        }
    )
    distance = ImmutableDict(
        {
            TransportMode.BICYCLE: 1800,
            TransportMode.CAR: 2000,
            TransportMode.PUBLIC_TRANSPORT: 2100,
            TransportMode.WALK: 1800,
            TransportMode.CARPOOLING: 2100,
            TransportMode.ELECTRIC_BICYCLE: 1800,
            TransportMode.ELECTRIC_CAR: 2000,
            TransportMode.MOTORCYCLE: 2000,
            TransportMode.AIRPLANE: 20000,
            TransportMode.ELECTRIC_MOTORCYCLE: 2000,
            TransportMode.FAST_BICYCLE: 2000,
            TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
        }
    )
    emission = ImmutableDict(
        {
            TransportMode.BICYCLE: 0,
            TransportMode.CAR: 4800,
            TransportMode.PUBLIC_TRANSPORT: 1000,
            TransportMode.WALK: 0,
            TransportMode.CARPOOLING: 2400,
            TransportMode.ELECTRIC_BICYCLE: 10,
            TransportMode.ELECTRIC_CAR: 1200,
            TransportMode.MOTORCYCLE: 4800,
            TransportMode.AIRPLANE: 10000,
            TransportMode.ELECTRIC_MOTORCYCLE: 15,
            TransportMode.FAST_BICYCLE: 10,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
        }
    )


@register
class TimedCommuteDataFactory(factory.Factory):
    class Meta:
        model = TimedCommuteData

    duration = ImmutableDict(
        {
            TransportMode.BICYCLE: 1600,
            TransportMode.CAR: 800,
            TransportMode.PUBLIC_TRANSPORT: 1200,
            TransportMode.WALK: 3200,
            TransportMode.CARPOOLING: 900,
            TransportMode.ELECTRIC_BICYCLE: 1500,
            TransportMode.ELECTRIC_CAR: 850,
            TransportMode.MOTORCYCLE: 700,
            TransportMode.AIRPLANE: 3600,
            TransportMode.ELECTRIC_MOTORCYCLE: 750,
            TransportMode.FAST_BICYCLE: 1400,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
        }
    )
    distance = ImmutableDict(
        {
            TransportMode.BICYCLE: 1800,
            TransportMode.CAR: 2000,
            TransportMode.PUBLIC_TRANSPORT: 2100,
            TransportMode.WALK: 1800,
            TransportMode.CARPOOLING: 2100,
            TransportMode.ELECTRIC_BICYCLE: 1800,
            TransportMode.ELECTRIC_CAR: 2000,
            TransportMode.MOTORCYCLE: 2000,
            TransportMode.AIRPLANE: 20000,
            TransportMode.ELECTRIC_MOTORCYCLE: 2000,
            TransportMode.FAST_BICYCLE: 2000,
            TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
        }
    )
    emission = ImmutableDict(
        {
            TransportMode.BICYCLE: 0,
            TransportMode.CAR: 4800,
            TransportMode.PUBLIC_TRANSPORT: 1000,
            TransportMode.WALK: 0,
            TransportMode.CARPOOLING: 2400,
            TransportMode.ELECTRIC_BICYCLE: 10,
            TransportMode.ELECTRIC_CAR: 1200,
            TransportMode.MOTORCYCLE: 4800,
            TransportMode.AIRPLANE: 10000,
            TransportMode.ELECTRIC_MOTORCYCLE: 15,
            TransportMode.FAST_BICYCLE: 10,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
        }
    )
    alternative_arrival_time: ImmutableDict = ImmutableDict()


@register
class ModalCommuteDataFactory(factory.Factory):
    class Meta:
        model = ModalCommuteData

    duration = ImmutableDict(
        {
            TransportMode.BICYCLE: 1600,
            TransportMode.CAR: 800,
            TransportMode.PUBLIC_TRANSPORT: 1200,
            TransportMode.WALK: 3200,
            TransportMode.CARPOOLING: 900,
            TransportMode.ELECTRIC_BICYCLE: 1500,
            TransportMode.ELECTRIC_CAR: 850,
            TransportMode.MOTORCYCLE: 700,
            TransportMode.AIRPLANE: 3600,
            TransportMode.ELECTRIC_MOTORCYCLE: 750,
            TransportMode.FAST_BICYCLE: 1400,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
        }
    )
    distance = ImmutableDict(
        {
            TransportMode.BICYCLE: 1800,
            TransportMode.CAR: 2000,
            TransportMode.PUBLIC_TRANSPORT: 2100,
            TransportMode.WALK: 1800,
            TransportMode.CARPOOLING: 2100,
            TransportMode.ELECTRIC_BICYCLE: 1800,
            TransportMode.ELECTRIC_CAR: 2000,
            TransportMode.MOTORCYCLE: 2000,
            TransportMode.AIRPLANE: 20000,
            TransportMode.ELECTRIC_MOTORCYCLE: 2000,
            TransportMode.FAST_BICYCLE: 2000,
            TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
        }
    )
    emission = ImmutableDict(
        {
            TransportMode.BICYCLE: 0,
            TransportMode.CAR: 4800,
            TransportMode.PUBLIC_TRANSPORT: 1000,
            TransportMode.WALK: 0,
            TransportMode.CARPOOLING: 2400,
            TransportMode.ELECTRIC_BICYCLE: 10,
            TransportMode.ELECTRIC_CAR: 1200,
            TransportMode.MOTORCYCLE: 4800,
            TransportMode.AIRPLANE: 10000,
            TransportMode.ELECTRIC_MOTORCYCLE: 15,
            TransportMode.FAST_BICYCLE: 10,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
        }
    )
    best_mode = TransportMode.WALK
    alternative_arrival_time: ImmutableDict = ImmutableDict()


@register
class FailedGeoEmployeeFactory(factory.Factory):
    class Meta:
        model = FailedGeoEmployee

    id = 2
    nickname = "employee_02"
    address = "emlpoyee_address_02"
    transport_mode = TransportMode.WALK
    remote = True
    failure = Error("geocode error", "unfortunate hun")


@register
class FailedGeoSiteFactory(factory.Factory):
    class Meta:
        model = FailedGeoSite

    id = 2
    nickname = "site_02"
    address = "site_address_02"
    failure = Error("geocode error", "unfortunate hun")
    car_parking_cost = None
    bicycle_parking_cost = None


@register
class ScenarioDataFactory(factory.Factory):
    class Meta:
        model = ScenarioData

    id = 1
    nickname = "trivial"
    is_present = True


@pytest.fixture
def consolidated_commute_factory(
    geo_employee: GeoEmployee,
    geo_site: GeoSite,
    modal_commute_data: ModalCommuteData,
) -> Callable[..., ConsolidatedCommute]:
    def builder(
        employee=geo_employee,
        site=geo_site,
        data=modal_commute_data,
    ):
        return Commute(
            origin=employee,
            destination=site,
            data=data,
        )

    return builder


@pytest.fixture
def consolidated_commute(consolidated_commute_factory) -> ConsolidatedCommute:
    return consolidated_commute_factory()


@pytest.fixture
def coworking_commute_factory(
    geo_employee: GeoEmployee,
    coworking_site: Callable,
    modal_commute_data: ModalCommuteData,
) -> Callable[..., ConsolidatedCommute]:
    def builder(
        employee=geo_employee,
        site=coworking_site(),
        data=modal_commute_data,
    ):
        return Commute(
            origin=employee,
            destination=site,
            data=data,
        )

    return builder


@pytest.fixture
def coworking_commute(coworking_commute_factory) -> ConsolidatedCommute:
    return coworking_commute_factory()


@pytest.fixture
def consolidated_scenario_factory(
    consolidated_commute,
) -> Callable[..., ConsolidatedScenario]:
    def builder(
        id=1,
        nickname="trivial",
        is_present=True,
        commutes=frozenset({consolidated_commute}),
    ):
        return ConsolidatedScenario(
            data=ScenarioData(id=id, nickname=nickname, is_present=is_present),
            commutes=commutes,
        )

    return builder


@pytest.fixture
def consolidated_scenario(consolidated_scenario_factory) -> ConsolidatedScenario:
    return consolidated_scenario_factory()


@pytest.fixture
def coworking_scenario_factory(
    coworking_commute,
) -> Callable[..., Scenario]:
    def builder(
        commutes=frozenset({coworking_commute}),
    ):
        return Scenario(
            data=None,
            commutes=commutes,
        )

    return builder


@pytest.fixture
def coworking_scenario(coworking_scenario_factory) -> Scenario:
    return coworking_scenario_factory()


@pytest.fixture
def study_data() -> Callable[..., StudyData]:
    def builder(
        company: str = "Modelity",
        territory: Territory = Territory.LYON,
        mission_id: str = "YYMXXX.V",
        arrival_time: Tuple[int, int] = (8, 30),
        agency: str = "",
        constraints: Constraints = Constraints.from_dict({}),
    ) -> StudyData:
        return StudyData(
            company=company,
            territory=territory,
            mission_id=mission_id,
            arrival_time=arrival_time,
            agency=agency,
            constraints=constraints,
        )

    return builder


@pytest.fixture
def localised_study_factory(
    study_data, infrastructure: Any, poi_scenario: Any
) -> Callable[..., LocalisedStudy]:
    def builder(
        scenarios: Scenarios = Scenarios.empty(),
        poi_scenarios: Scenarios = Scenarios(frozenset({poi_scenario})),
        data: StudyData = study_data(),
        infrastructure: Infrastructure = infrastructure(),
        geocode_failed_sites: Scenarios = Scenarios.empty(),
        geocode_failed_employees: Scenarios = Scenarios.empty(),
        geocode_failed_both: Scenarios = Scenarios.empty(),
        coworking_scenario: Scenario = Scenario.empty(None),
    ) -> LocalisedStudy:
        return LocalisedStudy(
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            data=data,
            infrastructure=infrastructure,
            geocode_failed_sites=geocode_failed_sites,
            geocode_failed_employees=geocode_failed_employees,
            geocode_failed_both=geocode_failed_both,
            coworking_scenario=coworking_scenario,
        )

    return builder


@pytest.fixture
def consolidated_study_factory(
    consolidated_scenario,
    poi_scenario,
    timed_infrastructure: Any,
    study_data: Any,
) -> Callable[..., ConsolidatedStudy]:
    def builder(
        data: StudyData = study_data(),
        scenarios: ConsolidatedScenarios = Scenarios(
            frozenset({consolidated_scenario})
        ),
        poi_scenarios: Scenarios = Scenarios(frozenset({poi_scenario})),
        infrastructure: TimedInfrastructure = timed_infrastructure(),
        time_failed: Scenarios = Scenarios.empty(),
        geocode_failed_sites: Scenarios = Scenarios.empty(),
        geocode_failed_employees: Scenarios = Scenarios.empty(),
        geocode_failed_both: Scenarios = Scenarios.empty(),
        isochrones: SiteIsochrone = {},
        coworking_scenario: Scenario = Scenario.empty(None),
        coworking_failed_scenario: Scenario = Scenario.empty(None),
    ) -> ConsolidatedStudy:
        return ConsolidatedStudy(
            data=data,
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            infrastructure=infrastructure,
            time_failed=time_failed,
            geocode_failed_sites=geocode_failed_sites,
            geocode_failed_employees=geocode_failed_employees,
            geocode_failed_both=geocode_failed_both,
            isochrones=isochrones,
            coworking_scenario=coworking_scenario,
            coworking_failed_scenario=coworking_failed_scenario,
        )

    return builder


@pytest.fixture
def consolidated_study(consolidated_study_factory) -> ConsolidatedStudy:
    return consolidated_study_factory()


def employees_commutes(modal_commute_data, geo_site, geo_employee):
    def _employees_commutes():
        return {geo_employee().id: modal_commute_data()}

    return _employees_commutes


@pytest.fixture
def timed_commute_factory(
    geo_employee: GeoEmployee,
    geo_site: GeoSite,
    timed_commute_data: TimedCommuteData,
) -> Callable[..., TimedCommute]:
    def builder(employee=geo_employee, site=geo_site, data=timed_commute_data):
        return Commute(
            origin=employee,
            destination=site,
            data=data,
        )

    return builder


@pytest.fixture
def modal_commute_factory(
    geo_employee: GeoEmployee,
    geo_site: GeoSite,
    modal_commute_data: ModalCommuteData,
) -> Callable[..., ModalCommute]:
    def builder(employee=geo_employee, site=geo_site, data=modal_commute_data):
        return Commute(
            origin=employee,
            destination=site,
            data=data,
        )

    return builder


@pytest.fixture
def timed_poi_commute_factory(
    geo_site: GeoSite,
    point_of_interest: PointOfInterest,
    timed_commute_data: TimedCommuteData,
) -> Callable[..., TimedPoiCommute]:
    def builder(site=geo_site, poi=point_of_interest, data=timed_commute_data):
        return Commute(origin=site, destination=poi, data=data)

    return builder


@pytest.fixture
def timed_transport_stop_commute_factory(
    geo_site: GeoSite,
    public_transport_stop: PublicTransportStop,
    base_commute_data: BaseCommuteData,
) -> Callable[..., TimedTransportStopCommute]:
    def builder(site=geo_site, pt_stop=public_transport_stop, data=base_commute_data):
        return Commute(origin=site, destination=pt_stop(), data=data())

    return builder


@pytest.fixture
def timed_commute(timed_commute_factory) -> TimedCommute:
    return timed_commute_factory()


@pytest.fixture
def modal_commute(modal_commute_factory) -> ModalCommute:
    return modal_commute_factory()


@pytest.fixture
def poi_commute(timed_poi_commute_factory) -> TimedPoiCommute:
    return timed_poi_commute_factory()


@pytest.fixture
def timed_transport_stop_commute(
    timed_transport_stop_commute_factory,
) -> TimedTransportStopCommute:
    return timed_transport_stop_commute_factory()


@pytest.fixture
def timed_scenario_factory(timed_commute) -> Callable[..., TimedScenario]:
    def builder(
        id=1,
        nickname="trivial",
        is_present=True,
        commutes=frozenset({timed_commute}),
    ):
        return TimedScenario(
            data=ScenarioData(id=id, nickname=nickname, is_present=is_present),
            commutes=commutes,
        )

    return builder


@pytest.fixture
def modal_scenario_factory(
    scenario_data, modal_commute
) -> Callable[..., ModalScenario]:
    def builder(
        data: ScenarioData = scenario_data,
        commutes=frozenset({modal_commute}),
    ):
        return ModalScenario(
            data=data,
            commutes=commutes,
        )

    return builder


@pytest.fixture
def poi_scenario_factory(poi_commute) -> Callable[..., PoiScenario]:
    def builder(
        id=1,
        nickname="trivial",
        is_present=True,
        commutes=frozenset({poi_commute}),
    ):
        return PoiScenario(
            data=ScenarioData(id=id, nickname=nickname, is_present=is_present),
            commutes=commutes,
        )

    return builder


@pytest.fixture
def public_transport_stations_scenario_factory(
    timed_transport_stop_commute,
) -> Callable[..., PublicTransportStationsScenario]:
    def builder(commutes=frozenset({timed_transport_stop_commute})):
        return PublicTransportStationsScenario(
            data=None,
            commutes=commutes,
        )

    return builder


@pytest.fixture
def public_transport_stations_scenario(
    public_transport_stations_scenario_factory,
) -> PublicTransportStationsScenario:
    return public_transport_stations_scenario_factory()


@pytest.fixture
def timed_scenario(timed_scenario_factory) -> TimedScenario:
    return timed_scenario_factory()


@pytest.fixture
def poi_scenario(poi_scenario_factory) -> PoiScenario:
    return poi_scenario_factory()


@pytest.fixture
def modal_scenario(modal_scenario_factory) -> ModalScenario:
    return modal_scenario_factory()


@pytest.fixture
def timed_study_factory(
    timed_scenario, poi_scenario, timed_infrastructure, study_data
) -> Callable[..., TimedStudy]:
    def builder(
        scenarios: Scenarios = Scenarios(frozenset({timed_scenario})),
        poi_scenarios: Scenarios = Scenarios(frozenset({poi_scenario})),
        infrastructure: TimedInfrastructure = timed_infrastructure(),
        data=study_data(),
        geocode_failed_sites: Scenarios = Scenarios.empty(),
        geocode_failed_employees: Scenarios = Scenarios.empty(),
        geocode_failed_both: Scenarios = Scenarios.empty(),
        isochrones: SiteIsochrone = {},
        coworking_scenario: Scenario = Scenario.empty(None),
    ):
        return TimedStudy(
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=geocode_failed_sites,
            geocode_failed_employees=geocode_failed_employees,
            geocode_failed_both=geocode_failed_both,
            isochrones=isochrones,
            coworking_scenario=coworking_scenario,
        )

    return builder


@pytest.fixture
def timed_study(timed_study_factory) -> TimedStudy:
    return timed_study_factory()


@pytest.fixture
def modal_study_factory(
    modal_scenario, timed_infrastructure, study_data, poi_scenario
) -> Callable[..., ModalStudy]:
    def builder(
        scenarios: Scenarios = Scenarios(frozenset({modal_scenario})),
        poi_scenarios: Scenarios = Scenarios(frozenset({poi_scenario})),
        infrastructure: TimedInfrastructure = timed_infrastructure(),
        data=study_data(),
        geocode_failed_sites: Scenarios = Scenarios.empty(),
        geocode_failed_employees: Scenarios = Scenarios.empty(),
        geocode_failed_both: Scenarios = Scenarios.empty(),
        isochrones: SiteIsochrone = {},
        coworking_scenario: Scenario = Scenario.empty(None),
    ):
        return ModalStudy(
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=geocode_failed_sites,
            geocode_failed_employees=geocode_failed_employees,
            geocode_failed_both=geocode_failed_both,
            isochrones=isochrones,
            coworking_scenario=coworking_scenario,
        )

    return builder


@pytest.fixture
def modal_study(modal_study_factory) -> ModalStudy:
    return modal_study_factory()


@pytest.fixture
def scenario_with_modes(
    geo_employee_factory,
    geo_site,
    modal_commute_data_factory,
    scenario_data,
):
    def factory(
        spec: List[Tuple[TransportMode, Dict[TransportMode, int]]],
    ):
        commutes = []
        for i, (preferred_mode, transport_mode_and_times) in enumerate(spec):
            employee = geo_employee_factory(
                id=f"employee_{i}",
            )
            commute = Commute(
                origin=employee,
                destination=geo_site,
                data=modal_commute_data_factory(
                    duration=ImmutableDict(transport_mode_and_times),
                    best_mode=preferred_mode,
                ),
            )
            commutes.append(commute)
        frozen_commutes = frozenset(commutes)
        scenario = Scenario(
            data=scenario_data,
            commutes=frozen_commutes,
        )
        return scenario

    return factory


@pytest.fixture
def one_line_employees_df():
    return pandas.DataFrame(
        {
            "Id Interne": ["SAL001"],
            "Adresses des employés": ["43 rue des lilas Lyon 69001 France"],
            "Modes de transport des employés": ["Vélo"],
            "Télétravail": ["Oui"],
            "Site actuel": ["mon site"],
        }
    )


@pytest.fixture
def one_line_sites_df():
    return pandas.DataFrame(
        {
            "Identifiant du site": ["mon site"],
            "Adresses des lieux à analyser": [
                "27 cours du maréchal Gambetta Bourg-en-Bresse 01000 France"
            ],
        }
    )


@pytest.fixture
def one_line_parameters_df():
    return pandas.DataFrame(
        {
            "Paramètre": ["param1"],
            "Valeur": ["val1"],
        }
    )


@pytest.fixture
def one_employee_one_site_input_file(
    one_line_sites_df, one_line_employees_df, one_line_parameters_df, tmpdir
):
    input_path = tmpdir / "input_modelity.xlsx"
    with pandas.ExcelWriter(input_path) as writer:
        one_line_sites_df.to_excel(
            writer, sheet_name=SITE_ADDRESSES_SHEET_NAME, index=False
        )
        one_line_employees_df.to_excel(
            writer, sheet_name=EMPLOYEE_ADDRESSES_SHEET_NAME, index=False
        )
        one_line_parameters_df.to_excel(
            writer, sheet_name=PARAMETERS_SHEET_NAME, index=False
        )
    return input_path


@pytest.fixture
def fake_territory(tmpdir, write_file):
    default_content = "<?xml version='1.0' encoding='UTF-8'?><osm version='0.6'></osm>"
    default_bbox_content = "2.0,1.0,4.0,3.0"
    default_json_coworking_sites = "[]"
    default_json_crit_air = "{}"
    default_json_company_potentials = "{}"

    def _make_fake_territory(
        bbox_content: Optional[str] = default_bbox_content,
        pt_routes_content: Optional[str] = default_content,
        bicycle_content: Optional[str] = default_content,
        car_content: Optional[str] = default_content,
        coworking_sites_content: Optional[str] = default_json_coworking_sites,
        custom_files: Dict[str, str] = {},
        crit_air_content: Optional[str] = default_json_crit_air,
        company_potentials_content: Optional[str] = default_json_company_potentials,
    ) -> Tuple[str, str]:
        territories_dir = tmpdir / "territories"
        territory_name = "avignon"
        if bbox_content is not None:
            bounding_box_file = (
                territories_dir
                / territory_name
                / TerritoryBuilder.BOUNDING_BOX_FILENAME
            )
            write_file(
                bounding_box_file,
                bbox_content,
            )
        if pt_routes_content is not None:
            public_transport_route_file = (
                territories_dir
                / territory_name
                / TerritoryBuilder.PUBLIC_TRANSPORT_ROUTES_FILENAME
            )
            write_file(
                public_transport_route_file,
                pt_routes_content,
            )
        if bicycle_content is not None:
            bicycle_route_file = (
                territories_dir
                / territory_name
                / TerritoryBuilder.BICYCLE_ROUTES_FILENAME
            )
            write_file(
                bicycle_route_file,
                bicycle_content,
            )
        if car_content is not None:
            car_route_file = (
                territories_dir / territory_name / TerritoryBuilder.CAR_ROUTES_FILENAME
            )
            write_file(car_route_file, car_content)
        if coworking_sites_content is not None:
            coworking_sites_file = territories_dir / TerritoryBuilder.COWORKING_FILENAME
            write_file(coworking_sites_file, coworking_sites_content)
        for custom_file_name, content in custom_files.items():
            custom_file_path = (
                territories_dir
                / territory_name
                / TerritoryBuilder.CUSTOM_PUBLIC_TRANSPORT_LINES_DIRNAME
                / custom_file_name
            )
            write_file(custom_file_path, content)
        crit_air_json_file = tmpdir / "crit_air_by_city_code" / "crit_air.json"
        if crit_air_content is not None:
            write_file(crit_air_json_file, crit_air_content)
        company_potentials_json_file = territories_dir / "company_potential_cache.json"
        if company_potentials_content is not None:
            write_file(company_potentials_json_file, company_potentials_content)
        return str(territories_dir), territory_name

    return _make_fake_territory


@pytest.fixture
def study_json_file(
    consolidated_study_factory, tmpdir, timed_infrastructure, poi_scenario
):
    the_consolidated_study = consolidated_study_factory()
    StudyJsonSerializer(str(tmpdir), the_consolidated_study, "").serialize()
    return str(
        tmpdir / f"Diagnostic_mobilité_{the_consolidated_study.data.company}_.json"
    )


@pytest.fixture
def trivial_geocoder():
    return TrivialGeocoder("", EventReporter())


@pytest.fixture
def trivial_travel_timer():
    return TrivialTravelTimer("", EventReporter())


@pytest.fixture
def dummy_study(
    scenario_data_factory: Any,
    geo_site_factory: Any,
    geo_employee: Any,
    modal_commute_data: Any,
    timed_infrastructure: Any,
):
    current_scenario_data = scenario_data_factory(id=1, is_present=True)
    next_scenario_data = scenario_data_factory(id=2, is_present=False)
    empty_scenarios: Scenarios = Scenarios.from_commutes(
        {current_scenario_data: set(), next_scenario_data: set()}
    )
    current_site = geo_site_factory(id=1)
    second_site = geo_site_factory(id=2)
    consolidated_scenarios = Scenarios.from_commutes(
        {
            current_scenario_data: {
                (geo_employee, current_site, modal_commute_data),
                (geo_employee, second_site, modal_commute_data),
            },
            next_scenario_data: {
                (geo_employee, second_site, modal_commute_data),
                (geo_employee, current_site, modal_commute_data),
            },
        }
    )
    infrastructure = timed_infrastructure()

    return ConsolidatedStudy(
        scenarios=consolidated_scenarios,
        poi_scenarios=empty_scenarios,
        infrastructure=infrastructure,
        data=StudyData(
            "Modelity",
            Territory.LYON,
            "24M000.0",
            (8, 30),
            "",
            Constraints.from_dict({}),
        ),
        time_failed=empty_scenarios,
        geocode_failed_sites=empty_scenarios,
        geocode_failed_employees=empty_scenarios,
        geocode_failed_both=empty_scenarios,
        isochrones={},
        coworking_scenario=Scenario.empty(None),
        coworking_failed_scenario=Scenario.empty(None),
    )


@pytest.fixture
def dummy_diagnosis(
    scenario_data: Any,
    geo_site_factory: Any,
    geo_employee: Any,
    modal_commute_data: Any,
    timed_infrastructure: Any,
):
    site = geo_site_factory(id=1)
    empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})
    consolidated_scenarios = Scenarios.from_commutes(
        {scenario_data: {(geo_employee, site, modal_commute_data)}}
    )
    infrastructure = timed_infrastructure()
    return ConsolidatedStudy(
        scenarios=consolidated_scenarios,
        poi_scenarios=empty_scenarios,
        infrastructure=infrastructure,
        data=StudyData(
            "Modelity",
            Territory.LYON,
            "YYMXXX.V",
            (8, 30),
            "",
            Constraints.from_dict({}),
        ),
        time_failed=empty_scenarios,
        geocode_failed_sites=empty_scenarios,
        geocode_failed_employees=empty_scenarios,
        geocode_failed_both=empty_scenarios,
        isochrones={},
        coworking_scenario=Scenario.empty(None),
        coworking_failed_scenario=Scenario.empty(None),
    )


@pytest.fixture()
def timed_infrastructure():
    def _timed_infrastructure(**kwargs):
        infrastructure = TimedInfrastructure(
            public_transport_stations_scenario=Scenario(None, frozenset()),
            public_transport_stops=frozenset(),
            public_transport_lines=frozenset(),
            bicycle_amenity_scenario=Scenario(None, frozenset()),
            bicycle_amenity=frozenset(),
            bicycle_lines=frozenset(),
            car_amenity_scenario=Scenario(None, frozenset()),
            car_amenity=frozenset(),
            car_ways=frozenset(),
        )
        return dataclasses.replace(infrastructure, **kwargs)

    return _timed_infrastructure


@pytest.fixture()
def infrastructure():
    def _infrastructure(**kwargs):
        infrastructure = Infrastructure(
            public_transport_stations_scenario=Scenario(None, frozenset()),
            public_transport_stops=frozenset(),
            public_transport_lines=frozenset(),
            bicycle_amenity_scenario=Scenario(None, frozenset()),
            bicycle_amenity=frozenset(),
            bicycle_lines=frozenset(),
            car_amenity_scenario=Scenario(None, frozenset()),
            car_amenity=frozenset(),
            car_ways=frozenset(),
        )
        return dataclasses.replace(infrastructure, **kwargs)

    return _infrastructure


@pytest.fixture()
def territory_data():
    default_name = "Fake Territory"
    default_bounding_box = BoundingBox(
        GeoCoordinates(0.0, 1.0), GeoCoordinates(2.0, 3.0)
    )
    default_public_transport_stops = []
    default_public_transport_lines = []
    default_bicycle_lines = []
    default_bicycle_amenities = []
    default_car_ways = []
    default_car_amenities = []

    def _territory_data(
        name: Optional[str] = None,
        bounding_box: Optional[BoundingBox] = None,
        public_transport_stops: Optional[List[PublicTransportStop]] = None,
        public_transport_lines: Optional[List[PublicTransportLine]] = None,
        bicycle_lines: Optional[List[BicycleLine]] = None,
        bicycle_amenities: Optional[List[BicycleAmenity]] = None,
        car_ways: Optional[List[CarWay]] = None,
        car_amenities: Optional[List[CarAmenity]] = None,
        coworking_sites: List[CoworkingSite] = [],
    ):
        name = default_name if name is None else name
        bounding_box = default_bounding_box if bounding_box is None else bounding_box
        public_transport_stops = (
            default_public_transport_stops
            if public_transport_stops is None
            else public_transport_stops
        )
        public_transport_lines = (
            default_public_transport_lines
            if public_transport_lines is None
            else public_transport_lines
        )
        bicycle_lines = (
            default_bicycle_lines if bicycle_lines is None else bicycle_lines
        )
        bicycle_amenities = (
            default_bicycle_amenities
            if bicycle_amenities is None
            else bicycle_amenities
        )
        car_ways = default_car_ways if car_ways is None else car_ways
        car_amenities = (
            default_car_amenities if car_amenities is None else car_amenities
        )
        return TerritoryData(
            name=name,
            bounding_box=bounding_box,
            public_transport_stops=public_transport_stops,
            public_transport_lines=public_transport_lines,
            bicycle_lines=bicycle_lines,
            bicycle_amenities=bicycle_amenities,
            car_ways=car_ways,
            car_amenities=car_amenities,
            coworking_sites=coworking_sites,
        )

    return _territory_data


@pytest.fixture()
def public_transport_stop():
    def _public_transport_stop(**kwargs):
        pt_stops = PublicTransportStop(
            name="l'arrêt des cars",
            coordinates=GeoCoordinates(1, 2),
            lines_connected=frozenset(),
            lines_kinds=frozenset(),
        )

        return dataclasses.replace(pt_stops, **kwargs)

    return _public_transport_stop


@pytest.fixture()
def bicycle_amenity():
    default_name = "fake bicycle amenity"
    default_coordinates = GeoCoordinates(latitude=1.0, longitude=1.0)
    default_geojson = '{"type":"Point","coordinates":[1.0,1.0]}'
    default_types = frozenset(AmenityType.BICYCLE_PARKING)
    default_capacity = None
    default_parking_type = None

    def _bicycle_amenity(
        name: Optional[str] = None,
        coordinates: Optional[GeoCoordinates] = None,
        geojson: Optional[str] = None,
        amenity_types: Optional[FrozenSet[AmenityType]] = None,
        capacity: Optional[int] = None,
        parking_type: Optional[str] = None,
    ) -> BicycleAmenity:
        name = default_name if name is None else name
        coordinates = default_coordinates if coordinates is None else coordinates
        geojson = default_geojson if geojson is None else geojson
        amenity_types = default_types if amenity_types is None else amenity_types
        capacity = default_capacity if capacity is None else capacity
        parking_type = default_parking_type if parking_type is None else parking_type
        return BicycleAmenity(
            name=name,
            coordinates=coordinates,
            geojson=geojson,
            types=amenity_types,
            capacity=capacity,
            parking_type=parking_type,
        )

    return _bicycle_amenity


@pytest.fixture()
def bicycle_line():
    def _bicycle_line(
        name: str = "fake bicycle lane",
        points: Tuple[Tuple[GeoCoordinates, ...], ...] = (
            (GeoCoordinates(1.0, 1.0), GeoCoordinates(2, 3)),
        ),
        geojson: str = '{"type":"Point","coordinates":[1.0,1.0]}',
        surface: Optional[str] = None,
        oneway: Optional[bool] = None,
        lighting: Optional[bool] = None,
        is_lane: bool = False,
    ) -> BicycleLine:
        return BicycleLine(
            name=name,
            points=points,
            geojson=geojson,
            surface=surface,
            oneway=oneway,
            lighting=lighting,
            is_lane=is_lane,
        )

    return _bicycle_line


@pytest.fixture()
def car_amenity():
    default_name = "fake car amenity"
    default_coordinates = GeoCoordinates(latitude=1.0, longitude=1.0)
    default_types = frozenset()
    default_capacity = None
    default_parking_type = None

    def _car_amenity(
        name: Optional[str] = None,
        coordinates: Optional[GeoCoordinates] = None,
        amenity_types: Optional[FrozenSet[AmenityType]] = None,
        capacity: Optional[int] = None,
        parking_type: Optional[str] = None,
    ) -> CarAmenity:
        name = default_name if name is None else name
        coordinates = default_coordinates if coordinates is None else coordinates
        amenity_types = default_types if amenity_types is None else amenity_types
        capacity = default_capacity if capacity is None else capacity
        parking_type = default_parking_type if parking_type is None else parking_type
        return CarAmenity(
            name=name,
            coordinates=coordinates,
            types=amenity_types,
            capacity=capacity,
            parking_type=parking_type,
            fee=False,
            access="yes",
        )

    return _car_amenity


@pytest.fixture()
def public_transport_line():
    def _public_transport_line(**kwargs):
        pt_lines = PublicTransportLine(
            name="ligne des cars",
            points=((GeoCoordinates(1, 2), GeoCoordinates(2, 3)),),
            operator="Sytralou",
            short_name="ligne cars",
            kind="bus",
            colour="#132456",
        )

        return dataclasses.replace(pt_lines, **kwargs)

    return _public_transport_line


@pytest.fixture()
def scenario_indicators(
    geo_employee, geo_site, modal_commute_data, timed_infrastructure, coworking_scenario
):
    commutes = {(geo_employee, geo_site, modal_commute_data)}
    default_scenario = Scenario.from_commutes(ScenarioData(1, "Lyon", True), commutes)
    default_coworking_scenario = coworking_scenario

    def _scenario_indicators(
        scenario: ConsolidatedScenario = default_scenario,
        coworking_scenario: CoworkingScenario = default_coworking_scenario,
    ) -> ScenarioIndicators:
        return ScenarioIndicators(scenario, coworking_scenario)

    return _scenario_indicators


@pytest.fixture()
def base_commute_data():
    default_duration = ImmutableDict(
        {
            TransportMode.WALK: 2000,
            TransportMode.BICYCLE: 1000,
            TransportMode.PUBLIC_TRANSPORT: 3000,
            TransportMode.CAR: 4000,
        }
    )
    default_distance = ImmutableDict(
        {
            TransportMode.WALK: 200,
            TransportMode.BICYCLE: 210,
            TransportMode.PUBLIC_TRANSPORT: 350,
            TransportMode.CAR: 500,
        }
    )
    default_emission = ImmutableDict(
        {
            TransportMode.WALK: 0,
            TransportMode.BICYCLE: 0,
            TransportMode.PUBLIC_TRANSPORT: 20,
            TransportMode.CAR: 1000,
        }
    )

    def _base_commute_data(
        duration: Optional[Dict[TransportMode, int]] = None,
        distance: Optional[Dict[TransportMode, int]] = None,
        emission: Optional[Dict[TransportMode, int]] = None,
    ) -> BaseCommuteData:
        duration = default_duration if duration is None else ImmutableDict(duration)
        distance = default_distance if distance is None else ImmutableDict(distance)
        emission = default_emission if emission is None else ImmutableDict(emission)
        return BaseCommuteData(duration=duration, distance=distance, emission=emission)

    return _base_commute_data


@pytest.fixture()
def address():
    default_full = "Trivial full address"
    default_normalized = "Trivially normalized address"
    default_city = "Trivial"
    default_postcode = "12345"
    default_citycode = "12345"

    default_coordinates = GeoCoordinates(latitude=1.0, longitude=2.0)

    def _address(
        full: Optional[str] = None,
        normalized: Optional[str] = None,
        city: Optional[str] = None,
        postcode: Optional[str] = None,
        citycode: Optional[str] = None,
        coordinates: Optional[GeoCoordinates] = None,
    ) -> Address:
        full = default_full if full is None else full
        normalized = default_normalized if normalized is None else normalized
        city = default_city if city is None else city
        postcode = default_postcode if postcode is None else postcode
        citycode = default_citycode if citycode is None else citycode
        coordinates = default_coordinates if coordinates is None else coordinates
        return Address(
            full=full,
            normalized=normalized,
            city=city,
            postcode=postcode,
            citycode=citycode,
            coordinates=coordinates,
        )

    return _address


@pytest.fixture()
def trip():
    default_city_code_origin = "26240"
    default_city_code_destination = "38100"
    default_category = "all"
    default_transport_mode = "total"
    default_nb_workers = 50

    def _trip(
        city_code_origin: Optional[str] = None,
        city_code_destination: Optional[str] = None,
        category: Optional[str] = None,
        transport_mode: Optional[str] = None,
        nb_workers: Optional[float] = None,
    ) -> Trip:
        city_code_origin = (
            default_city_code_origin if city_code_origin is None else city_code_origin
        )
        city_code_destination = (
            default_city_code_destination
            if city_code_destination is None
            else city_code_destination
        )
        category = default_category if category is None else category
        transport_mode = (
            default_transport_mode if transport_mode is None else transport_mode
        )
        nb_workers = default_nb_workers if nb_workers is None else nb_workers
        return Trip(
            city_code_origin=city_code_origin,
            city_code_destination=city_code_destination,
            category=category,
            transport_mode=transport_mode,
            nb_workers=nb_workers,
        )

    return _trip


@pytest.fixture()
def journey_attribute():
    def _journey_attribute(
        duration: int = 600,
        distance: int = 1000,
        emission: int = 300,
    ) -> JourneyAttribute:
        return JourneyAttribute(
            duration=duration,
            distance=distance,
            emission=emission,
        )

    return _journey_attribute


@pytest.fixture()
def geo_study():
    def _geo_study(
        scenarios: Scenarios[
            ScenarioData, GeoEmployee, GeoSite, None
        ] = Scenarios.empty(),
        poi_scenarios: Scenarios[
            ScenarioData, GeoSite, PointOfInterest, None
        ] = Scenarios.empty(),
        data: CsvStudyDataParameter = CsvStudyDataParameter(
            company="Modelity",
            mission_id="YYMXXX.V",
            arrival_time=(8, 30),
            agency="",
            constraints=Constraints.from_dict({}),
        ),
        geocode_failed_sites: Scenarios[
            ScenarioData, GeoEmployee, FailedGeoSite, None
        ] = Scenarios.empty(),
        geocode_failed_employees: Scenarios[
            ScenarioData, FailedGeoEmployee, GeoSite, None
        ] = Scenarios.empty(),
        geocode_failed_both: Scenarios[
            ScenarioData, FailedGeoEmployee, FailedGeoSite, None
        ] = Scenarios.empty(),
    ):
        return GeoStudy(
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            data=data,
            geocode_failed_sites=geocode_failed_sites,
            geocode_failed_employees=geocode_failed_employees,
            geocode_failed_both=geocode_failed_both,
        )

    return _geo_study


@pytest.fixture()
def mode_shift_commutes_split():
    def _mode_shift_commutes_split(
        shifters: List[ConsolidatedCommute] = [],
        non_shifters: List[ConsolidatedCommute] = [],
        no_shift_reason: Dict[GeoEmployee, NoModeShiftReason] = {},
    ) -> ModeShiftCommutesSplit:
        return ModeShiftCommutesSplit(
            shifters=shifters,
            non_shifters=non_shifters,
            no_shift_reason=no_shift_reason,
        )

    return _mode_shift_commutes_split


@pytest.fixture()
def carpooling_cluster(geo_site):
    def _carpooling_cluster(
        members: List[GeoEmployee] = [],
        site: GeoSite = geo_site,
        durations: List[int] = [],
        emissions: List[int] = [],
        distances: List[int] = [],
        direct_durations: List[int] = [],
        direct_emissions: List[int] = [],
        direct_distances: List[int] = [],
    ) -> CarpoolingCluster:
        return CarpoolingCluster(
            members=members,
            site=site,
            durations=durations,
            emissions=emissions,
            distances=distances,
            direct_durations=direct_durations,
            direct_emissions=direct_emissions,
            direct_distances=direct_distances,
        )

    return _carpooling_cluster


@pytest.fixture()
def carpooling_clusters():
    def _carpooling_clusters(
        groups: List[CarpoolingCluster] = [],
    ) -> CarpoolingClusters:
        return CarpoolingClusters(
            groups=groups,
        )

    return _carpooling_clusters


@pytest.fixture()
def arcgis_identity():
    def _arcgis_identity(
        website: str = "https://www.arcgis.com",
        username: str = "foo",
        password: str = "bar",
    ) -> ArcGISIdentity:
        return ArcGISIdentity(
            website=website,
            username=username,
            password=password,
        )

    return _arcgis_identity


@pytest.fixture()
def poi_feature(geo_coordinates: Any, site_commute_properties: Any) -> Callable:
    def _poi_feature(
        poi: str = "fake poi",
        coordinates: GeoCoordinates = geo_coordinates,
        site_commutes: List[SiteCommuteProperties] = [site_commute_properties()],
    ) -> POIFeature:
        return POIFeature(
            poi=poi,
            coordinates=coordinates,
            site_commutes=tuple(site_commutes),
        )

    return _poi_feature


@pytest.fixture()
def postcode_modal_share_feature(
    geo_coordinates: Any, site_commute_properties: Any, modal_share_full: Any
) -> Callable:
    def _postcode_modal_share_feature(
        postcode: str = "00000",
        coordinates: GeoCoordinates = geo_coordinates,
        current_modal_share: ModalShare = modal_share_full,
        scenario_modal_share: Dict[str, ModalShare] = dict(),
    ) -> PostCodeModalShareFeature:
        return PostCodeModalShareFeature(
            postcode=postcode,
            coordinates=coordinates,
            current_modal_share=current_modal_share,
            scenario_modal_share=ImmutableDict(scenario_modal_share),
        )

    return _postcode_modal_share_feature


@pytest.fixture()
def csv_study_data_parameters():
    def _csv_study_data_parameter(
        company: str = "Modelity",
        mission_id: str = "YYMXXX.V",
        arrival_time: Tuple[int, int] = (8, 30),
        agency: str = "",
        constraints: Constraints = Constraints.from_dict({}),
    ) -> CsvStudyDataParameter:
        return CsvStudyDataParameter(
            company=company,
            mission_id=mission_id,
            arrival_time=arrival_time,
            agency=agency,
            constraints=constraints,
        )

    return _csv_study_data_parameter

{"data_mtime": 1752154451, "dep_lines": [6, 7, 8, 9, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.indicators", "mobility.repositories.simulated_company", "mobility.use_cases.simulated_company_potential", "json", "random", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "json.encoder", "mobility", "mobility.ir", "mobility.ir.company_potential", "mobility.ir.indicators", "mobility.ir.study", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "os", "typing_extensions"], "hash": "174965fba1195a68c9b1e3386d5c942a97be382d", "id": "audit.generate_onboarding_app_cache_file", "ignore_all": false, "interface_hash": "e4a5ec43d116e908d2ca11ac2a05f68a803b9b4b", "mtime": 1723449306, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "audit\\generate_onboarding_app_cache_file.py", "plugin_data": null, "size": 19176, "suppressed": [], "version_id": "1.16.1"}
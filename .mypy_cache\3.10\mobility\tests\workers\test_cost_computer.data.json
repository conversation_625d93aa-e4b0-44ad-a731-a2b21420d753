{".class": "MypyFile", "_fullname": "mobility.tests.workers.test_cost_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BICYCLE_PARKING_YEARLY_COST": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.infrastructure_cost_calculator.BICYCLE_PARKING_YEARLY_COST", "kind": "Gdef"}, "CAR_PARKING_COST": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.infrastructure_cost_calculator.CAR_PARKING_COST", "kind": "Gdef"}, "Cost": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.Cost", "kind": "Gdef"}, "CostComputer": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.cost_computer.CostComputer", "kind": "Gdef"}, "CostKind": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostKind", "kind": "Gdef"}, "CostPayer": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostPayer", "kind": "Gdef"}, "Costs": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.Costs", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "TestBicycleParkingCommuteCostComputation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", "name": "TestBicycleParkingCommuteCostComputation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_cost_computer", "mro": ["mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", "builtins.object"], "names": {".class": "SymbolTable", "test_should_compute_bicycle_parking_commute_costs_with_employee_fee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation.test_should_compute_bicycle_parking_commute_costs_with_employee_fee", "name": "test_should_compute_bicycle_parking_commute_costs_with_employee_fee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_bicycle_parking_commute_costs_with_employee_fee of TestBicycleParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_compute_bicycle_parking_commute_costs_without_employee_fee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation.test_should_compute_bicycle_parking_commute_costs_without_employee_fee", "name": "test_should_compute_bicycle_parking_commute_costs_without_employee_fee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_bicycle_parking_commute_costs_without_employee_fee of TestBicycleParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "coworking_site", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation.test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site", "name": "test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "coworking_site", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site of TestBicycleParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_cost_computer.TestBicycleParkingCommuteCostComputation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestCarCostsComputation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation", "name": "TestCarCostsComputation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_cost_computer", "mro": ["mobility.tests.workers.test_cost_computer.TestCarCostsComputation", "builtins.object"], "names": {".class": "SymbolTable", "test_should_compute_car_costs_to_coworking_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "coworking_site", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation.test_should_compute_car_costs_to_coworking_site", "name": "test_should_compute_car_costs_to_coworking_site", "type": null}}, "test_should_compute_car_costs_to_geo_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation.test_should_compute_car_costs_to_geo_site", "name": "test_should_compute_car_costs_to_geo_site", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_cost_computer.TestCarCostsComputation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestCarParkingCommuteCostComputation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", "name": "TestCarParkingCommuteCostComputation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_cost_computer", "mro": ["mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", "builtins.object"], "names": {".class": "SymbolTable", "test_should_compute_car_parking_commute_costs_with_employee_fee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation.test_should_compute_car_parking_commute_costs_with_employee_fee", "name": "test_should_compute_car_parking_commute_costs_with_employee_fee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_car_parking_commute_costs_with_employee_fee of TestCarParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_compute_car_parking_commute_costs_without_employee_fee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation.test_should_compute_car_parking_commute_costs_without_employee_fee", "name": "test_should_compute_car_parking_commute_costs_without_employee_fee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "geo_site_factory", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_car_parking_commute_costs_without_employee_fee of TestCarParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_compute_no_car_parking_commute_costs_for_coworking_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "coworking_site", "cost_computer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation.test_should_compute_no_car_parking_commute_costs_for_coworking_site", "name": "test_should_compute_no_car_parking_commute_costs_for_coworking_site", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "coworking_site", "cost_computer"], "arg_types": ["mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_no_car_parking_commute_costs_for_coworking_site of TestCarParkingCommuteCostComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_cost_computer.TestCarParkingCommuteCostComputation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestComputeCostAdjustmentsForRemoteCommutes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes", "name": "TestComputeCostAdjustmentsForRemoteCommutes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_cost_computer", "mro": ["mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes", "builtins.object"], "names": {".class": "SymbolTable", "test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers", "name": "test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers", "name": "test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_adjust_costs_for_parking_spots_if_enough_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_parking_spots_if_enough_drivers", "name": "test_should_adjust_costs_for_parking_spots_if_enough_drivers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_parking_spots_if_enough_drivers", "name": "test_should_adjust_costs_for_parking_spots_if_enough_drivers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_adjust_costs_for_parking_spots_if_enough_drivers of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_adjust_costs_for_trip_related_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_trip_related_costs", "name": "test_should_adjust_costs_for_trip_related_costs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_costs_for_trip_related_costs", "name": "test_should_adjust_costs_for_trip_related_costs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_adjust_costs_for_trip_related_costs of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site", "name": "test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site", "name": "test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_not_adjust_costs_for_non_trip_related_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_costs_for_non_trip_related_costs", "name": "test_should_not_adjust_costs_for_non_trip_related_costs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_costs_for_non_trip_related_costs", "name": "test_should_not_adjust_costs_for_non_trip_related_costs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_not_adjust_costs_for_non_trip_related_costs of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers", "name": "test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers", "name": "test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_not_adjust_rent_costs_if_not_enough_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_rent_costs_if_not_enough_workers", "name": "test_should_not_adjust_rent_costs_if_not_enough_workers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_rent_costs_if_not_enough_workers", "name": "test_should_not_adjust_rent_costs_if_not_enough_workers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_not_adjust_rent_costs_if_not_enough_workers of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_not_adjust_rent_costs_if_workers_are_split_between_sites": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "base_commute_cost_computer", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_rent_costs_if_workers_are_split_between_sites", "name": "test_should_not_adjust_rent_costs_if_workers_are_split_between_sites", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.test_should_not_adjust_rent_costs_if_workers_are_split_between_sites", "name": "test_should_not_adjust_rent_costs_if_workers_are_split_between_sites", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_not_adjust_rent_costs_if_workers_are_split_between_sites of TestComputeCostAdjustmentsForRemoteCommutes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_cost_computer.TestComputeCostAdjustmentsForRemoteCommutes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestCostComputation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation", "name": "TestCostComputation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_cost_computer", "mro": ["mobility.tests.workers.test_cost_computer.TestCostComputation", "builtins.object"], "names": {".class": "SymbolTable", "test_should_compute_all_costs_from_carpooler_driver_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_carpooler_driver_commute", "name": "test_should_compute_all_costs_from_carpooler_driver_commute", "type": null}}, "test_should_compute_all_costs_from_single_agnostic_carpooling_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_agnostic_carpooling_commutes", "name": "test_should_compute_all_costs_from_single_agnostic_carpooling_commutes", "type": null}}, "test_should_compute_all_costs_from_single_bike_like_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "bike_like_mode", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_bike_like_commutes", "name": "test_should_compute_all_costs_from_single_bike_like_commutes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_bike_like_commutes", "name": "test_should_compute_all_costs_from_single_bike_like_commutes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_compute_all_costs_from_single_bike_pt_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_bike_pt_commute", "name": "test_should_compute_all_costs_from_single_bike_pt_commute", "type": null}}, "test_should_compute_all_costs_from_single_car_commutes_with_mobility_plan_benefits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_car_commutes_with_mobility_plan_benefits", "name": "test_should_compute_all_costs_from_single_car_commutes_with_mobility_plan_benefits", "type": null}}, "test_should_compute_all_costs_from_single_car_like_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "car_like_mode", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_car_like_commutes", "name": "test_should_compute_all_costs_from_single_car_like_commutes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_car_like_commutes", "name": "test_should_compute_all_costs_from_single_car_like_commutes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_compute_all_costs_from_single_car_pt_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_car_pt_commute", "name": "test_should_compute_all_costs_from_single_car_pt_commute", "type": null}}, "test_should_compute_all_costs_from_single_pt_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_pt_commutes", "name": "test_should_compute_all_costs_from_single_pt_commutes", "type": null}}, "test_should_compute_all_costs_from_single_walk_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data_factory", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_all_costs_from_single_walk_commutes", "name": "test_should_compute_all_costs_from_single_walk_commutes", "type": null}}, "test_should_compute_costs_from_empty_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cost_computer", "geo_employee_factory", "geo_site_factory", "modal_commute_data", "consolidated_commute_factory", "individual_costs", "cost", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_costs_from_empty_commutes", "name": "test_should_compute_costs_from_empty_commutes", "type": null}}, "test_should_compute_no_costs_from_empty_commutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cost_computer", "individual_costs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.test_should_compute_no_costs_from_empty_commutes", "name": "test_should_compute_no_costs_from_empty_commutes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_cost_computer.TestCostComputation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_cost_computer.TestCostComputation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_cost_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adimensional": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.adimensional", "kind": "Gdef"}, "assert_costs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["costs", "expected_costs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_cost_computer.assert_costs", "name": "assert_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["costs", "expected_costs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.cost.Costs"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.cost.Costs"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "assert_costs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cost_computer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.workers.test_cost_computer.cost_computer", "name": "cost_computer", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_cost_computer.cost_computer", "name": "cost_computer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cost_computer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "euros": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.euros", "kind": "Gdef"}, "mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "trip": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.trip", "kind": "Gdef"}, "yearly": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.yearly", "kind": "Gdef"}}, "path": "mobility\\tests\\workers\\test_cost_computer.py"}
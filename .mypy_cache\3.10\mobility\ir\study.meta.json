{"data_mtime": 1753176773, "dep_lines": [5, 6, 7, 8, 16, 17, 18, 19, 20, 21, 22, 4, 23, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.mode_constraints", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.territory", "mobility.ir.transport", "mobility.constants", "mobility.quantity", "dataclasses", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.funky", "mobility.ir.country", "typing_extensions"], "hash": "3cdece21451949f7a7860723547581c60eecd3e1", "id": "mobility.ir.study", "ignore_all": false, "interface_hash": "f1b0a5c82008b8a983fb3a5e0a5ccd34e00565b4", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\study.py", "plugin_data": null, "size": 17391, "suppressed": [], "version_id": "1.16.1"}
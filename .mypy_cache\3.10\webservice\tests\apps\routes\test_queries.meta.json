{"data_mtime": 1752154451, "dep_lines": [7, 2, 6, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["webservice.apps.routes.queries", "unittest.mock", "webservice.apps", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "abc", "flask", "flask.app", "flask.helpers", "flask.testing", "flask.wrappers", "mobility", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "mobility.use_cases.compute_query_stat", "webservice.apps.routes", "werkzeug", "werkzeug.test", "werkzeug.wrappers"], "hash": "f25777e5dc0011b05ef876b50315844059a39748", "id": "webservice.tests.apps.routes.test_queries", "ignore_all": false, "interface_hash": "4241e46fae141ad7a2b682daf64faaec025f8489", "mtime": 1722327437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\tests\\apps\\routes\\test_queries.py", "plugin_data": null, "size": 5387, "suppressed": [], "version_id": "1.16.1"}
from unittest import mock

import pytest

from mobility.converters.indicators.carpool import (
    build_carpooler_commutes,
    compute_carpool_indicators,
    compute_carpooling_clusters_costs,
    compute_carpooling_costs,
    compute_clusters_by_site,
    compute_ride_share_ratio,
    compute_site_to_car_users_mapping,
)
from mobility.funky import ImmutableDict, UnorderedList
from mobility.ir.cost import IndividualCosts
from mobility.ir.transport import CommuterProfile, TransportMode
from mobility.quantity import car_parking_spot, gramEC, seconds, trip


class TestComputeCarpoolIndicators:
    @mock.patch("mobility.converters.indicators.carpool.compute_clusters_by_site")
    def test_should_compute_carpool_indicators_from_single_site_scenario(
        self,
        mock_compute_clusters,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        geo_site_factory,
        geo_employee_factory,
        modal_commute_data_factory,
        new_carpool_indicators,
        carpooling_clusters,
        carpooling_cluster,
    ):
        cataroux = geo_site_factory(id=0)
        alfred = geo_employee_factory(id=0)
        beatrice = geo_employee_factory(id=1)
        clement = geo_employee_factory(id=2)
        damien = geo_employee_factory(id=3)
        eugenie = geo_employee_factory(id=4)
        fernande = geo_employee_factory(id=5)
        by_car = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 700}),
            emission=ImmutableDict({TransportMode.CAR: 100}),
        )
        by_bike = modal_commute_data_factory(best_mode=TransportMode.BICYCLE)
        by_foot = modal_commute_data_factory(best_mode=TransportMode.WALK)
        by_tc = modal_commute_data_factory(
            best_mode=TransportMode.PUBLIC_TRANSPORT,
            emission=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 1000}),
        )
        scenario = consolidated_scenario_factory(
            commutes={
                consolidated_commute_factory(
                    employee=alfred, site=cataroux, data=by_car
                ),
                consolidated_commute_factory(
                    employee=beatrice, site=cataroux, data=by_bike
                ),
                consolidated_commute_factory(
                    employee=clement, site=cataroux, data=by_foot
                ),
                consolidated_commute_factory(
                    employee=damien, site=cataroux, data=by_car
                ),
                consolidated_commute_factory(
                    employee=eugenie, site=cataroux, data=by_car
                ),
                consolidated_commute_factory(
                    employee=fernande, site=cataroux, data=by_tc
                ),
            }
        )
        cluster_alfred = carpooling_cluster(
            members=[alfred],
            site=cataroux,
            distances=[1000],
            emissions=[2000],
            durations=[3000],
            direct_distances=[1000],
            direct_emissions=[2000],
            direct_durations=[3000],
        )
        cluster_damien = carpooling_cluster(
            members=[damien, eugenie],
            site=cataroux,
            distances=[1000, 500],
            emissions=[2000, 500],
            durations=[2500, 1000],
            direct_distances=[1200, 500],
            direct_emissions=[2200, 500],
            direct_durations=[3200, 1000],
        )
        mock_compute_clusters.return_value = {
            cataroux: carpooling_clusters(groups=[cluster_alfred, cluster_damien])
        }

        indicators = compute_carpool_indicators(scenario)

        pt_emissions = 1000 * gramEC / trip
        single_driver_emission = 100 * gramEC / trip
        carpool_group_emission = (2500 * gramEC / 2200) * 100 / trip
        expected_carbon_emissions = (
            pt_emissions + single_driver_emission + carpool_group_emission
        )
        assert indicators == new_carpool_indicators(
            carbon_emission=expected_carbon_emissions,
            nb_car_parking_saved=1 * car_parking_spot,
            carpool_groups=UnorderedList([[alfred], [damien, eugenie]]),
            clusters=UnorderedList([cluster_alfred, cluster_damien]),
            not_clustered=UnorderedList([beatrice, clement, fernande]),
            drivers=UnorderedList([alfred, damien]),
            passengers=[eugenie],
            carpool_group_id_by_employee=ImmutableDict(
                {
                    alfred: 0,
                    damien: 1,
                    eugenie: 1,
                }
            ),
            costs=indicators.costs,
            zfe_impact_calendar=indicators.zfe_impact_calendar,
            average_travel_time=(1600 + 1200 + 3200 + 700 + (3500 / 3200) * 700 + 700)
            * seconds
            / 6,
            employees_count_per_mode=ImmutableDict(
                {
                    TransportMode.CAR: 0,
                    TransportMode.WALK: 1,
                    TransportMode.PUBLIC_TRANSPORT: 1,
                    TransportMode.BICYCLE: 1,
                    TransportMode.CARPOOLING: 3,
                    TransportMode.ELECTRIC_BICYCLE: 0,
                    TransportMode.ELECTRIC_CAR: 0,
                    TransportMode.MOTORCYCLE: 0,
                    TransportMode.AIRPLANE: 0,
                    TransportMode.ELECTRIC_MOTORCYCLE: 0,
                    TransportMode.FAST_BICYCLE: 0,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
                }
            ),
            employees_count_per_profile=ImmutableDict(
                {
                    CommuterProfile.WALKER: 1,
                    CommuterProfile.CYCLIST: 1,
                    CommuterProfile.PT_USER: 1,
                    CommuterProfile.CARPOOLER: 3,
                    CommuterProfile.COWORKER: 0,
                    CommuterProfile.REMOTE_WORKER: 0,
                }
            ),
        )
        assert set(indicators.costs.cost_by_employee) == {
            alfred,
            beatrice,
            clement,
            damien,
            eugenie,
            fernande,
        }

    @mock.patch("mobility.converters.indicators.carpool.TravelTimeApiMaker")
    @mock.patch("mobility.converters.indicators.carpool.DetourCarpoolComputer")
    def test_should_compute_clusters_for_each_site(
        self,
        mock_computer,
        mock_maker,
        geo_site_factory,
        geo_employee_factory,
        carpooling_clusters,
    ):
        mock_computer().compute_carpooling_clusters.return_value = carpooling_clusters()
        cataroux = geo_site_factory(id=0)
        crolles = geo_site_factory(id=1)
        milenis = geo_site_factory(id=2)
        alfred = geo_employee_factory(id=0)
        beatrice = geo_employee_factory(id=1)
        clement = geo_employee_factory(id=2)
        damien = geo_employee_factory(id=3)
        eugenie = geo_employee_factory(id=4)
        fernande = geo_employee_factory(id=5)
        site_mapping = {
            cataroux: [beatrice, clement, damien, eugenie, fernande],
            crolles: [alfred],
            milenis: [],
        }

        clusters = compute_clusters_by_site(site_mapping)

        assert clusters == {
            cataroux: carpooling_clusters(),
            crolles: carpooling_clusters(),
            milenis: carpooling_clusters(),
        }
        detour_computer = mock_maker().make.return_value.compute_detours
        calls = [
            mock.call(
                [beatrice, clement, damien, eugenie, fernande],
                cataroux,
                detour_computer,
                min_carpooling_group_size=3,
            ),
            mock.call(
                [alfred],
                crolles,
                detour_computer,
                min_carpooling_group_size=3,
            ),
            mock.call(
                [],
                milenis,
                detour_computer,
                min_carpooling_group_size=3,
            ),
        ]
        mock_computer.assert_has_calls(calls, any_order=True)

    def test_should_extract_mapping_from_site_to_car_users(
        self,
        consolidated_scenario_factory,
        geo_site_factory,
        geo_employee_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
    ):
        cataroux = geo_site_factory(id=0)
        crolles = geo_site_factory(id=1)
        milenis = geo_site_factory(id=2)
        alfred = geo_employee_factory(id=0)
        beatrice = geo_employee_factory(id=1)
        clement = geo_employee_factory(id=2)
        damien = geo_employee_factory(id=3)
        eugenie = geo_employee_factory(id=4)
        fernande = geo_employee_factory(id=5)
        by_car = modal_commute_data_factory(best_mode=TransportMode.CAR)
        by_bike = modal_commute_data_factory(best_mode=TransportMode.BICYCLE)
        by_foot = modal_commute_data_factory(best_mode=TransportMode.WALK)
        scenario = consolidated_scenario_factory(
            commutes={
                consolidated_commute_factory(
                    employee=alfred, site=crolles, data=by_car
                ),
                consolidated_commute_factory(
                    employee=beatrice, site=crolles, data=by_bike
                ),
                consolidated_commute_factory(
                    employee=clement, site=cataroux, data=by_foot
                ),
                consolidated_commute_factory(
                    employee=damien, site=cataroux, data=by_car
                ),
                consolidated_commute_factory(
                    employee=eugenie, site=cataroux, data=by_car
                ),
                consolidated_commute_factory(
                    employee=fernande, site=milenis, data=by_foot
                ),
            }
        )

        mapping = compute_site_to_car_users_mapping(scenario)

        assert mapping == {
            cataroux: UnorderedList([damien, eugenie]),
            crolles: [alfred],
            milenis: [],
        }


class TestComputeRideShareRatio:
    def test_should_return_empty_liste_when_no_commutes(self):
        commutes = []

        ratios = compute_ride_share_ratio(commutes)

        assert ratios == []

    def test_should_return_one_when_one_commute(
        self,
        consolidated_commute_factory,
        geo_employee,
        geo_site,
        modal_commute_data_factory,
    ):
        distances = [10000]

        ratios = compute_ride_share_ratio(distances)

        assert ratios == [1]

    def test_should_return_carpooler_ride_share_ratio(
        self,
        consolidated_commute_factory,
        geo_employee,
        geo_site,
        modal_commute_data_factory,
    ):
        distances = [5000, 0, 13000, 2000]

        ratios = compute_ride_share_ratio(distances)

        ratio_first_carpooler = 5000 / 1 / 20000
        ratio_second_carpooler = 0 / 2 / 20000
        ratio_third_carpooler = 13000 / 3 / 20000
        ratio_fourth_carpooler = 2000 / 4 / 20000
        assert ratios == pytest.approx(
            [
                ratio_first_carpooler
                + ratio_second_carpooler
                + ratio_third_carpooler
                + ratio_fourth_carpooler,
                ratio_second_carpooler + ratio_third_carpooler + ratio_fourth_carpooler,
                ratio_third_carpooler + ratio_fourth_carpooler,
                ratio_fourth_carpooler,
            ]
        )

    def test_should_return_share_ratio_one_when_distances_are_null(
        self,
        consolidated_commute_factory,
        geo_employee,
        geo_site,
        modal_commute_data_factory,
    ):
        distances = [0, 0, 0, 0]

        ratios = compute_ride_share_ratio(distances)

        assert ratios == pytest.approx([1.0, 1.0, 1.0, 1.0])


class TestBuildCarpoolerCommutes:
    def test_should_return_empty_list_when_no_employee_in_carpool_cluster(
        self, carpooling_cluster
    ):
        cluster = carpooling_cluster(members=[])

        commutes = build_carpooler_commutes(cluster, {})

        assert commutes == []

    def test_should_return_list_of_carpooler_commutes(
        self,
        carpooling_cluster,
        geo_employee_factory,
        geo_site,
        consolidated_commute_factory,
        modal_commute_data_factory,
    ):
        isabelle = geo_employee_factory(id=3)
        bob = geo_employee_factory(id=1)
        jean = geo_employee_factory(id=2)
        laser = geo_site
        durations = [10, 5, 30]
        emissions = [5, 2, 10]
        distances = [20, 10, 60]
        direct_durations = [40, 30, 30]
        direct_emissions = [15, 10, 10]
        direct_distances = [80, 60, 60]
        cluster = carpooling_cluster(
            [isabelle, bob, jean],
            laser,
            durations,
            emissions,
            distances,
            direct_durations,
            direct_emissions,
            direct_distances,
        )
        commute_data_by_employee = {
            isabelle: modal_commute_data_factory(
                duration=ImmutableDict({TransportMode.CAR: 100}),
                emission=ImmutableDict({TransportMode.CAR: 10}),
                distance=ImmutableDict({TransportMode.CAR: 200}),
            ),
            bob: modal_commute_data_factory(
                duration=ImmutableDict({TransportMode.CAR: 50}),
                emission=ImmutableDict({TransportMode.CAR: 5}),
                distance=ImmutableDict({TransportMode.CAR: 120}),
            ),
            jean: modal_commute_data_factory(
                duration=ImmutableDict({TransportMode.CAR: 25}),
                emission=ImmutableDict({TransportMode.CAR: 3}),
                distance=ImmutableDict({TransportMode.CAR: 60}),
            ),
        }

        commutes = build_carpooler_commutes(cluster, commute_data_by_employee)

        assert commutes == [
            consolidated_commute_factory(
                employee=isabelle,
                site=laser,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration=ImmutableDict({TransportMode.CAR: 112}),
                    emission=ImmutableDict({TransportMode.CAR: 11}),
                    distance=ImmutableDict({TransportMode.CAR: 225}),
                    alternative_arrival_time=ImmutableDict(),
                ),
            ),
            consolidated_commute_factory(
                employee=bob,
                site=laser,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration=ImmutableDict({TransportMode.CAR: 58}),
                    emission=ImmutableDict({TransportMode.CAR: 6}),
                    distance=ImmutableDict({TransportMode.CAR: 140}),
                    alternative_arrival_time=ImmutableDict(),
                ),
            ),
            consolidated_commute_factory(
                employee=jean,
                site=laser,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration=ImmutableDict({TransportMode.CAR: 25}),
                    emission=ImmutableDict({TransportMode.CAR: 3}),
                    distance=ImmutableDict({TransportMode.CAR: 60}),
                    alternative_arrival_time=ImmutableDict(),
                ),
            ),
        ]


class TestComputeCarpoolingCosts:
    def test_should_compute_no_cost_if_empty_carpooling_cluster(
        self, carpooling_cluster, individual_costs
    ) -> None:
        cluster = carpooling_cluster()

        costs = compute_carpooling_costs(cluster, {})

        assert costs == individual_costs()

    def test_should_compute_group_costs_if_one_carpooling_group(
        self,
        carpooling_cluster,
        geo_employee_factory,
        geo_site,
        consolidated_commute_factory,
        modal_commute_data_factory,
        individual_costs,
    ) -> None:
        isabelle = geo_employee_factory(id=3)
        bob = geo_employee_factory(id=1)
        jean = geo_employee_factory(id=2)
        laser = geo_site
        durations = [10, 5, 30]
        emissions = [5, 2, 10]
        distances = [20, 10, 60]
        direct_durations = [40, 30, 30]
        direct_emissions = [15, 10, 10]
        direct_distances = [80, 60, 60]
        cluster = carpooling_cluster(
            [isabelle, bob, jean],
            laser,
            durations,
            emissions,
            distances,
            direct_durations,
            direct_emissions,
            direct_distances,
        )
        commute_data_by_employee = {
            isabelle: modal_commute_data_factory(),
            bob: modal_commute_data_factory(),
            jean: modal_commute_data_factory(),
        }

        costs = compute_carpooling_costs(cluster, commute_data_by_employee)

        assert isinstance(costs, IndividualCosts)
        assert all(
            employee in costs.cost_by_employee for employee in [isabelle, bob, jean]
        )

    def test_should_compute_no_cost_if_no_carpooling_cluster(
        self, carpooling_cluster, individual_costs
    ) -> None:
        costs = compute_carpooling_clusters_costs([], {})

        assert costs == individual_costs()

    def test_should_compute_groups_costs_if_several_carpooling_group(
        self,
        carpooling_cluster,
        geo_employee_factory,
        geo_site_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
        individual_costs,
    ) -> None:
        isabelle = geo_employee_factory(id=3)
        bob = geo_employee_factory(id=1)
        jean = geo_employee_factory(id=2)
        mathilde = geo_employee_factory(id=4)
        gedeon = geo_employee_factory(id=5)
        baptiste = geo_employee_factory(id=6)
        bernadette = geo_employee_factory(id=7)
        laser = geo_site_factory(id=1)
        monolithe = geo_site_factory(id=1)
        durations = [10, 5, 30]
        emissions = [5, 2, 10]
        distances = [20, 10, 60]
        direct_durations = [40, 30, 30]
        direct_emissions = [15, 10, 10]
        direct_distances = [80, 60, 60]
        cluster_zaza = carpooling_cluster(
            [isabelle, bob, jean],
            laser,
            durations,
            emissions,
            distances,
            direct_durations,
            direct_emissions,
            direct_distances,
        )
        cluster_gege = carpooling_cluster(
            [gedeon, baptiste, bernadette, mathilde],
            monolithe,
            [4] * 4,
            [2] * 4,
            [3] * 4,
            [4] * 4,
            [2] * 4,
            [3] * 4,
        )
        commute_data_by_employee = {
            isabelle: modal_commute_data_factory(),
            bob: modal_commute_data_factory(),
            jean: modal_commute_data_factory(),
            mathilde: modal_commute_data_factory(),
            gedeon: modal_commute_data_factory(),
            baptiste: modal_commute_data_factory(),
            bernadette: modal_commute_data_factory(),
        }

        costs = compute_carpooling_clusters_costs(
            [cluster_zaza, cluster_gege], commute_data_by_employee
        )

        assert isinstance(costs, IndividualCosts)
        assert all(
            employee in costs.cost_by_employee
            for employee in [
                isabelle,
                bob,
                jean,
                gedeon,
                baptiste,
                bernadette,
                mathilde,
            ]
        )

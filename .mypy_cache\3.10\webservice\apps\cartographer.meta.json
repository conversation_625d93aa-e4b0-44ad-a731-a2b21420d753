{"data_mtime": 1752154444, "dep_lines": [8, 6, 7, 14, 15, 16, 18, 5, 17, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.ir.webstudy", "webservice.apps.old_names", "mobility.funky", "mobility.quantity", "dataclasses", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.commute_data", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.site", "types", "typing_extensions"], "hash": "c3883d15a7442b8b09e4d76593d4d2c2953229f7", "id": "webservice.apps.cartographer", "ignore_all": false, "interface_hash": "0671d7f5c049001f7b9789d588d52f815a6a5b8a", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\cartographer.py", "plugin_data": null, "size": 15730, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "api_abstraction.here.here_api_clients", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "BaseGeometry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.here.here_api_clients.BaseGeometry", "name": "BaseGeometry", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.BaseGeometry", "source_any": null, "type_of_any": 3}}}, "BaseHereAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.base_here_api.BaseHereAPI", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HereIntermodalAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mobility.ir.here.HereIntermodalRequestParameters", "api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "api_abstraction.here.base_here_api.BaseHereAPI"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI", "name": "HereIntermodalAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.here.here_api_clients", "mro": ["api_abstraction.here.here_api_clients.HereIntermodalAPI", "api_abstraction.here.base_here_api.BaseHereAPI", "builtins.object"], "names": {".class": "SymbolTable", "_format_input_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI._format_input_parameters", "name": "_format_input_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereIntermodalAPI", "mobility.ir.here.HereIntermodalRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_input_parameters of HereIntermodalAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI._format_result", "name": "_format_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["api_abstraction.here.here_api_clients.HereIntermodalAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_result of HereIntermodalAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereIntermodalAPI", "mobility.ir.here.HereIntermodalRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of HereIntermodalAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI.display_name", "name": "display_name", "setter_type": null, "type": "builtins.str"}}, "root_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI.root_url", "name": "root_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.here.here_api_clients.HereIntermodalAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.here.here_api_clients.HereIntermodalAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HereIntermodalRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIntermodalRequestParameters", "kind": "Gdef"}, "HereIsolineAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mobility.ir.here.HereIsolineRequestParameters", {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.BaseGeometry", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "api_abstraction.here.base_here_api.BaseHereAPI"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI", "name": "HereIsolineAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.here.here_api_clients", "mro": ["api_abstraction.here.here_api_clients.HereIsolineAPI", "api_abstraction.here.base_here_api.BaseHereAPI", "builtins.object"], "names": {".class": "SymbolTable", "_format_input_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI._format_input_parameters", "name": "_format_input_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereIsolineAPI", "mobility.ir.here.HereIsolineRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_input_parameters of HereIsolineAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI._format_result", "name": "_format_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["api_abstraction.here.here_api_clients.HereIsolineAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_result of HereIsolineAPI", "ret_type": {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.BaseGeometry", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_isoline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI.compute_isoline", "name": "compute_isoline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereIsolineAPI", "mobility.ir.here.HereIsolineRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isoline of HereIsolineAPI", "ret_type": {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.BaseGeometry", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI.display_name", "name": "display_name", "setter_type": null, "type": "builtins.str"}}, "root_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI.root_url", "name": "root_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.here.here_api_clients.HereIsolineAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.here.here_api_clients.HereIsolineAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HereIsolineRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIsolineRequestParameters", "kind": "Gdef"}, "HerePublicTransitAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mobility.ir.here.HerePublicTransitRequestParameters", "api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "api_abstraction.here.base_here_api.BaseHereAPI"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI", "name": "HerePublicTransitAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.here.here_api_clients", "mro": ["api_abstraction.here.here_api_clients.HerePublicTransitAPI", "api_abstraction.here.base_here_api.BaseHereAPI", "builtins.object"], "names": {".class": "SymbolTable", "_format_input_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI._format_input_parameters", "name": "_format_input_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HerePublicTransitAPI", "mobility.ir.here.HerePublicTransitRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_input_parameters of HerePublicTransitAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI._format_result", "name": "_format_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["api_abstraction.here.here_api_clients.HerePublicTransitAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_result of HerePublicTransitAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HerePublicTransitAPI", "mobility.ir.here.HerePublicTransitRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of HerePublicTransitAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI.display_name", "name": "display_name", "setter_type": null, "type": "builtins.str"}}, "root_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI.root_url", "name": "root_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.here.here_api_clients.HerePublicTransitAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.here.here_api_clients.HerePublicTransitAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HerePublicTransitRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HerePublicTransitRequestParameters", "kind": "Gdef"}, "HereRoutingAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mobility.ir.here.HereRoutingRequestParameters", "api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "api_abstraction.here.base_here_api.BaseHereAPI"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI", "name": "HereRoutingAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.here.here_api_clients", "mro": ["api_abstraction.here.here_api_clients.HereRoutingAPI", "api_abstraction.here.base_here_api.BaseHereAPI", "builtins.object"], "names": {".class": "SymbolTable", "_format_input_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI._format_input_parameters", "name": "_format_input_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereRoutingAPI", "mobility.ir.here.HereRoutingRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_input_parameters of HereRoutingAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI._format_result", "name": "_format_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["api_abstraction.here.here_api_clients.HereRoutingAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_result of HereRoutingAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.here.here_api_clients.HereRoutingAPI", "mobility.ir.here.HereRoutingRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of HereRoutingAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI.display_name", "name": "display_name", "setter_type": null, "type": "builtins.str"}}, "root_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI.root_url", "name": "root_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.here.here_api_clients.HereRoutingAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.here.here_api_clients.HereRoutingAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HereRoutingRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereRoutingRequestParameters", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.here.here_api_clients.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_calculate_total_duration_from_timestamps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sections"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients._calculate_total_duration_from_timestamps", "name": "_calculate_total_duration_from_timestamps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sections"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_total_duration_from_timestamps", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_route_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sections"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients._extract_route_distance", "name": "_extract_route_distance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sections"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_route_distance", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_here_routes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients._validate_here_routes", "name": "_validate_here_routes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["result"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_here_routes", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_here_result_to_journey_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.convert_here_result_to_journey_attribute", "name": "convert_here_result_to_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["result"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_here_result_to_journey_attribute", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "flexpolyline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.here.here_api_clients.flexpolyline", "name": "flexpolyline", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.flexpolyline", "source_any": null, "type_of_any": 3}}}, "get_max_transfers_in_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.here.here_api_clients.get_max_transfers_in_range", "name": "get_max_transfers_in_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["max_transfers"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_max_transfers_in_range", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "shape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.here.here_api_clients.shape", "name": "shape", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.here.here_api_clients.shape", "source_any": null, "type_of_any": 3}}}}, "path": "api_abstraction\\here\\here_api_clients.py"}
{"data_mtime": 1751444410, "dep_lines": [5, 11, 13, 14, 15, 25, 26, 27, 12, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.travel_time_api", "api_abstraction.trivial.trivial_travel_time", "mobility.ir.commute_data", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.repositories.abstract_repositories", "mobility.funky", "logging", "itertools", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.trivial", "enum", "functools", "mobility.ir", "mobility.ir.employee", "mobility.ir.infrastructure", "mobility.ir.mode_constraints", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.quantity", "mobility.repositories", "types", "typing_extensions"], "hash": "5510e994d1319171341683ff40d3207c9aa5683c", "id": "mobility.workers.travel_timer", "ignore_all": false, "interface_hash": "7fb7eb860a8a6c684cce2c7752b39d4cfb5609c5", "mtime": 1751895952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\travel_timer.py", "plugin_data": null, "size": 17567, "suppressed": [], "version_id": "1.16.1"}
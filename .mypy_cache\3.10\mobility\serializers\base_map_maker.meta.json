{"data_mtime": 1751444473, "dep_lines": [18, 3, 4, 5, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.serializers.charters.svg_map_addons", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.ir.map_elements", "mobility.serializers.chart_writer", "mobility.workers.color_picker", "typing", "builtins", "_frozen_importlib", "abc", "enum", "mobility.ir", "mobility.serializers.charters", "mobility.workers"], "hash": "bdc466ef68e8b46a5015ef0f795d6eb469632ff3", "id": "mobility.serializers.base_map_maker", "ignore_all": false, "interface_hash": "470b9b52327bd1c8dab325ced0409b317b8a8e29", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\base_map_maker.py", "plugin_data": null, "size": 5933, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444403, "dep_lines": [3, 4, 5, 7, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.geo_study", "mobility.ir.journey", "mobility.ir.transport", "mobility.store.database", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "mobility.ir", "mobility.store", "typing_extensions"], "hash": "49f0e1c70b427ca62bd42e0aca32def1c6e7fe42", "id": "mobility.repositories.journey", "ignore_all": false, "interface_hash": "6cf2b235a113d71080f8e92dbd0198bb20ca1f5c", "mtime": 1722327417, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\journey.py", "plugin_data": null, "size": 2911, "suppressed": [], "version_id": "1.16.1"}
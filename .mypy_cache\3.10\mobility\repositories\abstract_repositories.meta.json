{"data_mtime": 1753176774, "dep_lines": [19, 6, 7, 8, 9, 10, 20, 26, 27, 28, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "mobility.ir.company_potential", "mobility.ir.geo_study", "mobility.ir.gtfs", "mobility.ir.mode_shift_readiness_classification", "mobility.ir.onboarding_form_step", "mobility.ir.query_record", "mobility.ir.zfe", "datetime", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "enum", "mobility.ir", "mobility.ir.indicators", "types"], "hash": "2aa7fb210856516730e400c4f9ed53893432fb2b", "id": "mobility.repositories.abstract_repositories", "ignore_all": false, "interface_hash": "ad9d9e2a25cc34f56617dae51cc312f0f88f170e", "mtime": 1730103305, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\abstract_repositories.py", "plugin_data": null, "size": 5207, "suppressed": [], "version_id": "1.16.1"}
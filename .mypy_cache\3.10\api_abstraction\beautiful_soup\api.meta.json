{"data_mtime": 1753176775, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 15, 14, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.geopy.ban_france", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.quantity", "typing", "requests", "bs4", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "api_abstraction.api.geocode_api", "api_abstraction.geopy", "bs4.builder", "bs4.element", "enum", "functools", "http", "http.cookiejar", "mobility", "mobility.ir", "mobility.workers", "re", "requests.api", "requests.auth", "requests.models", "types", "typing_extensions"], "hash": "ac773cdbd503294fc358962a29f9c8cb993e2b2a", "id": "api_abstraction.beautiful_soup.api", "ignore_all": false, "interface_hash": "524f5b9a34a11a7143cf8260e36cf4c88ca129fd", "mtime": 1751895952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\beautiful_soup\\api.py", "plugin_data": null, "size": 9367, "suppressed": ["regex"], "version_id": "1.16.1"}
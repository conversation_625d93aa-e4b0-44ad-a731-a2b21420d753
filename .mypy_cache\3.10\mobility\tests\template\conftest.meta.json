{"data_mtime": 1752154451, "dep_lines": [9, 10, 11, 12, 13, 14, 15, 16, 17, 2, 8, 1, 2, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.map_elements", "mobility.ir.study", "mobility.ir.table_of_contents", "mobility.serializers.chart_tracer", "mobility.serializers.illustrator", "mobility.serializers.phraser", "mobility.serializers.relative_templates", "mobility.serializers.report_quantity_formatters", "unittest.mock", "mobility.enumerations", "typing", "unittest", "pytest", "jinja2", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "jinja2.bccache", "jinja2.environment", "jinja2.ext", "jinja2.loaders", "jinja2.runtime", "mobility.ir", "mobility.ir.indicators", "mobility.serializers", "typing_extensions"], "hash": "070257e11211ddfa25d083240d3c5f7a03374777", "id": "mobility.tests.template.conftest", "ignore_all": false, "interface_hash": "fb4521f66c41edbaee1498a14ed693e0c1c9e8f9", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\template\\conftest.py", "plugin_data": null, "size": 3758, "suppressed": ["tidylib"], "version_id": "1.16.1"}
{"data_mtime": 1753176775, "dep_lines": [9, 10, 11, 12, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 6, 5], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.travel_time_api", "api_abstraction.here.base_here_api", "mobility.ir.here", "logging", "datetime", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "enum", "mobility", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.transport", "types", "typing_extensions"], "hash": "3d5dea50508a3c31bf775bc9addc0af35773d500", "id": "api_abstraction.here.here_api_clients", "ignore_all": false, "interface_hash": "942ea700ee35af588e299fce0cb1976b8a45083b", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\here\\here_api_clients.py", "plugin_data": null, "size": 10441, "suppressed": ["shapely.geometry.base", "shapely.geometry", "flexpolyline"], "version_id": "1.16.1"}
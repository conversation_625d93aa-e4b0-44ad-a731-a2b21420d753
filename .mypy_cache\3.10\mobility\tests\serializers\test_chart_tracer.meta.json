{"data_mtime": 1752154451, "dep_lines": [4, 5, 6, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.study", "mobility.serializers.chart_tracer", "mobility.serializers.illustrator", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "abc", "mobility.ir", "mobility.ir.indicators", "mobility.ir.map_elements", "mobility.serializers", "types", "typing", "typing_extensions"], "hash": "6dc21c1eca01fee7d5fa4e35ba36ece0f4ee9d63", "id": "mobility.tests.serializers.test_chart_tracer", "ignore_all": false, "interface_hash": "7eec8cd2c16c6379d4eecf378fd07c526407bae2", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\serializers\\test_chart_tracer.py", "plugin_data": null, "size": 4046, "suppressed": ["shapely.geometry"], "version_id": "1.16.1"}
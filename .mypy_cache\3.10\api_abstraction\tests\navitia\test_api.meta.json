{"data_mtime": 1751444468, "dep_lines": [6, 7, 8, 9, 10, 11, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.navitia.api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "datetime", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "abc", "api_abstraction.api", "api_abstraction.api.travel_time_api", "api_abstraction.navitia", "contextlib", "enum", "functools", "mobility", "mobility.ir", "mobility.ir.country", "re", "typing_extensions"], "hash": "6621dfb016db1115049457d71074a26e4d1cda10", "id": "api_abstraction.tests.navitia.test_api", "ignore_all": false, "interface_hash": "1eca2b7b65f3e064759b02af90232305900f6aaf", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\navitia\\test_api.py", "plugin_data": null, "size": 13289, "suppressed": [], "version_id": "1.16.1"}
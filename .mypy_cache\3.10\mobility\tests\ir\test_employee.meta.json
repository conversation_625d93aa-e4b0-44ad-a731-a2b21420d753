{"data_mtime": 1751444403, "dep_lines": [1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.crit_air", "mobility.ir.employee", "mobility.ir.error", "mobility.ir.geo_study", "mobility.ir.transport", "builtins", "_frozen_importlib", "abc", "enum", "mobility.ir", "typing", "typing_extensions"], "hash": "ae3823a5ec01c672105f9b09bf3a03cb6c524772", "id": "mobility.tests.ir.test_employee", "ignore_all": false, "interface_hash": "25facf8abebffea857239a9c295725529618cf68", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\test_employee.py", "plugin_data": null, "size": 4156, "suppressed": [], "version_id": "1.16.1"}
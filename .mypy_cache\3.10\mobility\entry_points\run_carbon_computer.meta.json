{"data_mtime": 1752154451, "dep_lines": [12, 13, 14, 15, 17, 18, 19, 20, 22, 24, 25, 26, 27, 28, 33, 21, 23, 3, 4, 5, 6, 7, 8, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "mobility.builders.csv_multi_reader", "mobility.builders.excel_reader", "mobility.builders.exceptions", "mobility.builders.json_builder", "mobility.ir.study", "mobility.workers.emission_computer", "mobility.workers.geocoder", "mobility.workers.territory_computer", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "mobility.workers.travel_timer", "mobility.interface", "mobility.serializers", "<PERSON><PERSON><PERSON><PERSON>", "logging", "os", "sys", "datetime", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "configparser", "enum", "mobility.builders", "mobility.funky", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.transport", "mobility.serializers.json_serializer", "mobility.workers", "types", "typing_extensions"], "hash": "3b4c1ac80985c3fd0decb933b372c90d67dd25f7", "id": "mobility.entry_points.run_carbon_computer", "ignore_all": false, "interface_hash": "e55ac1615adb4955b7cac4210e5a1785262c1d08", "mtime": 1750067533, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\entry_points\\run_carbon_computer.py", "plugin_data": null, "size": 11017, "suppressed": ["pandas"], "version_id": "1.16.1"}
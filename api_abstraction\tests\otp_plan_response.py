tc_response = {
    "requestParameters": {
        "date": "2020-09-29",
        "mode": "TRANSIT,WALK",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.7540,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "20:15:00",
    },
    "plan": {
        "date": 1601403300000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 889,
                "startTime": 1601401431000,
                "endTime": 1601402320000,
                "walkTime": 287,
                "transitTime": 600,
                "waitingTime": 2,
                "walkDistance": 311.6281071427545,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1601401431000,
                        "endTime": 1601401499000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 73.857,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1601401431000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Marcel Michelin",
                            "stopId": "1:StopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601401499000,
                            "departure": 1601401500000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "{igvGue_RPd@\\~@Vd@FJ",
                            "length": 5,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 68.0,
                        "steps": [
                            {
                                "distance": 73.857,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140379124761624,
                                "lat": 45.75406510317528,
                                "elevation": [],
                            }
                        ],
                    },
                    {
                        "startTime": 1601401500000,
                        "endTime": 1601402100000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3302.916316523103,
                        "pathway": False,
                        "mode": "BUS",
                        "route": "3",
                        "agencyName": "T2C (Clermont-Ferrand)",
                        "agencyUrl": "http://navitia2-eng.FR-AUV.prod.canaltp.fr/",
                        "agencyTimeZoneOffset": 7200000,
                        "routeColor": "ED6E00",
                        "routeType": 3,
                        "routeId": "1:T2C11821953316814882",
                        "routeTextColor": "FFFFFF",
                        "interlineWithPreviousLeg": False,
                        "headsign": "Stade G. Montpied",
                        "agencyId": "T2C",
                        "tripId": "1:T2C4503737117950476-1_9523",
                        "serviceDate": "20200929",
                        "from": {
                            "name": "Marcel Michelin",
                            "stopId": "1:StopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601401499000,
                            "departure": 1601401500000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Barrière d'Issoire",
                            "stopId": "1:StopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601402100000,
                            "departure": 1601402101000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "uggvGq`_RpGbR]rN}PzTeRjLoOnHgQxKwSrN{VfHcMd@i@fR",
                            "length": 11,
                        },
                        "routeShortName": "3",
                        "routeLongName": "Les Vignes - Gergovia",
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": True,
                        "duration": 600.0,
                        "steps": [],
                    },
                    {
                        "startTime": 1601402101000,
                        "endTime": 1601402320000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 237.747,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Barrière d'Issoire",
                            "stopId": "1:StopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601402100000,
                            "departure": 1601402101000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1601402320000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "gckvGyvzQFwA@Y?M@c@@[Mg@KSCOgCkG",
                            "length": 10,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 219.0,
                        "steps": [
                            {
                                "distance": 74.938,
                                "relativeDirection": "DEPART",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "EAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0911734370376722,
                                "lat": 45.7734841868617,
                                "elevation": [],
                            },
                            {
                                "distance": 34.676,
                                "relativeDirection": "SLIGHTLY_LEFT",
                                "streetName": "Lafayette",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0921342000000003,
                                "lat": 45.7734127,
                                "elevation": [],
                            },
                            {
                                "distance": 128.133,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0925177,
                                "lat": 45.773569900000005,
                                "elevation": [],
                            },
                        ],
                    },
                ],
                "tooSloped": False,
            },
            {
                "duration": 889,
                "startTime": 1601401431000,
                "endTime": 1601402320000,
                "walkTime": 287,
                "transitTime": 600,
                "waitingTime": 2,
                "walkDistance": 311.6281071427545,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1601401431000,
                        "endTime": 1601401499000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 73.857,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1601401431000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Marcel Michelin",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601401499000,
                            "departure": 1601401500000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "{igvGue_RPd@\\~@Vd@FJ",
                            "length": 5,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 68.0,
                        "steps": [
                            {
                                "distance": 73.857,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140379124761624,
                                "lat": 45.75406510317528,
                                "elevation": [],
                            }
                        ],
                    },
                    {
                        "startTime": 1601401500000,
                        "endTime": 1601402100000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3302.916316523103,
                        "pathway": False,
                        "mode": "BUS",
                        "route": "3",
                        "agencyName": "Clermont-Ferrand - T2C",
                        "agencyUrl": "http://navitia2-eng.FR-SE-OPEN.prod.canaltp.fr/",
                        "agencyTimeZoneOffset": 7200000,
                        "routeColor": "ED6E00",
                        "routeType": 3,
                        "routeId": "2:CLET2C11821953316814882",
                        "routeTextColor": "FFFFFF",
                        "interlineWithPreviousLeg": False,
                        "headsign": "Stade G. Montpied",
                        "agencyId": "T2C",
                        "tripId": "2:CLET2C4503737117950476-1_9523-1_285691",
                        "serviceDate": "20200929",
                        "from": {
                            "name": "Marcel Michelin",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601401499000,
                            "departure": 1601401500000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Barrière d'Issoire",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601402100000,
                            "departure": 1601402101000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "uggvGq`_RpGbR]rN}PzTeRjLoOnHgQxKwSrN{VfHcMd@i@fR",
                            "length": 11,
                        },
                        "routeShortName": "3",
                        "routeLongName": "Les Vignes - Gergovia",
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": True,
                        "duration": 600.0,
                        "steps": [],
                    },
                    {
                        "startTime": 1601402101000,
                        "endTime": 1601402320000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 237.747,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Barrière d'Issoire",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601402100000,
                            "departure": 1601402101000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1601402320000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "gckvGyvzQFwA@Y?M@c@@[Mg@KSCOgCkG",
                            "length": 10,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 219.0,
                        "steps": [
                            {
                                "distance": 74.938,
                                "relativeDirection": "DEPART",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "EAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0911734370376722,
                                "lat": 45.7734841868617,
                                "elevation": [],
                            },
                            {
                                "distance": 34.676,
                                "relativeDirection": "SLIGHTLY_LEFT",
                                "streetName": "Lafayette",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0921342000000003,
                                "lat": 45.7734127,
                                "elevation": [],
                            },
                            {
                                "distance": 128.133,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0925177,
                                "lat": 45.773569900000005,
                                "elevation": [],
                            },
                        ],
                    },
                ],
                "tooSloped": False,
            },
            {
                "duration": 949,
                "startTime": 1601400531000,
                "endTime": 1601401480000,
                "walkTime": 287,
                "transitTime": 660,
                "waitingTime": 2,
                "walkDistance": 311.6281071427545,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1601400531000,
                        "endTime": 1601400599000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 73.857,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1601400531000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Marcel Michelin",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601400599000,
                            "departure": 1601400600000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "{igvGue_RPd@\\~@Vd@FJ",
                            "length": 5,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 68.0,
                        "steps": [
                            {
                                "distance": 73.857,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140379124761624,
                                "lat": 45.75406510317528,
                                "elevation": [],
                            }
                        ],
                    },
                    {
                        "startTime": 1601400600000,
                        "endTime": 1601401260000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3302.916316523103,
                        "pathway": False,
                        "mode": "BUS",
                        "route": "3",
                        "agencyName": "Clermont-Ferrand - T2C",
                        "agencyUrl": "http://navitia2-eng.FR-SE-OPEN.prod.canaltp.fr/",
                        "agencyTimeZoneOffset": 7200000,
                        "routeColor": "ED6E00",
                        "routeType": 3,
                        "routeId": "2:CLET2C11821953316814882",
                        "routeTextColor": "FFFFFF",
                        "interlineWithPreviousLeg": False,
                        "headsign": "Les Vignes",
                        "agencyId": "T2C",
                        "tripId": "2:CLET2C4503737117950611-1_9549-1_285717",
                        "serviceDate": "20200929",
                        "from": {
                            "name": "Marcel Michelin",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495850",
                            "lon": 3.113219,
                            "lat": 45.753713,
                            "arrival": 1601400599000,
                            "departure": 1601400600000,
                            "stopIndex": 3,
                            "stopSequence": 3,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Barrière d'Issoire",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601401260000,
                            "departure": 1601401261000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "legGeometry": {
                            "points": "uggvGq`_RpGbR]rN}PzTeRjLoOnHgQxKwSrN{VfHcMd@i@fR",
                            "length": 11,
                        },
                        "routeShortName": "3",
                        "routeLongName": "Les Vignes - Gergovia",
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": True,
                        "duration": 660.0,
                        "steps": [],
                    },
                    {
                        "startTime": 1601401261000,
                        "endTime": 1601401480000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 237.747,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Barrière d'Issoire",
                            "stopId": "2:StopPoint:CLEStopPoint:T2C3377704015495245",
                            "lon": 3.091193,
                            "lat": 45.773603,
                            "arrival": 1601401260000,
                            "departure": 1601401261000,
                            "stopIndex": 13,
                            "stopSequence": 13,
                            "vertexType": "TRANSIT",
                            "boardAlightType": "DEFAULT",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1601401480000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "gckvGyvzQFwA@Y?M@c@@[Mg@KSCOgCkG",
                            "length": 10,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "transitLeg": False,
                        "duration": 219.0,
                        "steps": [
                            {
                                "distance": 74.938,
                                "relativeDirection": "DEPART",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "EAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0911734370376722,
                                "lat": 45.7734841868617,
                                "elevation": [],
                            },
                            {
                                "distance": 34.676,
                                "relativeDirection": "SLIGHTLY_LEFT",
                                "streetName": "Lafayette",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0921342000000003,
                                "lat": 45.7734127,
                                "elevation": [],
                            },
                            {
                                "distance": 128.133,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0925177,
                                "lat": 45.773569900000005,
                                "elevation": [],
                            },
                        ],
                    },
                ],
                "tooSloped": False,
            },
        ],
    },
    "debugOutput": {
        "precalculationTime": 361,
        "pathCalculationTime": 73,
        "pathTimes": [26, 20, 26],
        "renderingTime": 1,
        "totalTime": 435,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 27.044551115817065,
        "geoidElevation": False,
    },
}

walk_response = {
    "requestParameters": {
        "date": "2020-09-22",
        "mode": "WALK",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.754,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "08:30:00",
        "maxWalkDistance": "6048",
    },
    "plan": {
        "date": 1600756200000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 3038,
                "startTime": 1600753162000,
                "endTime": 1600756200000,
                "walkTime": 3038,
                "transitTime": 0,
                "waitingTime": 0,
                "walkDistance": 3253.638017855101,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1600753162000,
                        "endTime": 1600756200000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3253.159,
                        "pathway": False,
                        "mode": "WALK",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1600753162000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1600756200000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "}igvGse_RBFHTHP^v@{@n@EJ?LH`ACLeAfAd@lB@FV~@`@~@n@tAJVQPe@f@m@h@u@z@EBUP?B?D?DCBA@C?C?O`@EHGN_@zAYpAOh@I`@Yl@UROHUJWLs@Nm@NQF_@RYRQPWTeA~@sAjAkAbAe@b@cA|@_@ZAl@?VIfDAf@?x@OAC?a@p@{BfEaA~As@nASZa@b@gAhAIN?H@VSHEBGB??[L??IDE@EBQHOFE@GBgBr@GBk@TWLSLQJu@p@IFMJIHEBKJIHEBw@r@KFIHe@`@SPEDw@p@m@h@QNIHMLk@t@mBfCIJKLMLOLy@f@ULKDUFUDA?i@FUDQDK@_APK@q@Te@Ta@Tg@^MJYVURMJe@\\UPKFKDQBOAE?GAi@SSISEUCO?O@KBOD_DfAKDMDUFQFEBGBSFYLa@PYVSRORKJIFA@IHIHa@^]Zu@r@i@j@GFe@b@GDOTIDKBMBE@G?OE_B{Dm@wA",
                            "length": 186,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "duration": 3038.0,
                        "transitLeg": False,
                        "steps": [
                            {
                                "distance": 51.291,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140299194201013,
                                "lat": 45.75407631691722,
                                "elevation": [],
                            },
                            {
                                "distance": 129.165,
                                "relativeDirection": "RIGHT",
                                "streetName": "Rue Marcel Michelin",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1135054,
                                "lat": 45.7537965,
                                "elevation": [],
                            },
                            {
                                "distance": 164.29199999999997,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue du Docteur Casati",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1123714,
                                "lat": 45.7544431,
                                "elevation": [],
                            },
                            {
                                "distance": 722.6740000000002,
                                "relativeDirection": "RIGHT",
                                "streetName": "Rue Pasteur",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1105975,
                                "lat": 45.7536502,
                                "elevation": [],
                            },
                            {
                                "distance": 271.912,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue Pasteur",
                                "absoluteDirection": "WEST",
                                "stayOn": True,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1052445,
                                "lat": 45.7586823,
                                "elevation": [],
                            },
                            {
                                "distance": 204.44400000000002,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Rue des Cézeaux",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1023279,
                                "lat": 45.7596453,
                                "elevation": [],
                            },
                            {
                                "distance": 1538.1670000000006,
                                "relativeDirection": "RIGHT",
                                "streetName": "Avenue Léon Blum",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1005051,
                                "lat": 45.7609083,
                                "elevation": [],
                            },
                            {
                                "distance": 21.095,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "Avenue Léon Blum",
                                "absoluteDirection": "NORTH",
                                "stayOn": True,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0925173,
                                "lat": 45.7732114,
                                "elevation": [],
                            },
                            {
                                "distance": 150.119,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0924419,
                                "lat": 45.7733931,
                                "elevation": [],
                            },
                        ],
                    }
                ],
                "tooSloped": False,
            }
        ],
    },
    "debugOutput": {
        "precalculationTime": 0,
        "pathCalculationTime": 40,
        "pathTimes": [39],
        "renderingTime": 1,
        "totalTime": 41,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 51.02758866380542,
        "geoidElevation": False,
    },
}

car_response = {
    "requestParameters": {
        "date": "2020-09-22",
        "mode": "CAR,WALK",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.754,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "08:30:00",
        "maxWalkDistance": "6048",
    },
    "plan": {
        "date": 1600756200000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 918,
                "startTime": 1600755282000,
                "endTime": 1600756200000,
                "walkTime": 918,
                "transitTime": 0,
                "waitingTime": 0,
                "walkDistance": 0.0,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1600755282000,
                        "endTime": 1600756200000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 4023.5109999999995,
                        "pathway": False,
                        "mode": "CAR",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1600755282000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1600756200000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "}igvGse_Rm@gBGQc@iAiAcDEMEICIMU_@w@wAoCOY_@m@CGCGaAsBi@y@qBwDEIIKKOQYW_@CCg@u@Wa@aAwAGIGKc@o@aAqAc@a@MMo@e@k@_@UKWKO[QWKUMKc@bAQd@EFa@`AcA`C]n@Wb@KNA@MPEDCBq@t@{@j@}BdB}@n@SNULSPSPQPILGF?@OTOVMXMXCHGRIZI^G^E^C^C`@ARAR?LAJAZMxDIjB?PMjDGxAAh@Cj@Cl@ATCr@A^ANAV?PMvC?NANANCNCNCNCLMZMXINEHOXOVMXa@t@CDCFKNiAtBABKR_@~@S^U^GHc@n@S^KRm@`Aa@p@KNMPKLYd@e@z@_@p@QZYb@iAlBGJc@v@g@z@INMTS`@OVe@|@}AzC_AhBQ^]n@o@nAgAvBQZIPILGLcC~EgAtBIPUb@A@_@t@oAdCgBjDGPEVGh@MdDALCTAl@KTCBGNQEWEEAKC_Cg@yBi@EAYGKRCRAN?@Rd@FPnBvEFLHNHR@B",
                            "length": 191,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "duration": 918.0,
                        "transitLeg": False,
                        "steps": [
                            {
                                "distance": 47.876,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140299194201013,
                                "lat": 45.75407631691722,
                                "elevation": [],
                            },
                            {
                                "distance": 881.399,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue Roger Maerte",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1145495,
                                "lat": 45.7543086,
                                "elevation": [],
                            },
                            {
                                "distance": 9.593,
                                "relativeDirection": "CONTINUE",
                                "streetName": "route sans nom",
                                "absoluteDirection": "NORTHEAST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": True,
                                "lon": 3.1225503,
                                "lat": 45.7598163,
                                "elevation": [],
                            },
                            {
                                "distance": 55.216,
                                "relativeDirection": "LEFT",
                                "streetName": "Avenue des Landais",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1226193,
                                "lat": 45.7598879,
                                "elevation": [],
                            },
                            {
                                "distance": 615.817,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue de la Margeride",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1220444,
                                "lat": 45.7601805,
                                "elevation": [],
                            },
                            {
                                "distance": 557.18,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Avenue des Landais",
                                "absoluteDirection": "WEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1165745,
                                "lat": 45.7638429,
                                "elevation": [],
                            },
                            {
                                "distance": 1456.9430000000002,
                                "relativeDirection": "CONTINUE",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1097982,
                                "lat": 45.7650174,
                                "elevation": [],
                            },
                            {
                                "distance": 21.05,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "bretelle d'accès",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": True,
                                "lon": 3.0954261,
                                "lat": 45.773183,
                                "elevation": [],
                            },
                            {
                                "distance": 195.031,
                                "relativeDirection": "RIGHT",
                                "streetName": "Boulevard Fleury",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0952133,
                                "lat": 45.7733001,
                                "elevation": [],
                            },
                            {
                                "distance": 25.877000000000002,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue de l'Oradou",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0957674,
                                "lat": 45.7750108,
                                "elevation": [],
                            },
                            {
                                "distance": 157.529,
                                "relativeDirection": "LEFT",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0954752,
                                "lat": 45.7751023,
                                "elevation": [],
                            },
                        ],
                    }
                ],
                "tooSloped": False,
            }
        ],
    },
    "debugOutput": {
        "precalculationTime": 0,
        "pathCalculationTime": 37,
        "pathTimes": [36],
        "renderingTime": 2,
        "totalTime": 39,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 51.02758866380542,
        "geoidElevation": False,
    },
}

bicycle_response = {
    "requestParameters": {
        "date": "2020-09-22",
        "mode": "BICYCLE",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.754,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "08:30:00",
        "maxWalkDistance": "6048",
        "bikeSpeed": "4.17",
    },
    "plan": {
        "date": 1600756200000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 910,
                "startTime": 1600755290000,
                "endTime": 1600756200000,
                "walkTime": 910,
                "transitTime": 0,
                "waitingTime": 0,
                "walkDistance": 3305.9457697816524,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1600755290000,
                        "endTime": 1600756200000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3305.8080000000004,
                        "pathway": False,
                        "mode": "BICYCLE",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1600755290000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1600756200000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "}igvGse_RBFHTHP^v@P\\DHBDVd@LVf@z@b@x@FNHPHZH\\DZJn@BVIDC@OLSRe@d@aA`ASPQPe@f@m@h@u@z@EBUPACCAAAC?A?C@ABAB?D?B@B@BB@O`@EHGN_@zAYpAOh@I`@Yl@UROHUJWLs@Nm@NQF_@RYRQPWTeA~@sAjAkAbAe@b@cA|@_@Z_@?oCCgA?G?q@?E?c@@U?S@UD_@Hi@LMBQD[HK@YBW@y@AcAAq@?U?UAm@?y@Ae@AQASA{@A]?QAM?O@K?QDSFi@NYHC@g@NKDG@[HC@SFUBOAC?}@KKAQCs@Ic@?Y@k@Bc@@OBKByAFqAFK?GAEECCCEMTS`@OVe@|@}AzC_AhBQ^]n@o@nAgAvBQZIPILGLcC~EgAtBIPUb@A@_@t@oAdCgBjDGPEVGh@MdDALCTAl@KTCBGNQEWEEAKC@H?H{AlCq@hAKNHNHR@B",
                            "length": 179,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "duration": 910.0,
                        "transitLeg": False,
                        "steps": [
                            {
                                "distance": 251.65200000000002,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140299194201013,
                                "lat": 45.75407631691722,
                                "elevation": [],
                            },
                            {
                                "distance": 847.393,
                                "relativeDirection": "RIGHT",
                                "streetName": "Rue Pasteur",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1114174,
                                "lat": 45.7527851,
                                "elevation": [],
                            },
                            {
                                "distance": 1090.657,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "Rue des Meuniers",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1052445,
                                "lat": 45.7586823,
                                "elevation": [],
                            },
                            {
                                "distance": 909.0539999999997,
                                "relativeDirection": "LEFT",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1046667,
                                "lat": 45.7683961,
                                "elevation": [],
                            },
                            {
                                "distance": 21.05,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "bretelle d'accès",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": True,
                                "lon": 3.0954261,
                                "lat": 45.773183,
                                "elevation": [],
                            },
                            {
                                "distance": 34.931,
                                "relativeDirection": "RIGHT",
                                "streetName": "Boulevard Fleury",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0952133,
                                "lat": 45.7733001,
                                "elevation": [],
                            },
                            {
                                "distance": 131.713,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue Michalias",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0953062,
                                "lat": 45.7736075,
                                "elevation": [],
                            },
                            {
                                "distance": 19.358,
                                "relativeDirection": "LEFT",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.094045,
                                "lat": 45.7743615,
                                "elevation": [],
                            },
                        ],
                    }
                ],
                "tooSloped": False,
            }
        ],
    },
    "debugOutput": {
        "precalculationTime": 0,
        "pathCalculationTime": 28,
        "pathTimes": [28],
        "renderingTime": 1,
        "totalTime": 29,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 51.02758866380542,
        "geoidElevation": False,
    },
}

ebike_response = {
    "requestParameters": {
        "date": "2020-09-22",
        "mode": "BICYCLE",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.754,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "08:30:00",
        "maxWalkDistance": "6048",
        "bikeSpeed": "5.6",
    },
    "plan": {
        "date": 1600756200000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 715,
                "startTime": 1600755485000,
                "endTime": 1600756200000,
                "walkTime": 715,
                "transitTime": 0,
                "waitingTime": 0,
                "walkDistance": 3305.910589287461,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1600755485000,
                        "endTime": 1600756200000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3305.8080000000004,
                        "pathway": False,
                        "mode": "BICYCLE",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1600755485000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1600756200000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "}igvGse_RBFHTHP^v@P\\DHBDVd@LVf@z@b@x@FNHPHZH\\DZJn@BVIDC@OLSRe@d@aA`ASPQPe@f@m@h@u@z@EBUPACCAAAC?A?C@ABAB?D?B@B@BB@O`@EHGN_@zAYpAOh@I`@Yl@UROHUJWLs@Nm@NQF_@RYRQPWTeA~@sAjAkAbAe@b@cA|@_@Z_@?oCCgA?G?q@?E?c@@U?S@UD_@Hi@LMBQD[HK@YBW@y@AcAAq@?U?UAm@?y@Ae@AQASA{@A]?QAM?O@K?QDSFi@NYHC@g@NKDG@[HC@SFUBOAC?}@KKAQCs@Ic@?Y@k@Bc@@OBKByAFqAFK?GAEECCCEMTS`@OVe@|@}AzC_AhBQ^]n@o@nAgAvBQZIPILGLcC~EgAtBIPUb@A@_@t@oAdCgBjDGPEVGh@MdDALCTAl@KTCBGNQEWEEAKC@H?H{AlCq@hAKNHNHR@B",
                            "length": 179,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "duration": 715.0,
                        "transitLeg": False,
                        "steps": [
                            {
                                "distance": 251.65200000000002,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140299194201013,
                                "lat": 45.75407631691722,
                                "elevation": [],
                            },
                            {
                                "distance": 847.393,
                                "relativeDirection": "RIGHT",
                                "streetName": "Rue Pasteur",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1114174,
                                "lat": 45.7527851,
                                "elevation": [],
                            },
                            {
                                "distance": 1090.657,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "Rue des Meuniers",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1052445,
                                "lat": 45.7586823,
                                "elevation": [],
                            },
                            {
                                "distance": 909.0539999999997,
                                "relativeDirection": "LEFT",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1046667,
                                "lat": 45.7683961,
                                "elevation": [],
                            },
                            {
                                "distance": 21.05,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "bretelle d'accès",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": True,
                                "lon": 3.0954261,
                                "lat": 45.773183,
                                "elevation": [],
                            },
                            {
                                "distance": 34.931,
                                "relativeDirection": "RIGHT",
                                "streetName": "Boulevard Fleury",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0952133,
                                "lat": 45.7733001,
                                "elevation": [],
                            },
                            {
                                "distance": 131.713,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue Michalias",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0953062,
                                "lat": 45.7736075,
                                "elevation": [],
                            },
                            {
                                "distance": 19.358,
                                "relativeDirection": "LEFT",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.094045,
                                "lat": 45.7743615,
                                "elevation": [],
                            },
                        ],
                    }
                ],
                "tooSloped": False,
            }
        ],
    },
    "debugOutput": {
        "precalculationTime": 0,
        "pathCalculationTime": 29,
        "pathTimes": [28],
        "renderingTime": 0,
        "totalTime": 29,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 51.02758866380542,
        "geoidElevation": False,
    },
}

fast_bike_response = {
    "requestParameters": {
        "date": "2020-09-22",
        "mode": "BICYCLE",
        "arriveBy": "True",
        "walkSpeed": "1.12",
        "fromPlace": "45.754,3.1141",
        "toPlace": "45.7743,3.0938",
        "time": "08:30:00",
        "maxWalkDistance": "6048",
        "bikeSpeed": "12.5",
    },
    "plan": {
        "date": 1600756200000,
        "from": {
            "name": "Origin",
            "lon": 3.1141,
            "lat": 45.754,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "to": {
            "name": "Destination",
            "lon": 3.0938,
            "lat": 45.7743,
            "orig": "",
            "vertexType": "NORMAL",
        },
        "itineraries": [
            {
                "duration": 385,
                "startTime": 1600755815000,
                "endTime": 1600756200000,
                "walkTime": 385,
                "transitTime": 0,
                "waitingTime": 0,
                "walkDistance": 3305.85396,
                "walkLimitExceeded": False,
                "elevationLost": 0.0,
                "elevationGained": 0.0,
                "transfers": 0,
                "legs": [
                    {
                        "startTime": 1600755815000,
                        "endTime": 1600756200000,
                        "departureDelay": 0,
                        "arrivalDelay": 0,
                        "realTime": False,
                        "distance": 3305.8080000000004,
                        "pathway": False,
                        "mode": "BICYCLE",
                        "route": "",
                        "agencyTimeZoneOffset": 7200000,
                        "interlineWithPreviousLeg": False,
                        "from": {
                            "name": "Origin",
                            "lon": 3.1141,
                            "lat": 45.754,
                            "departure": 1600755815000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "to": {
                            "name": "Destination",
                            "lon": 3.0938,
                            "lat": 45.7743,
                            "arrival": 1600756200000,
                            "orig": "",
                            "vertexType": "NORMAL",
                        },
                        "legGeometry": {
                            "points": "}igvGse_RBFHTHP^v@P\\DHBDVd@LVf@z@b@x@FNHPHZH\\DZJn@BVIDC@OLSRe@d@aA`ASPQPe@f@m@h@u@z@EBUPACCAAAC?A?C@ABAB?D?B@B@BB@O`@EHGN_@zAYpAOh@I`@Yl@UROHUJWLs@Nm@NQF_@RYRQPWTeA~@sAjAkAbAe@b@cA|@_@Z_@?oCCgA?G?q@?E?c@@U?S@UD_@Hi@LMBQD[HK@YBW@y@AcAAq@?U?UAm@?y@Ae@AQASA{@A]?QAM?O@K?QDSFi@NYHC@g@NKDG@[HC@SFUBOAC?}@KKAQCs@Ic@?Y@k@Bc@@OBKByAFqAFK?GAEECCCEMTS`@OVe@|@}AzC_AhBQ^]n@o@nAgAvBQZIPILGLcC~EgAtBIPUb@A@_@t@oAdCgBjDGPEVGh@MdDALCTAl@KTCBGNQEWEEAKC@H?H{AlCq@hAKNHNHR@B",
                            "length": 179,
                        },
                        "rentedBike": False,
                        "flexDrtAdvanceBookMin": 0.0,
                        "duration": 385.0,
                        "transitLeg": False,
                        "steps": [
                            {
                                "distance": 251.65200000000002,
                                "relativeDirection": "DEPART",
                                "streetName": "Rue des Foisses",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1140299194201013,
                                "lat": 45.75407631691722,
                                "elevation": [],
                            },
                            {
                                "distance": 847.393,
                                "relativeDirection": "RIGHT",
                                "streetName": "Rue Pasteur",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1114174,
                                "lat": 45.7527851,
                                "elevation": [],
                            },
                            {
                                "distance": 1090.657,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "Rue des Meuniers",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1052445,
                                "lat": 45.7586823,
                                "elevation": [],
                            },
                            {
                                "distance": 909.0539999999997,
                                "relativeDirection": "LEFT",
                                "streetName": "Boulevard Lafayette",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.1046667,
                                "lat": 45.7683961,
                                "elevation": [],
                            },
                            {
                                "distance": 21.05,
                                "relativeDirection": "SLIGHTLY_RIGHT",
                                "streetName": "bretelle d'accès",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": True,
                                "lon": 3.0954261,
                                "lat": 45.773183,
                                "elevation": [],
                            },
                            {
                                "distance": 34.931,
                                "relativeDirection": "RIGHT",
                                "streetName": "Boulevard Fleury",
                                "absoluteDirection": "NORTH",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0952133,
                                "lat": 45.7733001,
                                "elevation": [],
                            },
                            {
                                "distance": 131.713,
                                "relativeDirection": "LEFT",
                                "streetName": "Rue Michalias",
                                "absoluteDirection": "NORTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.0953062,
                                "lat": 45.7736075,
                                "elevation": [],
                            },
                            {
                                "distance": 19.358,
                                "relativeDirection": "LEFT",
                                "streetName": "Avenue des Paulines",
                                "absoluteDirection": "SOUTHWEST",
                                "stayOn": False,
                                "area": False,
                                "bogusName": False,
                                "lon": 3.094045,
                                "lat": 45.7743615,
                                "elevation": [],
                            },
                        ],
                    }
                ],
                "tooSloped": False,
            }
        ],
    },
    "debugOutput": {
        "precalculationTime": 0,
        "pathCalculationTime": 36,
        "pathTimes": [35],
        "renderingTime": 6,
        "totalTime": 42,
        "timedOut": False,
    },
    "elevationMetadata": {
        "ellipsoidToGeoidDifference": 51.02758866380542,
        "geoidElevation": False,
    },
}

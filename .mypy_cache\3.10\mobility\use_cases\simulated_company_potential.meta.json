{"data_mtime": 1752154448, "dep_lines": [10, 11, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 22, 23, 27, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.indicators", "mobility.builders.address_expander", "mobility.ir.company_potential", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.study", "mobility.repositories.abstract_repositories", "mobility.use_cases.base_use_case", "mobility.workers.distance_computer", "mobility.workers.emission_computer", "mobility.workers.ideal_scenario", "mobility.workers.territory_computer", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "mobility.workers.travel_timer", "dataclasses", "random", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "enum", "mobility.builders", "mobility.funky", "mobility.ir", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.indicators.study_indicators", "mobility.ir.plan", "mobility.ir.zfe", "mobility.quantity", "mobility.repositories", "mobility.workers", "mobility.workers.zfe_impact_computer", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "typing_extensions"], "hash": "2fd8669db78bee674619f48cc8e029815492a827", "id": "mobility.use_cases.simulated_company_potential", "ignore_all": false, "interface_hash": "029b78c7b32e132f63167cb54caf8d444498d980", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\use_cases\\simulated_company_potential.py", "plugin_data": null, "size": 6793, "suppressed": [], "version_id": "1.16.1"}
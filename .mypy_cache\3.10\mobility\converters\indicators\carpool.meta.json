{"data_mtime": 1753087664, "dep_lines": [7, 15, 4, 5, 12, 13, 14, 16, 17, 18, 20, 25, 26, 11, 19, 1, 2, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.scenario", "mobility.ir.indicators.carpool_indicators", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.site", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.carpool_computer", "mobility.workers.cost_computer", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "configparser", "enum", "fractions", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.study_types", "mobility.workers", "mobility.workers.health_cost_calculator", "numbers", "typing_extensions"], "hash": "ae73018b8185e4f96a5e803737ab6863815dde50", "id": "mobility.converters.indicators.carpool", "ignore_all": false, "interface_hash": "4a22f0efe7746f8f7655b0386a854ed33fb47c75", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\carpool.py", "plugin_data": null, "size": 12901, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "mobility.ir.indicators.poi_scenario_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PoiScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.PoiScenario", "kind": "Gdef"}, "PoiScenarioIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", "name": "PoiScenarioIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.ir.indicators.poi_scenario_indicators", "mro": ["mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenario"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PoiScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_closest_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["access_times"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.get_closest_poi", "name": "get_closest_poi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["access_times"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_closest_poi of PoiScenarioIndicators", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.get_closest_poi", "name": "get_closest_poi", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["access_times"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_closest_poi of PoiScenarioIndicators", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_poi_access_times": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "modes", "site"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.get_poi_access_times", "name": "get_poi_access_times", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "modes", "site"], "arg_types": ["mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.site.GeoSite"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_poi_access_times of PoiScenarioIndicators", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nickname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.nickname", "name": "nickname", "setter_type": null, "type": "builtins.str"}}, "scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.scenario", "name": "scenario", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenario"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.poi_scenario_indicators.PoiScenarioIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointOfInterest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.poi.PointOfInterest", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.poi_scenario_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}, "shorten_nickname": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.indicators.study_indicators.shorten_nickname", "kind": "Gdef"}}, "path": "mobility\\ir\\indicators\\poi_scenario_indicators.py"}
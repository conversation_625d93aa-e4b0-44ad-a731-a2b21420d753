{"data_mtime": 1751444468, "dep_lines": [5, 6, 7, 8, 9, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.flight.flight_travel_time", "mobility.ir.geo_study", "mobility.ir.transport", "unittest.mock", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.python_api", "abc", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.flight", "contextlib", "enum", "mobility", "mobility.ir", "mobility.ir.territory", "re", "types", "typing", "typing_extensions", "unittest"], "hash": "4b48bb6d7a4246abdaa16d320e8d8d74930a1eb7", "id": "api_abstraction.tests.flight.test_flight_travel_timer", "ignore_all": false, "interface_hash": "770e684b55a41554682b422b661c9a9bc200824a", "mtime": 1748435570, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\flight\\test_flight_travel_timer.py", "plugin_data": null, "size": 8549, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444411, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.study_types", "mobility.workers.coworking_scenario_computer", "builtins", "_frozen_importlib", "abc", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.study", "mobility.ir.territory", "mobility.workers", "typing"], "hash": "90e2b3282a751cf1e87e5a4bb849c451ba8f33a7", "id": "mobility.tests.workers.test_coworking_scenario_computer", "ignore_all": false, "interface_hash": "5651b8c43584c1786a9382b72e07261998f396c1", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_coworking_scenario_computer.py", "plugin_data": null, "size": 6882, "suppressed": [], "version_id": "1.16.1"}
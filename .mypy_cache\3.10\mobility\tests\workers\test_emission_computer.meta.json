{"data_mtime": 1753176777, "dep_lines": [6, 7, 8, 9, 5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.workers.emission_computer", "mobility.funky", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.workers", "re", "typing_extensions"], "hash": "e4f8f01fa7964ffcb51ddc0d6a525d43ce1eb138", "id": "mobility.tests.workers.test_emission_computer", "ignore_all": false, "interface_hash": "b6981c95db99dcdcd02b1ecc05152d0466151c96", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_emission_computer.py", "plugin_data": null, "size": 9027, "suppressed": [], "version_id": "1.16.1"}
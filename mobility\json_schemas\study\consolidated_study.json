{"$schema": "http://json-schema.org/draft-07/schema", "$id": "file:///schemas/study/consolidated_study.json", "type": "object", "properties": {"version": {"const": "v18"}, "data": {"$ref": "/schemas/study/study_data.json"}, "scenarios": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_modal_commute_scenario.json"}}, "poi_scenarios": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_poi_commute_scenario.json"}}, "public_transport_stations_scenario": {"$ref": "/schemas/scenario/site_to_pt_stop_scenario.json"}, "bicycle_amenity_scenario": {"$ref": "/schemas/scenario/site_to_bike_scenario.json"}, "car_amenity_scenario": {"$ref": "/schemas/scenario/site_to_car_scenario.json"}, "time_failed": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_modal_commute_scenario.json"}}, "geocode_failed_employees": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_no_commute_scenario.json"}}, "geocode_failed_sites": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_no_commute_scenario.json"}}, "geocode_failed_both": {"type": "array", "items": {"$ref": "/schemas/scenario/id_ref_no_commute_scenario.json"}}, "employees": {"$ref": "/schemas/employee/employees_mapping.json"}, "sites": {"$ref": "/schemas/site/sites_mapping.json"}, "scenarios_data": {"$ref": "/schemas/scenario_data/scenario_data_mapping.json"}, "public_transport_stops": {"type": "array", "items": {"$ref": "/schemas/infrastructure/pt_stop.json"}}, "public_transport_lines": {"type": "array", "items": {"$ref": "/schemas/infrastructure/pt_line.json"}}, "bicycle_amenities": {"type": "array", "items": {"$ref": "/schemas/infrastructure/bike_amenity.json"}}, "bicycle_lines": {"type": "array", "items": {"$ref": "/schemas/infrastructure/bike_line.json"}}, "car_amenities": {"type": "array", "items": {"$ref": "/schemas/infrastructure/car_amenity.json"}}, "car_ways": {"type": "array", "items": {"$ref": "/schemas/infrastructure/car_way.json"}}, "isochrones": {"$ref": "/schemas/isochrone/site_mode_isochrone_mapping.json"}, "coworking_scenario": {"$ref": "/schemas/scenario/coworking_scenario.json"}, "coworking_failed_scenario": {"$ref": "/schemas/scenario/coworking_scenario.json"}}, "required": ["version", "data", "scenarios", "poi_scenarios", "public_transport_stations_scenario", "bicycle_amenity_scenario", "car_amenity_scenario", "time_failed", "geocode_failed_employees", "geocode_failed_sites", "geocode_failed_both", "employees", "sites", "scenarios_data", "public_transport_stops", "public_transport_lines", "bicycle_amenities", "bicycle_lines", "car_amenities", "car_ways", "isochrones", "coworking_scenario", "coworking_failed_scenario"], "additionalProperties": false}
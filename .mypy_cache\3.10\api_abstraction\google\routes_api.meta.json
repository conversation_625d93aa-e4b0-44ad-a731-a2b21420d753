{"data_mtime": **********, "dep_lines": [3, 4, 6, 7, 8, 12, 3, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.api_core.exceptions", "google.maps.routing_v2", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "api_abstraction.google.base_google_api", "mobility.ir.transport", "google.api_core", "typing", "google", "builtins", "_frozen_importlib", "abc", "api_abstraction.api", "datetime", "enum", "google.api_core.client_info", "google.api_core.client_options", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.auth", "google.auth._credentials_base", "google.auth.credentials", "google.maps", "google.maps.routing_v2.services", "google.maps.routing_v2.services.routes", "google.maps.routing_v2.services.routes.client", "google.maps.routing_v2.services.routes.transports", "google.maps.routing_v2.services.routes.transports.base", "google.maps.routing_v2.types", "google.maps.routing_v2.types.route", "google.maps.routing_v2.types.routes_service", "google.protobuf", "google.protobuf.duration_pb2", "google.protobuf.internal", "google.protobuf.internal.well_known_types", "google.protobuf.message", "mobility", "mobility.ir", "mobility.ir.geo_study", "typing_extensions"], "hash": "e01994b6155539a2bd7451c2fa0a2126e93b0a08", "id": "api_abstraction.google.routes_api", "ignore_all": false, "interface_hash": "79941ed49a86efd627a91c974132b27d23435bc9", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\google\\routes_api.py", "plugin_data": null, "size": 4696, "suppressed": [], "version_id": "1.16.1"}
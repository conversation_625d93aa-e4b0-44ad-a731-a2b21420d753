# Strasbourg Aggregation Flow - FlowChartGrapher Refactoring Applied

## Overview

The `strasbourg_aggregation_flow.py` script has been updated to take full advantage of the refactored FlowChartGrapher class, which now provides enhanced customization and intelligent text fitting capabilities.

## Key Improvements Applied

### 1. Enhanced Constructor Configuration

The FlowChartGrapher is now instantiated with optimized parameters for complex multi-level visualizations:

```python
grapher = FlowChartGrapher[str](
    output_dir,
    main_title_font_size_px=32,      # Smaller main title for complex charts
    title_font_size_px=26,           # Smaller level titles
    line_header_font_size_px=18,     # Smaller category labels
    number_font_size_px=12,          # Smaller numbers for better fit
    column_width=90,                 # Slightly wider columns for better text fitting
    categories_padding=25,           # More padding for cleaner layout
    flow_width=250,                  # Wider flows for better visual impact
    node_text_color="#ffffff",       # White text for better contrast
    default_font_color="#2c3e50",    # Dark blue-gray for category labels
    flow_opacity=0.5,                # Slightly more opaque flows for better visibility
    main_title_color="#1a237e"       # Deep blue for main title
)
```

### 2. Intelligent Text Fitting Benefits

The refactored FlowChartGrapher now automatically handles text overflow in SVG nodes:

- **Primary Display**: Shows both percentage and value when both fit within the node
- **Fallback 1**: Shows only percentage when full text doesn't fit
- **Fallback 2**: Shows nothing when even percentage doesn't fit

This is particularly beneficial for the Strasbourg chart which has:
- 7 Strasbourg communes with potentially long names
- 10 external zones with varying name lengths
- Complex 4-level flow structure requiring clear labeling

### 3. Visual Enhancements

**Layout Improvements:**
- Wider columns (90px vs 80px) provide more space for text
- Increased padding (25px vs 20px) creates cleaner visual separation
- Wider flows (250px vs 200px) improve visual impact and readability

**Color Scheme:**
- Professional color palette with better contrast
- White text on colored backgrounds for optimal readability
- Deep blue main title for authoritative appearance
- Dark blue-gray category labels for professional look

**Typography:**
- Optimized font sizes for multi-level complexity
- Smaller fonts prevent overcrowding while maintaining readability
- Consistent font hierarchy across all chart elements

### 4. Backward Compatibility

The refactoring maintains full backward compatibility:
- All existing functionality preserved
- No breaking changes to the public API
- Existing scripts continue to work without modification
- Enhanced features are opt-in through constructor parameters

## Chart Structure

The Strasbourg aggregation flow demonstrates a complete 4-level aggregation/disaggregation pattern:

1. **Level 1**: Strasbourg communes (origins) - All flows start at commune level
2. **Level 2**: All departments (origins) - Strasbourg + external zones
3. **Level 3**: All departments (destinations) - Complete mobility picture
4. **Level 4**: Strasbourg communes (destinations) - All flows end at commune level

## Output

The enhanced chart is generated as:
- **File**: `output_flow_charts/charts/strasbourg_aggregation_disaggregation.svg`
- **Features**: Professional styling, intelligent text fitting, optimized layout
- **Data**: 113 4-level flow records from 190 CSV rows
- **Categories**: 18 total (7 Strasbourg communes + 10 external zones + 1 Strasbourg department)

## Benefits for Complex Visualizations

The refactored implementation is particularly beneficial for complex charts like Strasbourg's because:

1. **Scalability**: Handles varying text lengths gracefully
2. **Readability**: Optimized fonts and spacing prevent overcrowding
3. **Professional Appearance**: Consistent styling and color scheme
4. **Flexibility**: Easy to adjust parameters for different data sets
5. **Maintainability**: Centralized configuration reduces code duplication

## Usage Example

```python
# Before refactoring - limited customization
grapher = FlowChartGrapher[str](output_dir)
output_file = grapher.make_multilevel_chart(
    # ... parameters including font sizes in method call
    main_title_font_size_px=32,
    title_font_size_px=26,
    # ... other parameters
)

# After refactoring - enhanced customization
grapher = FlowChartGrapher[str](
    output_dir,
    main_title_font_size_px=32,
    title_font_size_px=26,
    column_width=90,
    node_text_color="#ffffff",
    # ... all customization in constructor
)
output_file = grapher.make_multilevel_chart(
    # ... only chart-specific parameters
)
```

This refactoring demonstrates how the enhanced FlowChartGrapher can significantly improve the quality and readability of complex multi-level flow visualizations while maintaining code simplicity and maintainability.

{"data_mtime": 1751444476, "dep_lines": [18, 7, 9, 10, 15, 16, 17, 6, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["webservice.apps.repositories.query_record", "mobility.ir.onboarding_form_step", "mobility.repositories.onboarding_repository", "mobility.use_cases.compute_onboarding_results", "mobility.use_cases.record_query", "mobility.use_cases.run_onboarding_form", "webservice.apps.db", "mobility.enumerations", "mobility.quantity", "typing", "flask", "builtins", "_frozen_importlib", "abc", "enum", "flask.blueprints", "flask.globals", "flask.helpers", "flask.templating", "flask.wrappers", "mobility", "mobility.ir", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.repositories.config_repository", "mobility.use_cases", "mobility.use_cases.base_use_case", "types", "typing_extensions", "webservice.apps.repositories", "werkzeug", "werkzeug.datastructures", "werkzeug.utils", "werkzeug.wrappers"], "hash": "734ac851f527fa9e0895a888419545ab48905266", "id": "webservice.apps.routes.onboarding", "ignore_all": false, "interface_hash": "ea8cec8a37d70b43f9d6763a1ce4c2be94ef29e9", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\routes\\onboarding.py", "plugin_data": null, "size": 4509, "suppressed": ["flask_cors"], "version_id": "1.16.1"}
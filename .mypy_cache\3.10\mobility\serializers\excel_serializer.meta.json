{"data_mtime": 1752154450, "dep_lines": [22, 20, 21, 23, 9, 19, 24, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.cost", "mobility.ir.excel", "mobility.ir.study", "mobility.constants", "mobility.funky", "mobility.quantity", "shutil", "collections", "pathlib", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.inter_scenario_indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.territory", "os", "typing_extensions"], "hash": "ccf37ae7b986f85b1b79417a6e08512b4bcc97e9", "id": "mobility.serializers.excel_serializer", "ignore_all": false, "interface_hash": "def064891c789cd3b67c30cc19281d3b59b6a2bc", "mtime": 1751987098, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\excel_serializer.py", "plugin_data": null, "size": 18378, "suppressed": ["pandas"], "version_id": "1.16.1"}
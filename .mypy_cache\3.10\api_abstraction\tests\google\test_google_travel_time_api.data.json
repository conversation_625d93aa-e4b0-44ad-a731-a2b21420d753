{".class": "MypyFile", "_fullname": "api_abstraction.tests.google.test_google_travel_time_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "ApiTimeout": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiTimeout", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "GoogleRequestParameters": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.base_google_api.GoogleRequestParameters", "kind": "Gdef"}, "GoogleTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.api.GoogleTravelTimeAPI", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TestComputeInfSupJourney": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", "name": "TestComputeInfSupJourney", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", "builtins.object"], "names": {".class": "SymbolTable", "test_should_return_non_if_no_journey_is_stored": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_non_if_no_journey_is_stored", "name": "test_should_return_non_if_no_journey_is_stored", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_non_if_no_journey_is_stored of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_return_the_inf_journey_before_requested_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_the_inf_journey_before_requested_time", "name": "test_should_return_the_inf_journey_before_requested_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_the_inf_journey_before_requested_time of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_return_the_sup_journey_above_requested_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_the_sup_journey_above_requested_time", "name": "test_should_return_the_sup_journey_above_requested_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_the_sup_journey_above_requested_time of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_return_the_two_journeys_arriving_around_requested_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_the_two_journeys_arriving_around_requested_time", "name": "test_should_return_the_two_journeys_arriving_around_requested_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_the_two_journeys_arriving_around_requested_time of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_return_the_two_journeys_arriving_around_requested_time_after_filtering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_the_two_journeys_arriving_around_requested_time_after_filtering", "name": "test_should_return_the_two_journeys_arriving_around_requested_time_after_filtering", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_the_two_journeys_arriving_around_requested_time_after_filtering of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_return_the_two_journeys_departing_successively_and_arriving_around_requested_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.test_should_return_the_two_journeys_departing_successively_and_arriving_around_requested_time", "name": "test_should_return_the_two_journeys_departing_successively_and_arriving_around_requested_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey_attribute"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_return_the_two_journeys_departing_successively_and_arriving_around_requested_time of TestComputeInfSupJourney", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.TestComputeInfSupJourney", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestDistanceGoogleTravelTimeApi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi", "name": "TestDistanceGoogleTravelTimeApi", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_route_compute_belleville_to_lyon_driving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_compute_route_compute_belleville_to_lyon_driving", "name": "test_compute_route_compute_belleville_to_lyon_driving", "type": null}}, "test_compute_route_compute_belleville_to_lyon_driving_at_10": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_compute_route_compute_belleville_to_lyon_driving_at_10", "name": "test_compute_route_compute_belleville_to_lyon_driving_at_10", "type": null}}, "test_compute_route_fails_if_no_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_compute_route_fails_if_no_route", "name": "test_compute_route_fails_if_no_route", "type": null}}, "test_compute_route_is_resilient_to_transient_traffic_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "failing_traffic_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_compute_route_is_resilient_to_transient_traffic_failure", "name": "test_compute_route_is_resilient_to_transient_traffic_failure", "type": null}}, "test_compute_route_times_out_on_repeated_traffic_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "failing_traffic_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_compute_route_times_out_on_repeated_traffic_failure", "name": "test_compute_route_times_out_on_repeated_traffic_failure", "type": null}}, "test_fails_to_time_electric_bike": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_fails_to_time_electric_bike", "name": "test_fails_to_time_electric_bike", "type": null}}, "test_get_time_from_store_for_car_like_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_route", "car_like_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_get_time_from_store_for_car_like_modes", "name": "test_get_time_from_store_for_car_like_modes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_get_time_from_store_for_car_like_modes", "name": "test_get_time_from_store_for_car_like_modes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_raise_an_error_when_asking_to_compute_an_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_raise_an_error_when_asking_to_compute_an_isochrone", "name": "test_raise_an_error_when_asking_to_compute_an_isochrone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raise_an_error_when_asking_to_compute_an_isochrone of TestDistanceGoogleTravelTimeApi", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_time_biking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.test_time_biking", "name": "test_time_biking", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeApi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestDistanceGoogleTravelTimeInternals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals", "name": "TestDistanceGoogleTravelTimeInternals", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals", "builtins.object"], "names": {".class": "SymbolTable", "test_can_estimate_car_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_can_estimate_car_duration", "name": "test_can_estimate_car_duration", "type": null}}, "test_can_estimate_car_duration_based_on_records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_can_estimate_car_duration_based_on_records", "name": "test_can_estimate_car_duration_based_on_records", "type": null}}, "test_can_estimate_car_duration_based_on_records_uneven": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_can_estimate_car_duration_based_on_records_uneven", "name": "test_can_estimate_car_duration_based_on_records_uneven", "type": null}}, "test_can_record_car_travel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_can_record_car_travel", "name": "test_can_record_car_travel", "type": null}}, "test_can_retrieve_stored_car_result_in_tolerance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_can_retrieve_stored_car_result_in_tolerance", "name": "test_can_retrieve_stored_car_result_in_tolerance", "type": null}}, "test_cannot_retrieve_stored_car_result_out_of_tolerance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_cannot_retrieve_stored_car_result_out_of_tolerance", "name": "test_cannot_retrieve_stored_car_result_out_of_tolerance", "type": null}}, "test_should_not_find_different_results_for_same_od": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_should_not_find_different_results_for_same_od", "name": "test_should_not_find_different_results_for_same_od", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_not_find_different_results_for_same_od of TestDistanceGoogleTravelTimeInternals", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_not_retry_finding_departure_time_for_same_od": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.test_should_not_retry_finding_departure_time_for_same_od", "name": "test_should_not_retry_finding_departure_time_for_same_od", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_not_retry_finding_departure_time_for_same_od of TestDistanceGoogleTravelTimeInternals", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.TestDistanceGoogleTravelTimeInternals", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestFindDurationWithLinearRegression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression", "name": "TestFindDurationWithLinearRegression", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression", "builtins.object"], "names": {".class": "SymbolTable", "test_should_find_duration_on_real_case_actually": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression.test_should_find_duration_on_real_case_actually", "name": "test_should_find_duration_on_real_case_actually", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_find_duration_on_real_case_actually of TestFindDurationWithLinearRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_find_duration_when_lin_reg_is_unavailable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression.test_should_find_duration_when_lin_reg_is_unavailable", "name": "test_should_find_duration_when_lin_reg_is_unavailable", "type": null}}, "test_should_find_duration_with_lin_reg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_compute_route_linear_with_gap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression.test_should_find_duration_with_lin_reg", "name": "test_should_find_duration_with_lin_reg", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.TestFindDurationWithLinearRegression", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "failing_traffic_compute_route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.failing_traffic_compute_route", "name": "failing_traffic_compute_route", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "failing_traffic_compute_route", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.failing_traffic_compute_route", "name": "failing_traffic_compute_route", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "failing_traffic_compute_route", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fake_compute_route@19": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19", "builtins.object"], "names": {".class": "SymbolTable", "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19", "api_abstraction.google.base_google_api.GoogleRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of fake_compute_route", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@19", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_compute_route@436": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436", "builtins.object"], "names": {".class": "SymbolTable", "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436", "api_abstraction.google.base_google_api.GoogleRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of fake_compute_route", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@436", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_compute_route@460": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460", "builtins.object"], "names": {".class": "SymbolTable", "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460", "api_abstraction.google.base_google_api.GoogleRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of fake_compute_route", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@460", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_compute_route@72": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_google_travel_time_api", "mro": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n_failing_calls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n_failing_calls"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of fake_compute_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72.compute_route", "name": "compute_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters"], "arg_types": ["api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "api_abstraction.google.base_google_api.GoogleRequestParameters"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_route of fake_compute_route", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "n_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72.n_calls", "name": "n_calls", "setter_type": null, "type": "builtins.int"}}, "n_failing_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72.n_failing_calls", "name": "n_failing_calls", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_google_travel_time_api.fake_compute_route@72", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "hours": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.hours", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "minutes": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.minutes", "kind": "Gdef"}, "mock_compute_route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["geo_coordinates"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.mock_compute_route", "name": "mock_compute_route", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["geo_coordinates"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_compute_route", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.mock_compute_route", "name": "mock_compute_route", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["geo_coordinates"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_compute_route", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_compute_route_linear_with_gap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.mock_compute_route_linear_with_gap", "name": "mock_compute_route_linear_with_gap", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_compute_route_linear_with_gap", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.mock_compute_route_linear_with_gap", "name": "mock_compute_route_linear_with_gap", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_compute_route_linear_with_gap", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_compute_route_parametrized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mock_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_google_travel_time_api.mock_compute_route_parametrized", "name": "mock_compute_route_parametrized", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mock_parameters"], "arg_types": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_compute_route_parametrized", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}}, "path": "api_abstraction\\tests\\google\\test_google_travel_time_api.py"}
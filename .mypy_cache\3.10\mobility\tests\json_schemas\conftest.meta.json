{"data_mtime": 1751444429, "dep_lines": [8, 9, 1, 2, 3, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.builders.json_builder", "mobility.constants", "json", "os", "typing", "pytest", "builtins", "_frozen_importlib", "_io", "_pytest", "_pytest.config", "_pytest.fixtures", "_typeshed", "abc", "io", "json.decoder", "mobility.builders", "ntpath"], "hash": "ef144fb112a03cf17eaa43b0648e8accdfb15354", "id": "mobility.tests.json_schemas.conftest", "ignore_all": false, "interface_hash": "e21d987cc46258d00cf50ab3a3a33a1948bcb5ce", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\json_schemas\\conftest.py", "plugin_data": null, "size": 1540, "suppressed": ["jsonschema"], "version_id": "1.16.1"}
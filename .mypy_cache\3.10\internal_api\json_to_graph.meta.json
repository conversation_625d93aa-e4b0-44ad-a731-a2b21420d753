{"data_mtime": 1751444402, "dep_lines": [7, 8, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.ir.geo_study", "mobility.ir.transport", "<PERSON><PERSON><PERSON><PERSON>", "json", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "mobility", "mobility.ir", "os", "typing_extensions"], "hash": "716cb29032bf635c6f499d52957e034901f774cf", "id": "internal_api.json_to_graph", "ignore_all": false, "interface_hash": "7e40dc135ca775e945bfffae6de30a8d5e28156d", "mtime": 1722327415, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\json_to_graph.py", "plugin_data": null, "size": 1757, "suppressed": ["networkx"], "version_id": "1.16.1"}
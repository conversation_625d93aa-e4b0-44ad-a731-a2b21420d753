{"data_mtime": 1751444403, "dep_lines": [5, 6, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.ir.geo_study", "mobility.ir.transport", "internal_api.graph_to_json", "internal_api.json_to_graph", "builtins", "_frozen_importlib", "abc", "enum", "mobility", "mobility.ir", "typing", "typing_extensions"], "hash": "985ee2ec258eed1f982635965f335118b68fb2c2", "id": "internal_api.tests.test_graph_from_json", "ignore_all": false, "interface_hash": "51556b8efd72c27ffd4b39d3c65756cdd07b3051", "mtime": 1723449311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\tests\\test_graph_from_json.py", "plugin_data": null, "size": 7495, "suppressed": ["networkx"], "version_id": "1.16.1"}
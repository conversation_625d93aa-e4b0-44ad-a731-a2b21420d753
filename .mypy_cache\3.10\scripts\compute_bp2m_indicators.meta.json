{"data_mtime": 1753092209, "dep_lines": [46, 18, 19, 20, 21, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 47, 48, 49, 25, 35, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 15, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 16, 11, 13], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10], "dependencies": ["mobility.serializers.charters.cost_grapher", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.travel_time_api", "api_abstraction.otp.api", "mobility.builders.territory_builder", "mobility.builders.territory_database", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.crit_air", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.workers.distance_computer", "mobility.workers.geo_projection", "mobility.funky", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "csv", "dataclasses", "logging", "os", "collections", "datetime", "enum", "typing", "osmium", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobility", "builtins", "_collections_abc", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.otp", "configparser", "functools", "io", "mobility.builders", "mobility.ir", "mobility.ir.country", "mobility.ir.infrastructure", "mobility.serializers.charters", "mobility.workers", "mobility.workers.employee_cost_calculator", "mobility.workers.health_cost_calculator", "mobility.workers.infrastructure_cost_calculator", "mobility.workers.transport_cost_calculator", "mobility.workers.work_accident_cost_calculator", "osmium.io", "osmium.osm", "osmium.osm._osm", "osmium.osm.types", "osmium.simple_handler", "pyproj._geod", "pyproj.geod", "types", "typing_extensions"], "hash": "d6df1351eee749a5cf23ea591e64bbe9b1e56948", "id": "scripts.compute_bp2m_indicators", "ignore_all": false, "interface_hash": "91efdfcbe792c4bf528af3c20948bbaee0a35d1b", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\compute_bp2m_indicators.py", "plugin_data": null, "size": 44194, "suppressed": ["fiona.crs", "shapely.geometry", "fiona", "shapely"], "version_id": "1.16.1"}
{"data_mtime": 1752154450, "dep_lines": [5, 15, 16, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.excel", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.constants", "mobility.funky", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "enum", "mobility.ir", "typing", "typing_extensions"], "hash": "1f71d3f146fa7a690a8f8795e7059b9347df105a", "id": "mobility.tests.ir.test_excel", "ignore_all": false, "interface_hash": "104e139e7241d79a29eef1c1d81c76aa83958cd1", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\test_excel.py", "plugin_data": null, "size": 6109, "suppressed": [], "version_id": "1.16.1"}
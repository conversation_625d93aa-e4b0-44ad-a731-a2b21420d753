{"data_mtime": 1752154444, "dep_lines": [4, 6, 7, 3, 8, 5, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.scenario", "mobility.ir.indicators.critical_cases", "mobility.ir.indicators.scores", "mobility.converters.accessibility", "mobility.ir.study", "mobility.funky", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "abc", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.transport"], "hash": "eb0ae691e6a0e73c18a08f7756e6fa2df2f673aa", "id": "mobility.converters.indicators.scores", "ignore_all": false, "interface_hash": "6bbfee2081624f2ff61934c7a19608c6cf947395", "mtime": 1722327416, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\scores.py", "plugin_data": null, "size": 3859, "suppressed": [], "version_id": "1.16.1"}
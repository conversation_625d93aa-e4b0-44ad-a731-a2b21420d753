{"data_mtime": 1753087664, "dep_lines": [9, 10, 11, 16, 19, 20, 21, 28, 29, 30, 34, 35, 39, 8, 25, 26, 27, 40, 41, 52, 53, 6, 24, 42, 1, 2, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.carpool", "mobility.converters.indicators.clustering", "mobility.converters.indicators.mode_shift_scenario", "mobility.converters.indicators.remote_scenario", "mobility.converters.indicators.scenario", "mobility.converters.indicators.scores", "mobility.converters.indicators.staggered_hours", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.clustering_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.staggered_hours_indicators", "mobility.ir.indicators.study_indicators", "mobility.converters.accessibility", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.workers.zfe_impact_computer", "mobility.m_list", "mobility.funky", "mobility.quantity", "collections", "typing", "numpy", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir.commute_data", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.scores", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.workers", "mobility.workers.health_cost_calculator", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "typing_extensions"], "hash": "a6c4ea459e9295f727c2749006741caf2a1a6ad8", "id": "mobility.ir.indicators.scenario_indicators", "ignore_all": false, "interface_hash": "0bc41c45ec442ca58273a76feb6143e1ff67b5b7", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\scenario_indicators.py", "plugin_data": null, "size": 24014, "suppressed": [], "version_id": "1.16.1"}
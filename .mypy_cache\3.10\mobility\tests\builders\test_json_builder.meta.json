{"data_mtime": 1753176790, "dep_lines": [7, 8, 18, 19, 20, 21, 22, 27, 28, 30, 3, 17, 29, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.exceptions", "mobility.builders.json_builder", "mobility.ir.commute_data", "mobility.ir.crit_air", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.serializers.json_serializer", "unittest.mock", "mobility.funky", "mobility.quantity", "json", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_io", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "io", "json.encoder", "mobility.builders", "mobility.ir", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "os", "re", "typing_extensions"], "hash": "6004dc7ee98491bf244eca13202c88361f6afaa2", "id": "mobility.tests.builders.test_json_builder", "ignore_all": false, "interface_hash": "b0852af1074ac6c79bd5f930849710672043af5c", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\builders\\test_json_builder.py", "plugin_data": null, "size": 64549, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444420, "dep_lines": [5, 24, 25, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.constants", "mobility.funky", "json", "os", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "mobility.builders", "ntpath", "posixpath", "types", "typing_extensions"], "hash": "d49455d3615ab3d395d16cc47c80ec37752a5cba", "id": "mobility.tests.builders.test_json_version_converters", "ignore_all": false, "interface_hash": "ccf33ce8c21c3fed6998acc999ece4379f4e7560", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\builders\\test_json_version_converters.py", "plugin_data": null, "size": 9367, "suppressed": [], "version_id": "1.16.1"}
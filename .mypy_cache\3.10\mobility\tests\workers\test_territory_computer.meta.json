{"data_mtime": 1751444426, "dep_lines": [5, 6, 11, 12, 13, 14, 15, 16, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.mode_constraints", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.territory_computer", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "abc", "contextlib", "enum", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.country", "mobility.ir.employee", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.workers", "re", "typing_extensions"], "hash": "60c1b0ce2aadc333c3d0c8fe32c8f078a1ce6bbb", "id": "mobility.tests.workers.test_territory_computer", "ignore_all": false, "interface_hash": "003a76d5c9088dee04c1a9a61e58d106598d833b", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_territory_computer.py", "plugin_data": null, "size": 15968, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444480, "dep_lines": [21, 6, 7, 16, 17, 20, 2, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["webservice.apps.routes.onboarding", "mobility.ir.geo_study", "mobility.ir.mode_shift_readiness_classification", "mobility.ir.transport", "mobility.repositories.abstract_repositories", "mobility.use_cases.classify_mode_shift_readiness", "unittest.mock", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.python_api", "abc", "contextlib", "enum", "mobility.ir", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "mobility.use_cases.compute_onboarding_results", "re", "typing_extensions"], "hash": "6aa0b33500b77bf96a01f50f70ae006195d7c933", "id": "mobility.tests.use_cases.test_classify_mode_shift_readiness", "ignore_all": false, "interface_hash": "d20f567ec7583e063d59286e6439aed98c9815e6", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_classify_mode_shift_readiness.py", "plugin_data": null, "size": 51520, "suppressed": [], "version_id": "1.16.1"}
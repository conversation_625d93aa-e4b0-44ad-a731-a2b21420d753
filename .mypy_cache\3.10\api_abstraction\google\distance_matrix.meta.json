{"data_mtime": 1753176776, "dep_lines": [5, 6, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["api_abstraction.api.travel_time_api", "api_abstraction.google.base_google_api", "mobility.ir.transport", "typing", "builtins", "_frozen_importlib", "abc", "api_abstraction.api", "api_abstraction.api.api", "datetime", "enum", "mobility", "mobility.ir", "mobility.ir.geo_study", "typing_extensions"], "hash": "d90e56e4374a3641217be857657d67df8b552a67", "id": "api_abstraction.google.distance_matrix", "ignore_all": false, "interface_hash": "1808f3119a5366aa96c8eaaa1db109918f79380d", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\google\\distance_matrix.py", "plugin_data": null, "size": 4120, "suppressed": ["googlemaps"], "version_id": "1.16.1"}
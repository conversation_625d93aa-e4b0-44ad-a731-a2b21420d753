{".class": "MypyFile", "_fullname": "mobility.repositories.abstract_repositories", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractApiRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["is_valid_key", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractApiRepository", "name": "AbstractApiRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractApiRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractApiRepository", "builtins.object"], "names": {".class": "SymbolTable", "is_valid_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "api_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractApiRepository.is_valid_key", "name": "is_valid_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "api_key"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractApiRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_valid_key of AbstractApiRepository", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractApiRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractApiRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractConfigRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_config", 2], ["get_json", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractConfigRepository", "name": "AbstractConfigRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractConfigRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractConfigRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractConfigRepository.get_config", "name": "get_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "key"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractConfigRepository", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_config of AbstractConfigRepository", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractConfigRepository.get_json", "name": "get_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "key"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractConfigRepository", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_json of AbstractConfigRepository", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractConfigRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractConfigRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractGTFSRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_agencies", 2], ["get_route", 2], ["get_routes", 2], ["get_service", 2], ["get_services", 2], ["get_stop", 2], ["get_trip_stop_times", 2], ["get_trips", 2], ["save_agency", 2], ["save_full_trip", 2], ["save_route", 2], ["save_service", 2], ["save_stop", 2], ["save_stop_time", 2], ["save_trip", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository", "name": "AbstractGTFSRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_agencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_agencies", "name": "get_agencies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_agencies of AbstractGTFSRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.gtfs.GTFSAgency"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_route", "name": "get_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_route of AbstractGTFSRepository", "ret_type": {".class": "UnionType", "items": ["mobility.ir.gtfs.GTFSRoute", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_routes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_routes", "name": "get_routes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_routes of AbstractGTFSRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.gtfs.GTFSRoute"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_service", "name": "get_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_service of AbstractGTFSRepository", "ret_type": {".class": "UnionType", "items": ["mobility.ir.gtfs.GTFSService", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_services": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_services", "name": "get_services", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_services of AbstractGTFSRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.gtfs.GTFSService"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_stop", "name": "get_stop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uuid"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_stop of AbstractGTFSRepository", "ret_type": {".class": "UnionType", "items": ["mobility.ir.gtfs.GTFSStop", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_trip_stop_times": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_trip_stop_times", "name": "get_trip_stop_times", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSTrip"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_trip_stop_times of AbstractGTFSRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.gtfs.GTFSStopTime"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_trips": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.get_trips", "name": "get_trips", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_trips of AbstractGTFSRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.gtfs.GTFSTrip"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_agency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "agency"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_agency", "name": "save_agency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "agency"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSAgency"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_agency of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_full_trip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_full_trip", "name": "save_full_trip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSFullTrip"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_full_trip of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "route"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_route", "name": "save_route", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "route"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSRoute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_route of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "service"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_service", "name": "save_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "service"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSService"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_service of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_stop", "name": "save_stop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stop"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSStop"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_stop of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_stop_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "stop_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_stop_time", "name": "save_stop_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stop_time"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSStopTime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_stop_time of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_trip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.save_trip", "name": "save_trip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "trip"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGTFSRepository", "mobility.ir.gtfs.GTFSTrip"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_trip of AbstractGTFSRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractGTFSRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractGTFSRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractGeocoderAndTerritoriesAndTravelTimerRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_cities_buildings", 2], ["get_geocoder", 2], ["get_territories_dir", 2], ["get_travel_timer", 2], ["get_zfe_calendar", 2]], "alt_promote": null, "bases": ["mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesAndTravelTimerRepository", "name": "AbstractGeocoderAndTerritoriesAndTravelTimerRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesAndTravelTimerRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesAndTravelTimerRepository", "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesAndTravelTimerRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesAndTravelTimerRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractGeocoderAndTerritoriesRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_cities_buildings", 2], ["get_geocoder", 2], ["get_territories_dir", 2], ["get_zfe_calendar", 2]], "alt_promote": null, "bases": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractGeocoderRepository"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesRepository", "name": "AbstractGeocoderAndTerritoriesRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractGeocoderAndTerritoriesRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractGeocoderRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_geocoder", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "name": "AbstractGeocoderRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_geocoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderRepository.get_geocoder", "name": "get_geocoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractGeocoderRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_geocoder of AbstractGeocoderRepository", "ret_type": "api_abstraction.api.geocode_api.GeocodeApi", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractGeocoderTerritoriesTravelTimerApiRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["cache_company_potential", 2], ["get_cached_company_potential", 2], ["get_cities_buildings", 2], ["get_geocoder", 2], ["get_territories_dir", 2], ["get_travel_timer", 2], ["get_zfe_calendar", 2], ["is_valid_key", 2]], "alt_promote": null, "bases": ["mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "mobility.repositories.abstract_repositories.AbstractApiRepository", "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderTerritoriesTravelTimerApiRepository", "name": "AbstractGeocoderTerritoriesTravelTimerApiRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderTerritoriesTravelTimerApiRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractGeocoderTerritoriesTravelTimerApiRepository", "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "mobility.repositories.abstract_repositories.AbstractApiRepository", "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractGeocoderTerritoriesTravelTimerApiRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractGeocoderTerritoriesTravelTimerApiRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractModeShiftReadinessAppRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["compute_study_stats", 2], ["current_user_has_recorded_result", 2], ["get_current_form_step", 2], ["get_geocoder", 2], ["get_new_form_id_for_current_user", 2], ["record_form_step", 2], ["record_study_result", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "name": "AbstractModeShiftReadinessAppRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "builtins.object"], "names": {".class": "SymbolTable", "compute_study_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.compute_study_stats", "name": "compute_study_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_study_stats of AbstractModeShiftReadinessAppRepository", "ret_type": "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessStatistics", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_user_has_recorded_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.current_user_has_recorded_result", "name": "current_user_has_recorded_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_user_has_recorded_result of AbstractModeShiftReadinessAppRepository", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_form_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.get_current_form_step", "name": "get_current_form_step", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_current_form_step of AbstractModeShiftReadinessAppRepository", "ret_type": {".class": "UnionType", "items": ["mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessFormStep", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_geocoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.get_geocoder", "name": "get_geocoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_geocoder of AbstractModeShiftReadinessAppRepository", "ret_type": "api_abstraction.api.geocode_api.GeocodeApi", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_new_form_id_for_current_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.get_new_form_id_for_current_user", "name": "get_new_form_id_for_current_user", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_new_form_id_for_current_user of AbstractModeShiftReadinessAppRepository", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_form_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.record_form_step", "name": "record_form_step", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "step"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessFormStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "record_form_step of AbstractModeShiftReadinessAppRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_study_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.record_study_result", "name": "record_study_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "result"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessRequest", "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessResult"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "record_study_result of AbstractModeShiftReadinessAppRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractModeShiftReadinessAppRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractOnboardingAppRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_cached_company_potential_by_axis", 2], ["get_current_onboarding_form_step", 2], ["get_geocoder", 2], ["record_onboarding_form_step", 2]], "alt_promote": null, "bases": ["mobility.repositories.abstract_repositories.AbstractGeocoderRepository"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "name": "AbstractOnboardingAppRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "mobility.repositories.abstract_repositories.AbstractGeocoderRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_cached_company_potential_by_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository.get_cached_company_potential_by_axis", "name": "get_cached_company_potential_by_axis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cached_company_potential_by_axis of AbstractOnboardingAppRepository", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["mobility.ir.indicators.ideal_scenario_indicator.DevelopmentAxis", "mobility.ir.company_potential.CompanyPotential"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_onboarding_form_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository.get_current_onboarding_form_step", "name": "get_current_onboarding_form_step", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_current_onboarding_form_step of AbstractOnboardingAppRepository", "ret_type": {".class": "UnionType", "items": ["mobility.ir.onboarding_form_step.OnboardingStep", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_onboarding_form_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository.record_onboarding_form_step", "name": "record_onboarding_form_step", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "step"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "mobility.ir.onboarding_form_step.OnboardingStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "record_onboarding_form_step of AbstractOnboardingAppRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractOnboardingAppRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractQueryRecordRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["count_sessions_reaching_category", 2], ["get_config", 2], ["get_json", 2], ["get_queries", 2], ["record_query", 2]], "alt_promote": null, "bases": ["mobility.repositories.abstract_repositories.AbstractConfigRepository"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", "name": "AbstractQueryRecordRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", "mobility.repositories.abstract_repositories.AbstractConfigRepository", "builtins.object"], "names": {".class": "SymbolTable", "count_sessions_reaching_category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository.count_sessions_reaching_category", "name": "count_sessions_reaching_category", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_sessions_reaching_category of AbstractQueryRecordRepository", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_queries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "session_uuid", "category", "sub_category", "page_num", "page_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository.get_queries", "name": "get_queries", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "session_uuid", "category", "sub_category", "page_num", "page_size"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_queries of AbstractQueryRecordRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.query_record.QueryRecord"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "category", "sub_category", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository.record_query", "name": "record_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "category", "sub_category", "content"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "record_query of AbstractQueryRecordRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractQueryRecordRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractTerritoriesRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_cities_buildings", 2], ["get_territories_dir", 2], ["get_zfe_calendar", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "name": "AbstractTerritoriesRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_cities_buildings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository.get_cities_buildings", "name": "get_cities_buildings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cities_buildings of AbstractTerritoriesRepository", "ret_type": {".class": "Instance", "args": ["mobility.ir.geo_study.Address"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_territories_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository.get_territories_dir", "name": "get_territories_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_territories_dir of AbstractTerritoriesRepository", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_zfe_calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository.get_zfe_calendar", "name": "get_zfe_calendar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "zfe_name"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_zfe_calendar of AbstractTerritoriesRepository", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractTerritoryCacheRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["cache_company_potential", 2], ["get_cached_company_potential", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "name": "AbstractTerritoryCacheRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "builtins.object"], "names": {".class": "SymbolTable", "cache_company_potential": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "citycode", "nb_employees", "company_potential"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository.cache_company_potential", "name": "cache_company_potential", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "citycode", "nb_employees", "company_potential"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "builtins.str", "builtins.int", "mobility.ir.company_potential.CompanyPotential"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cache_company_potential of AbstractTerritoryCacheRepository", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cached_company_potential": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository.get_cached_company_potential", "name": "get_cached_company_potential", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cached_company_potential of AbstractTerritoryCacheRepository", "ret_type": {".class": "Instance", "args": ["builtins.int", "mobility.ir.company_potential.CompanyPotential"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractTerritoryCacheRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractTravelTimerRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_travel_timer", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "name": "AbstractTravelTimerRepository", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mobility.repositories.abstract_repositories", "mro": ["mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "builtins.object"], "names": {".class": "SymbolTable", "get_travel_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository.get_travel_timer", "name": "get_travel_timer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTravelTimerRepository"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_travel_timer of AbstractTravelTimerRepository", "ret_type": "api_abstraction.api.travel_time_api.TravelTimeApi", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.repositories.abstract_repositories.AbstractTravelTimerRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Address": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Address", "kind": "Gdef"}, "CompanyPotential": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.company_potential.CompanyPotential", "kind": "Gdef"}, "DevelopmentAxis": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.indicators.ideal_scenario_indicator.DevelopmentAxis", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GTFSAgency": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSAgency", "kind": "Gdef"}, "GTFSFullTrip": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSFullTrip", "kind": "Gdef"}, "GTFSRoute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSRoute", "kind": "Gdef"}, "GTFSService": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSService", "kind": "Gdef"}, "GTFSStop": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSStop", "kind": "Gdef"}, "GTFSStopTime": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSStopTime", "kind": "Gdef"}, "GTFSTrip": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.gtfs.GTFSTrip", "kind": "Gdef"}, "GeocodeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.geocode_api.GeocodeApi", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModeShiftReadinessFormStep": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessFormStep", "kind": "Gdef"}, "ModeShiftReadinessRequest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessRequest", "kind": "Gdef"}, "ModeShiftReadinessResult": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessResult", "kind": "Gdef"}, "ModeShiftReadinessStatistics": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_shift_readiness_classification.ModeShiftReadinessStatistics", "kind": "Gdef"}, "OnboardingStep": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.onboarding_form_step.OnboardingStep", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "QueryRecord": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.query_record.QueryRecord", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "ZFECalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.zfe.ZFECalendar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.repositories.abstract_repositories.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}}, "path": "mobility\\repositories\\abstract_repositories.py"}
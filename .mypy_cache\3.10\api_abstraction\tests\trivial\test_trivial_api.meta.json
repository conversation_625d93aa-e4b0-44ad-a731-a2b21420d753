{"data_mtime": 1751444413, "dep_lines": [1, 2, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.trivial.trivial_travel_time", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "builtins", "_frozen_importlib", "abc", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "api_abstraction.trivial", "enum", "functools", "mobility", "mobility.ir", "mobility.ir.country", "types", "typing", "typing_extensions"], "hash": "51af5eafc4434e191197c1bf04052edb8562558c", "id": "api_abstraction.tests.trivial.test_trivial_api", "ignore_all": false, "interface_hash": "9c2a493992b4b4f067824554552f2c6ad48fd4a0", "mtime": 1748435570, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\trivial\\test_trivial_api.py", "plugin_data": null, "size": 3611, "suppressed": [], "version_id": "1.16.1"}
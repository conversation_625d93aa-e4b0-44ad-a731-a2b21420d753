{"data_mtime": 1752154451, "dep_lines": [7, 8, 9, 12, 3, 10, 11, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.study", "mobility.ir.territory", "mobility.serializers.report_serializer", "unittest.mock", "mobility.quantity", "mobility.serializers", "os", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "abc", "enum", "mobility.ir", "mobility.ir.country", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.map_elements", "mobility.ir.new_indicators", "types", "typing_extensions", "unittest"], "hash": "0d3cb2fa26ad5c633e276c77755198e93fd7c6af", "id": "mobility.tests.serializers.test_report_serializer", "ignore_all": false, "interface_hash": "be1fee630ee108a8c451bb668b408a739c174e5d", "mtime": 1747931728, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\serializers\\test_report_serializer.py", "plugin_data": null, "size": 3633, "suppressed": [], "version_id": "1.16.1"}
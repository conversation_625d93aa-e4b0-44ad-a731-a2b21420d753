{"data_mtime": 1751444412, "dep_lines": [5, 4, 8, 9, 10, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.bicycle_infrastructure_indicators", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.study_types", "mobility.ir.transport", "unittest.mock", "dataclasses", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.indicators", "mobility.ir.site", "mobility.ir.study", "typing", "typing_extensions", "unittest"], "hash": "06a41af2b401cf5b36dcd98d13167fdcfafc7231", "id": "mobility.tests.ir.indicators.test_bicycle_infrastructure_indicators", "ignore_all": false, "interface_hash": "ed2d5f149da1c5dbf799dd28f84afe0d040e1be2", "mtime": 1725446374, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\indicators\\test_bicycle_infrastructure_indicators.py", "plugin_data": null, "size": 10176, "suppressed": [], "version_id": "1.16.1"}
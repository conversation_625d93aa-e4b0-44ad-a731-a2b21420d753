{"data_mtime": 1751444431, "dep_lines": [5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.carpool_indicators", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.python_api", "abc", "mobility.funky", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.site"], "hash": "fc35d9aee9c76c9397ec73b4b0a7648262c740bb", "id": "mobility.tests.ir.indicators.test_carpool_indicators", "ignore_all": false, "interface_hash": "35d419c7e6c26f5f38b1d63b4ad4681c725f5805", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\indicators\\test_carpool_indicators.py", "plugin_data": null, "size": 2344, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1753176776, "dep_lines": [9, 10, 11, 12, 13, 14, 21, 22, 31, 32, 20, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.date_iterators", "api_abstraction.api.event_reporter", "api_abstraction.api.hypotheses", "api_abstraction.api.travel_time_api", "api_abstraction.here.here_api_clients", "mobility.ir.geo_study", "mobility.ir.here", "mobility.ir.territory", "mobility.ir.transport", "mobility.constants", "json", "logging", "datetime", "typing", "builtins", "_frozen_importlib", "abc", "api_abstraction.api", "api_abstraction.here.base_here_api", "enum", "json.decoder", "mobility", "mobility.ir", "types", "typing_extensions"], "hash": "82749c259fa429c62fcbf7a74738cea4b710384f", "id": "api_abstraction.here.api", "ignore_all": false, "interface_hash": "dd43e7efe73c68ca27ef5d7281af1ad164072a6e", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\here\\api.py", "plugin_data": null, "size": 6160, "suppressed": ["shapely.geometry.base", "geopandas"], "version_id": "1.16.1"}
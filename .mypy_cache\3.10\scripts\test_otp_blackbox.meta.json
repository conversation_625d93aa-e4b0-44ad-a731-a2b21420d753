{"data_mtime": 1751444410, "dep_lines": [6, 7, 8, 10, 9, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.otp.api", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.quantity", "dataclasses", "collections", "itertools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.otp", "enum", "fractions", "functools", "io", "mobility", "mobility.ir", "mobility.workers", "numbers", "os", "typing_extensions"], "hash": "cc3ce0cc4e189cf2575f26a59c91a1c05cb4f6d8", "id": "scripts.test_otp_blackbox", "ignore_all": false, "interface_hash": "ecac0e4d66a339bbdb7d8d38f16a7ec9d7df4ff2", "mtime": 1740385612, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\test_otp_blackbox.py", "plugin_data": null, "size": 4834, "suppressed": [], "version_id": "1.16.1"}
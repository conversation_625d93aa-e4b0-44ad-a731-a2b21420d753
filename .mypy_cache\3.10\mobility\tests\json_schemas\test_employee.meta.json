{"data_mtime": 1751444429, "dep_lines": [3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.transport", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "abc", "enum", "mobility.ir", "typing", "typing_extensions"], "hash": "8d12635e5cd2cc440dbb097484f6087e41f36c57", "id": "mobility.tests.json_schemas.test_employee", "ignore_all": false, "interface_hash": "ad47274a61652d9685f81c68a6a7bf02679425b0", "mtime": 1748436326, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\json_schemas\\test_employee.py", "plugin_data": null, "size": 3757, "suppressed": [], "version_id": "1.16.1"}
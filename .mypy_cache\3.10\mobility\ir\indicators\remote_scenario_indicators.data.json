{".class": "MypyFile", "_fullname": "mobility.ir.indicators.remote_scenario_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommuterProfile": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.CommuterProfile", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IndividualCosts": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.IndividualCosts", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "RemoteScenarioIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators", "name": "RemoteScenarioIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "nickname", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 15, "name": "min_bound", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 16, "name": "average_travel_time", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 17, "name": "remote_workers_average_travel_time", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "carbon_emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "remote_workers", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "non_remote_workers", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 21, "name": "nb_remote_workers", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 22, "name": "employees_count_per_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 23, "name": "employees_count_per_profile", "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 24, "name": "remote_employees_count_per_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "remote_workers_travel_time_saved_per_remote_day", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "remote_workers_travel_time_saved", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 27, "name": "remote_workers_annual_distance_saved", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "remote_drivers_annual_distance_saved", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "percent_diff_emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "costs", "type": "mobility.ir.cost.IndividualCosts"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "zfe_impact_calendar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.remote_scenario_indicators", "mro": ["mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "min_bound", "average_travel_time", "remote_workers_average_travel_time", "carbon_emission", "remote_workers", "non_remote_workers", "nb_remote_workers", "employees_count_per_mode", "employees_count_per_profile", "remote_employees_count_per_mode", "remote_workers_travel_time_saved_per_remote_day", "remote_workers_travel_time_saved", "remote_workers_annual_distance_saved", "remote_drivers_annual_distance_saved", "emission", "percent_diff_emission", "costs", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "min_bound", "average_travel_time", "remote_workers_average_travel_time", "carbon_emission", "remote_workers", "non_remote_workers", "nb_remote_workers", "employees_count_per_mode", "employees_count_per_profile", "remote_employees_count_per_mode", "remote_workers_travel_time_saved_per_remote_day", "remote_workers_travel_time_saved", "remote_workers_annual_distance_saved", "remote_drivers_annual_distance_saved", "emission", "percent_diff_emission", "costs", "zfe_impact_calendar"], "arg_types": ["mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators", "builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RemoteScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nickname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min_bound"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_workers_average_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbon_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "non_remote_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_remote_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_profile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_employees_count_per_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_workers_travel_time_saved_per_remote_day"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_workers_travel_time_saved"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_workers_annual_distance_saved"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_drivers_annual_distance_saved"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "percent_diff_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "costs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zfe_impact_calendar"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "min_bound", "average_travel_time", "remote_workers_average_travel_time", "carbon_emission", "remote_workers", "non_remote_workers", "nb_remote_workers", "employees_count_per_mode", "employees_count_per_profile", "remote_employees_count_per_mode", "remote_workers_travel_time_saved_per_remote_day", "remote_workers_travel_time_saved", "remote_workers_annual_distance_saved", "remote_drivers_annual_distance_saved", "emission", "percent_diff_emission", "costs", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "min_bound", "average_travel_time", "remote_workers_average_travel_time", "carbon_emission", "remote_workers", "non_remote_workers", "nb_remote_workers", "employees_count_per_mode", "employees_count_per_profile", "remote_employees_count_per_mode", "remote_workers_travel_time_saved_per_remote_day", "remote_workers_travel_time_saved", "remote_workers_annual_distance_saved", "remote_drivers_annual_distance_saved", "emission", "percent_diff_emission", "costs", "zfe_impact_calendar"], "arg_types": ["builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RemoteScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "min_bound", "average_travel_time", "remote_workers_average_travel_time", "carbon_emission", "remote_workers", "non_remote_workers", "nb_remote_workers", "employees_count_per_mode", "employees_count_per_profile", "remote_employees_count_per_mode", "remote_workers_travel_time_saved_per_remote_day", "remote_workers_travel_time_saved", "remote_workers_annual_distance_saved", "remote_drivers_annual_distance_saved", "emission", "percent_diff_emission", "costs", "zfe_impact_calendar"], "arg_types": ["builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RemoteScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "average_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.average_travel_time", "name": "average_travel_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "carbon_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.carbon_emission", "name": "carbon_emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "costs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.costs", "name": "costs", "setter_type": null, "type": "mobility.ir.cost.IndividualCosts"}}, "emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.emission", "name": "emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "employees_count_per_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.employees_count_per_mode", "name": "employees_count_per_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_count_per_profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.employees_count_per_profile", "name": "employees_count_per_profile", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "min_bound": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.min_bound", "name": "min_bound", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "nb_remote_workers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.nb_remote_workers", "name": "nb_remote_workers", "setter_type": null, "type": "builtins.int"}}, "nickname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.nickname", "name": "nickname", "setter_type": null, "type": "builtins.str"}}, "non_remote_workers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.non_remote_workers", "name": "non_remote_workers", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "percent_diff_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.percent_diff_emission", "name": "percent_diff_emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "remote_drivers_annual_distance_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_drivers_annual_distance_saved", "name": "remote_drivers_annual_distance_saved", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "remote_employees_count_per_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_employees_count_per_mode", "name": "remote_employees_count_per_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "remote_workers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_workers", "name": "remote_workers", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "remote_workers_annual_distance_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_workers_annual_distance_saved", "name": "remote_workers_annual_distance_saved", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "remote_workers_average_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_workers_average_travel_time", "name": "remote_workers_average_travel_time", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "remote_workers_travel_time_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_workers_travel_time_saved", "name": "remote_workers_travel_time_saved", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "remote_workers_travel_time_saved_per_remote_day": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.remote_workers_travel_time_saved_per_remote_day", "name": "remote_workers_travel_time_saved_per_remote_day", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "zfe_impact_calendar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.zfe_impact_calendar", "name": "zfe_impact_calendar", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.remote_scenario_indicators.RemoteScenarioIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "ZFEImpactCalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.remote_scenario_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}}, "path": "mobility\\ir\\indicators\\remote_scenario_indicators.py"}
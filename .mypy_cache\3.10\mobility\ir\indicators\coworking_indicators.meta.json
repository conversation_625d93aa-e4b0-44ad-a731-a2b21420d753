{"data_mtime": 1753087663, "dep_lines": [6, 8, 9, 10, 11, 12, 17, 18, 20, 21, 7, 19, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.scenario", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "fractions", "mobility.converters", "mobility.converters.indicators", "mobility.ir.geo_study", "mobility.ir.scenario_data", "mobility.workers", "mobility.workers.health_cost_calculator", "numbers", "typing_extensions"], "hash": "abed4578a4a41945afda1021e9c766dc64957efb", "id": "mobility.ir.indicators.coworking_indicators", "ignore_all": false, "interface_hash": "d426220669fa0da14236e9ce5ea9ccf37fa82359", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\coworking_indicators.py", "plugin_data": null, "size": 11778, "suppressed": [], "version_id": "1.16.1"}
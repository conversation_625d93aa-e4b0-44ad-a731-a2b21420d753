from dataclasses import dataclass
from typing import Optional, Union

from mobility.constants import NOT_APPLICABLE
from mobility.funky import ImmutableDict
from mobility.ir.error import Error
from mobility.ir.geo_study import Address
from mobility.ir.indicators.indicators import Indicators
from mobility.ir.indicators.scenario_indicators import (
    convert_carbon_emission_gec_per_commute_to_teq_per_year,
)
from mobility.ir.study import (
    FailedGeoEmployee,
    FailedGeoSite,
    GeoEmployee,
    GeoSite,
    ModalCommuteData,
    ScenarioData,
)
from mobility.ir.transport import Distance, Duration, Emission, TransportMode


def _serialize_transport_mode(mode: Optional[TransportMode]) -> str:
    return {
        TransportMode.WALK: "Marche à pied",
        TransportMode.PUBLIC_TRANSPORT: "Transport en commun",
        TransportMode.CAR: "Voiture",
        TransportMode.BICYCLE: "Vélo",
        TransportMode.CARPOOLING: "Covoiturage",
        TransportMode.ELECTRIC_BICYCLE: "Vélo électrique",
        TransportMode.ELECTRIC_CAR: "Voiture électrique",
        TransportMode.MOTORCYCLE: "Moto",
        TransportMode.AIRPLANE: "Avion",
        TransportMode.ELECTRIC_MOTORCYCLE: "Moto électrique",
        TransportMode.FAST_BICYCLE: "Vélo électrique rapide",
        TransportMode.CAR_PUBLIC_TRANSPORT: "Intermodal Voiture/TC",
        TransportMode.BICYCLE_PUBLIC_TRANSPORT: "Intermodal Vélo/TC",
        None: NOT_APPLICABLE,
    }.get(mode, str(mode))


def _serialize_duration(
    mode: Optional[TransportMode], duration: Duration
) -> Union[int, str]:
    return NOT_APPLICABLE if mode is None else duration.get(mode, NOT_APPLICABLE)


def _serialize_distance(
    mode: Optional[TransportMode], distance: Distance
) -> Union[int, str]:
    return NOT_APPLICABLE if mode is None else distance.get(mode, NOT_APPLICABLE)


def _serialize_emission(
    mode: Optional[TransportMode], emission: Emission
) -> Union[int, str]:
    return NOT_APPLICABLE if mode is None else emission.get(mode, NOT_APPLICABLE)


def _serialize_yearly_emission(
    mode: Optional[TransportMode], emission: Emission
) -> Union[float, str]:
    emissions = _serialize_emission(mode, emission)
    if isinstance(emissions, int):
        return convert_carbon_emission_gec_per_commute_to_teq_per_year(emissions)
    else:
        return emissions


def _serialize_scenario_belonging(belong_to_scenario: bool) -> str:
    return "oui" if belong_to_scenario else "non"


def _serialize_cluster_id(cluster_id: Union[bool, int]) -> Union[int, str]:
    return NOT_APPLICABLE if cluster_id is False else cluster_id


def _serialize_address(address: Union[bool, Address]) -> str:
    if isinstance(address, Address):
        return f"{address.city} ({address.postcode})"
    else:
        return NOT_APPLICABLE


def _serialize_complete_address(address: Union[bool, Address]) -> str:
    if isinstance(address, Address):
        return f"{address.normalized}"
    else:
        return NOT_APPLICABLE


def _serialize_error(error: Error) -> str:
    kind = error.kind.replace(">", "&gt;")
    description = error.description
    return f"Error <{kind}>: {description}".replace(",", ";")


@dataclass(eq=True, frozen=True)
class ExcelOutCommute:
    employee_and_scenario_id: str
    scenario_id: int
    scenario_name: str
    employee_id: int
    employee_name: str
    employee_address: str
    employee_geo_address: str
    transport_mode_supplied: str
    transport_mode_selected: str
    time_transport_mode_supplied: Union[int, str]
    time_transport_mode_selected: Union[int, str]
    time_walk: Union[int, str]
    time_pt: Union[int, str]
    time_car: Union[int, str]
    time_bicycle: Union[int, str]
    site_id: int
    site_name: str
    site_address: str
    site_geo_address: str
    critical_cases: int
    error_code: int
    distance_transport_mode_supplied: Union[int, str]
    distance_transport_mode_selected: Union[int, str]
    distance_walk: Union[int, str]
    distance_pt: Union[int, str]
    distance_car: Union[int, str]
    distance_bicycle: Union[int, str]
    emission_transport_mode_supplied: Union[int, str]
    emission_yearly_transport_mode_supplied: Union[float, str]
    emission_transport_mode_selected: Union[int, str]
    emission_yearly_transport_mode_selected: Union[float, str]
    emission_walk: Union[int, str]
    emission_yearly_walk: Union[float, str]
    emission_pt: Union[int, str]
    emission_yearly_pt: Union[float, str]
    emission_car: Union[int, str]
    emission_yearly_car: Union[float, str]
    emission_bicycle: Union[int, str]
    emission_yearly_bicycle: Union[float, str]
    bicycle_modal_shift_scenario_1: str
    bicycle_modal_shift_scenario_2: str
    pt_modal_shift_scenario_1: str
    pt_modal_shift_scenario_2: str
    remote_worker_scenario_1: str
    remote_worker_scenario_2: str
    remote_worker_scenario_3: str
    remote_worker_scenario_4: str
    time_8_00_transport_mode_selected: Union[int, str]
    time_9_00_transport_mode_selected: Union[int, str]
    time_9_30_transport_mode_selected: Union[int, str]
    time_10_00_transport_mode_selected: Union[int, str]
    carpool_cluster_id: Union[int, str]
    cluster_id: Union[int, str]
    cluster_center_address: str
    coworking_name: str
    coworking_address: str
    coworking_transport_mode: str
    coworking_time: Union[int, str]
    coworking_emission: Union[int, str]
    coworking_yearly_emission: Union[float, str]
    coworking_distance: Union[int, str]

    @classmethod
    def from_modal_commute(
        cls,
        scenario_data: ScenarioData,
        employee: GeoEmployee,
        site: GeoSite,
        commute_data: ModalCommuteData,
        critical_case: int,
        indicators: Indicators,
    ) -> "ExcelOutCommute":
        bike_shift_scenarios = indicators.present_scenario.get_bike_shift_scenarios()
        pt_shift_scenarios = (
            indicators.present_scenario.get_public_transport_shift_scenarios()
        )
        remote_scenarios = indicators.present_scenario.get_remote_scenarios()
        carpool_cluster_id = indicators.present_scenario.carpool_indicators.carpool_group_id_by_employee.get(
            employee, False
        )
        if (
            employee
            in indicators.present_scenario.clustering_indicators.employee_cluster_id
        ):
            cluster_id: Union[bool, int] = (
                indicators.present_scenario.clustering_indicators.employee_cluster_id[
                    employee
                ]
            )
            cluster_center_address: Union[bool, Address] = (
                indicators.present_scenario.clustering_indicators.clusters[
                    cluster_id
                ].center_address
            )
        else:
            cluster_id = False
            cluster_center_address = False
        time_8_00 = cls.get_commute_alternative_duration(commute_data, employee, 8, 0)
        time_9_00 = cls.get_commute_alternative_duration(commute_data, employee, 9, 0)
        time_9_30 = cls.get_commute_alternative_duration(commute_data, employee, 9, 30)
        time_10_00 = cls.get_commute_alternative_duration(commute_data, employee, 10, 0)

        coworking_indicators = indicators.present_scenario.coworking_indicators
        coworking_site = coworking_indicators.coworkers_sites.get(employee)
        coworking_commute = coworking_indicators.coworkers_commutes.get(employee)
        if coworking_site is not None:
            coworking_name = coworking_site.name
            coworking_address: Union[bool, Address] = coworking_site.address
        else:
            coworking_name = NOT_APPLICABLE
            coworking_address = False

        if coworking_commute is not None:
            coworking_transport_mode = _serialize_transport_mode(
                coworking_commute.best_mode
            )
            coworking_time = _serialize_duration(
                coworking_commute.best_mode, coworking_commute.duration
            )
            coworking_emission = _serialize_emission(
                coworking_commute.best_mode, coworking_commute.emission
            )
            coworking_yearly_emission = _serialize_yearly_emission(
                coworking_commute.best_mode, coworking_commute.emission
            )
            coworking_distance = _serialize_distance(
                coworking_commute.best_mode, coworking_commute.distance
            )
        else:
            coworking_transport_mode = NOT_APPLICABLE
            coworking_time = NOT_APPLICABLE
            coworking_emission = NOT_APPLICABLE
            coworking_yearly_emission = NOT_APPLICABLE
            coworking_distance = NOT_APPLICABLE

        return cls(
            employee_and_scenario_id=employee.nickname + scenario_data.nickname,
            scenario_id=scenario_data.id,
            scenario_name=scenario_data.nickname,
            employee_id=employee.id,
            employee_name=employee.nickname,
            employee_address=employee.address,
            employee_geo_address=str(employee.coordinates),
            transport_mode_supplied=_serialize_transport_mode(employee.transport_mode),
            transport_mode_selected=_serialize_transport_mode(commute_data.best_mode),
            time_transport_mode_supplied=_serialize_duration(
                employee.transport_mode, commute_data.duration
            ),
            time_transport_mode_selected=_serialize_duration(
                commute_data.best_mode, commute_data.duration
            ),
            time_walk=_serialize_duration(TransportMode.WALK, commute_data.duration),
            time_pt=_serialize_duration(
                TransportMode.PUBLIC_TRANSPORT, commute_data.duration
            ),
            time_car=_serialize_duration(TransportMode.CAR, commute_data.duration),
            time_bicycle=_serialize_duration(
                TransportMode.BICYCLE, commute_data.duration
            ),
            site_id=site.id,
            site_name=site.nickname,
            site_address=site.address,
            site_geo_address=str(site.coordinates),
            critical_cases=critical_case,
            error_code=0,
            distance_transport_mode_supplied=_serialize_distance(
                employee.transport_mode, commute_data.distance
            ),
            distance_transport_mode_selected=_serialize_distance(
                commute_data.best_mode, commute_data.distance
            ),
            distance_walk=_serialize_distance(
                TransportMode.WALK, commute_data.distance
            ),
            distance_pt=_serialize_distance(
                TransportMode.PUBLIC_TRANSPORT, commute_data.distance
            ),
            distance_car=_serialize_distance(TransportMode.CAR, commute_data.distance),
            distance_bicycle=_serialize_distance(
                TransportMode.BICYCLE, commute_data.distance
            ),
            emission_transport_mode_supplied=_serialize_emission(
                employee.transport_mode, commute_data.emission
            ),
            emission_yearly_transport_mode_supplied=_serialize_yearly_emission(
                employee.transport_mode, commute_data.emission
            ),
            emission_transport_mode_selected=_serialize_emission(
                commute_data.best_mode, commute_data.emission
            ),
            emission_yearly_transport_mode_selected=_serialize_yearly_emission(
                commute_data.best_mode, commute_data.emission
            ),
            emission_walk=_serialize_emission(
                TransportMode.WALK, commute_data.emission
            ),
            emission_yearly_walk=_serialize_yearly_emission(
                TransportMode.WALK, commute_data.emission
            ),
            emission_pt=_serialize_emission(
                TransportMode.PUBLIC_TRANSPORT, commute_data.emission
            ),
            emission_yearly_pt=_serialize_yearly_emission(
                TransportMode.PUBLIC_TRANSPORT, commute_data.emission
            ),
            emission_car=_serialize_emission(TransportMode.CAR, commute_data.emission),
            emission_yearly_car=_serialize_yearly_emission(
                TransportMode.CAR, commute_data.emission
            ),
            emission_bicycle=_serialize_emission(
                TransportMode.BICYCLE, commute_data.emission
            ),
            emission_yearly_bicycle=_serialize_yearly_emission(
                TransportMode.BICYCLE, commute_data.emission
            ),
            bicycle_modal_shift_scenario_1=_serialize_scenario_belonging(
                employee in bike_shift_scenarios[0].shifter_employees
            ),
            bicycle_modal_shift_scenario_2=_serialize_scenario_belonging(
                employee in bike_shift_scenarios[1].shifter_employees
            ),
            pt_modal_shift_scenario_1=_serialize_scenario_belonging(
                employee in pt_shift_scenarios[0].shifter_employees
            ),
            pt_modal_shift_scenario_2=_serialize_scenario_belonging(
                employee in pt_shift_scenarios[1].shifter_employees
            ),
            remote_worker_scenario_1=_serialize_scenario_belonging(
                employee in remote_scenarios[0].remote_workers,
            ),
            remote_worker_scenario_2=_serialize_scenario_belonging(
                employee in remote_scenarios[1].remote_workers,
            ),
            remote_worker_scenario_3=_serialize_scenario_belonging(
                employee in remote_scenarios[2].remote_workers,
            ),
            remote_worker_scenario_4=_serialize_scenario_belonging(
                employee in remote_scenarios[3].remote_workers,
            ),
            time_8_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode, time_8_00
            ),
            time_9_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_9_00,
            ),
            time_9_30_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_9_30,
            ),
            time_10_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_10_00,
            ),
            carpool_cluster_id=_serialize_cluster_id(carpool_cluster_id),
            cluster_id=_serialize_cluster_id(cluster_id),
            cluster_center_address=_serialize_address(cluster_center_address),
            coworking_name=coworking_name,
            coworking_address=_serialize_complete_address(coworking_address),
            coworking_transport_mode=coworking_transport_mode,
            coworking_time=coworking_time,
            coworking_emission=coworking_emission,
            coworking_yearly_emission=coworking_yearly_emission,
            coworking_distance=coworking_distance,
        )

    @classmethod
    def from_time_failed_commute(
        cls,
        scenario_data: ScenarioData,
        employee: GeoEmployee,
        site: GeoSite,
        commute_data: ModalCommuteData,
    ) -> "ExcelOutCommute":
        time_8_00 = cls.get_commute_alternative_duration(commute_data, employee, 8, 0)
        time_9_00 = cls.get_commute_alternative_duration(commute_data, employee, 9, 0)
        time_9_30 = cls.get_commute_alternative_duration(commute_data, employee, 9, 30)
        time_10_00 = cls.get_commute_alternative_duration(commute_data, employee, 10, 0)
        return cls(
            employee_and_scenario_id=employee.nickname + scenario_data.nickname,
            scenario_id=scenario_data.id,
            scenario_name=scenario_data.nickname,
            employee_id=employee.id,
            employee_name=employee.nickname,
            employee_address=employee.address,
            employee_geo_address=str(employee.coordinates),
            transport_mode_supplied=_serialize_transport_mode(employee.transport_mode),
            transport_mode_selected=_serialize_transport_mode(commute_data.best_mode),
            time_transport_mode_supplied=_serialize_duration(
                employee.transport_mode, commute_data.duration
            ),
            time_transport_mode_selected=_serialize_duration(
                commute_data.best_mode, commute_data.duration
            ),
            time_walk=_serialize_duration(TransportMode.WALK, commute_data.duration),
            time_pt=_serialize_duration(
                TransportMode.PUBLIC_TRANSPORT, commute_data.duration
            ),
            time_car=_serialize_duration(TransportMode.CAR, commute_data.duration),
            time_bicycle=_serialize_duration(
                TransportMode.BICYCLE, commute_data.duration
            ),
            site_id=site.id,
            site_name=site.nickname,
            site_address=site.address,
            site_geo_address=str(site.coordinates),
            critical_cases=0,
            error_code=2,
            distance_transport_mode_supplied=_serialize_distance(
                employee.transport_mode, commute_data.distance
            ),
            distance_transport_mode_selected=_serialize_distance(
                commute_data.best_mode, commute_data.distance
            ),
            distance_walk=_serialize_distance(
                TransportMode.WALK, commute_data.distance
            ),
            distance_pt=_serialize_distance(
                TransportMode.PUBLIC_TRANSPORT, commute_data.distance
            ),
            distance_car=_serialize_distance(TransportMode.CAR, commute_data.distance),
            distance_bicycle=_serialize_distance(
                TransportMode.BICYCLE, commute_data.distance
            ),
            emission_transport_mode_supplied=_serialize_emission(
                employee.transport_mode, commute_data.emission
            ),
            emission_yearly_transport_mode_supplied=_serialize_yearly_emission(
                employee.transport_mode, commute_data.emission
            ),
            emission_transport_mode_selected=_serialize_emission(
                commute_data.best_mode, commute_data.emission
            ),
            emission_yearly_transport_mode_selected=_serialize_yearly_emission(
                commute_data.best_mode, commute_data.emission
            ),
            emission_walk=_serialize_emission(
                TransportMode.WALK, commute_data.emission
            ),
            emission_yearly_walk=_serialize_yearly_emission(
                TransportMode.WALK, commute_data.emission
            ),
            emission_pt=_serialize_emission(
                TransportMode.PUBLIC_TRANSPORT, commute_data.emission
            ),
            emission_yearly_pt=_serialize_yearly_emission(
                TransportMode.PUBLIC_TRANSPORT, commute_data.emission
            ),
            emission_car=_serialize_emission(TransportMode.CAR, commute_data.emission),
            emission_yearly_car=_serialize_yearly_emission(
                TransportMode.CAR, commute_data.emission
            ),
            emission_bicycle=_serialize_emission(
                TransportMode.BICYCLE, commute_data.emission
            ),
            emission_yearly_bicycle=_serialize_yearly_emission(
                TransportMode.BICYCLE, commute_data.emission
            ),
            bicycle_modal_shift_scenario_1=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_2=NOT_APPLICABLE,
            pt_modal_shift_scenario_1=NOT_APPLICABLE,
            pt_modal_shift_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_1=NOT_APPLICABLE,
            remote_worker_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_3=NOT_APPLICABLE,
            remote_worker_scenario_4=NOT_APPLICABLE,
            time_8_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_8_00,
            ),
            time_9_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_9_00,
            ),
            time_9_30_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_9_30,
            ),
            time_10_00_transport_mode_selected=_serialize_duration(
                commute_data.best_mode,
                time_10_00,
            ),
            carpool_cluster_id=NOT_APPLICABLE,
            cluster_id=NOT_APPLICABLE,
            cluster_center_address=NOT_APPLICABLE,
            coworking_name=NOT_APPLICABLE,
            coworking_address=NOT_APPLICABLE,
            coworking_transport_mode=NOT_APPLICABLE,
            coworking_time=NOT_APPLICABLE,
            coworking_emission=NOT_APPLICABLE,
            coworking_yearly_emission=NOT_APPLICABLE,
            coworking_distance=NOT_APPLICABLE,
        )

    @classmethod
    def from_geocode_failed_employee(
        cls,
        scenario_data: ScenarioData,
        employee: FailedGeoEmployee,
        site: GeoSite,
        commute_data: None,
    ) -> "ExcelOutCommute":
        return cls(
            employee_and_scenario_id=employee.nickname + scenario_data.nickname,
            scenario_id=scenario_data.id,
            scenario_name=scenario_data.nickname,
            employee_id=employee.id,
            employee_name=employee.nickname,
            employee_address=employee.address,
            employee_geo_address=_serialize_error(employee.failure),
            transport_mode_supplied=_serialize_transport_mode(employee.transport_mode),
            transport_mode_selected=NOT_APPLICABLE,
            time_transport_mode_supplied=NOT_APPLICABLE,
            time_transport_mode_selected=NOT_APPLICABLE,
            time_walk=NOT_APPLICABLE,
            time_pt=NOT_APPLICABLE,
            time_car=NOT_APPLICABLE,
            time_bicycle=NOT_APPLICABLE,
            site_id=site.id,
            site_name=site.nickname,
            site_address=site.address,
            site_geo_address=str(site.coordinates),
            critical_cases=0,
            error_code=1,
            distance_transport_mode_supplied=NOT_APPLICABLE,
            distance_transport_mode_selected=NOT_APPLICABLE,
            distance_walk=NOT_APPLICABLE,
            distance_pt=NOT_APPLICABLE,
            distance_car=NOT_APPLICABLE,
            distance_bicycle=NOT_APPLICABLE,
            emission_transport_mode_supplied=NOT_APPLICABLE,
            emission_yearly_transport_mode_supplied=NOT_APPLICABLE,
            emission_transport_mode_selected=NOT_APPLICABLE,
            emission_yearly_transport_mode_selected=NOT_APPLICABLE,
            emission_walk=NOT_APPLICABLE,
            emission_yearly_walk=NOT_APPLICABLE,
            emission_pt=NOT_APPLICABLE,
            emission_yearly_pt=NOT_APPLICABLE,
            emission_car=NOT_APPLICABLE,
            emission_yearly_car=NOT_APPLICABLE,
            emission_bicycle=NOT_APPLICABLE,
            emission_yearly_bicycle=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_1=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_2=NOT_APPLICABLE,
            pt_modal_shift_scenario_1=NOT_APPLICABLE,
            pt_modal_shift_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_1=NOT_APPLICABLE,
            remote_worker_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_3=NOT_APPLICABLE,
            remote_worker_scenario_4=NOT_APPLICABLE,
            time_8_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_30_transport_mode_selected=NOT_APPLICABLE,
            time_10_00_transport_mode_selected=NOT_APPLICABLE,
            carpool_cluster_id=NOT_APPLICABLE,
            cluster_id=NOT_APPLICABLE,
            cluster_center_address=NOT_APPLICABLE,
            coworking_name=NOT_APPLICABLE,
            coworking_address=NOT_APPLICABLE,
            coworking_transport_mode=NOT_APPLICABLE,
            coworking_time=NOT_APPLICABLE,
            coworking_emission=NOT_APPLICABLE,
            coworking_yearly_emission=NOT_APPLICABLE,
            coworking_distance=NOT_APPLICABLE,
        )

    @classmethod
    def from_geocode_failed_site(
        cls,
        scenario_data: ScenarioData,
        employee: GeoEmployee,
        site: FailedGeoSite,
        commute_data: None,
    ) -> "ExcelOutCommute":
        return cls(
            employee_and_scenario_id=employee.nickname + scenario_data.nickname,
            scenario_id=scenario_data.id,
            scenario_name=scenario_data.nickname,
            employee_id=employee.id,
            employee_name=employee.nickname,
            employee_address=employee.address,
            employee_geo_address=str(employee.coordinates),
            transport_mode_supplied=_serialize_transport_mode(employee.transport_mode),
            transport_mode_selected=NOT_APPLICABLE,
            time_transport_mode_supplied=NOT_APPLICABLE,
            time_transport_mode_selected=NOT_APPLICABLE,
            time_walk=NOT_APPLICABLE,
            time_pt=NOT_APPLICABLE,
            time_car=NOT_APPLICABLE,
            time_bicycle=NOT_APPLICABLE,
            site_id=site.id,
            site_name=site.nickname,
            site_address=site.address,
            site_geo_address=_serialize_error(site.failure),
            critical_cases=0,
            error_code=1,
            distance_transport_mode_supplied=NOT_APPLICABLE,
            distance_transport_mode_selected=NOT_APPLICABLE,
            distance_walk=NOT_APPLICABLE,
            distance_pt=NOT_APPLICABLE,
            distance_car=NOT_APPLICABLE,
            distance_bicycle=NOT_APPLICABLE,
            emission_transport_mode_supplied=NOT_APPLICABLE,
            emission_yearly_transport_mode_supplied=NOT_APPLICABLE,
            emission_transport_mode_selected=NOT_APPLICABLE,
            emission_yearly_transport_mode_selected=NOT_APPLICABLE,
            emission_walk=NOT_APPLICABLE,
            emission_yearly_walk=NOT_APPLICABLE,
            emission_pt=NOT_APPLICABLE,
            emission_yearly_pt=NOT_APPLICABLE,
            emission_car=NOT_APPLICABLE,
            emission_yearly_car=NOT_APPLICABLE,
            emission_bicycle=NOT_APPLICABLE,
            emission_yearly_bicycle=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_1=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_2=NOT_APPLICABLE,
            pt_modal_shift_scenario_1=NOT_APPLICABLE,
            pt_modal_shift_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_1=NOT_APPLICABLE,
            remote_worker_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_3=NOT_APPLICABLE,
            remote_worker_scenario_4=NOT_APPLICABLE,
            time_8_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_30_transport_mode_selected=NOT_APPLICABLE,
            time_10_00_transport_mode_selected=NOT_APPLICABLE,
            carpool_cluster_id=NOT_APPLICABLE,
            cluster_id=NOT_APPLICABLE,
            cluster_center_address=NOT_APPLICABLE,
            coworking_name=NOT_APPLICABLE,
            coworking_address=NOT_APPLICABLE,
            coworking_transport_mode=NOT_APPLICABLE,
            coworking_time=NOT_APPLICABLE,
            coworking_emission=NOT_APPLICABLE,
            coworking_yearly_emission=NOT_APPLICABLE,
            coworking_distance=NOT_APPLICABLE,
        )

    @classmethod
    def from_geocode_failed_both(
        cls,
        scenario_data: ScenarioData,
        employee: FailedGeoEmployee,
        site: FailedGeoSite,
        commute_data: None,
    ) -> "ExcelOutCommute":
        return cls(
            employee_and_scenario_id=employee.nickname + scenario_data.nickname,
            scenario_id=scenario_data.id,
            scenario_name=scenario_data.nickname,
            employee_id=employee.id,
            employee_name=employee.nickname,
            employee_address=employee.address,
            employee_geo_address=_serialize_error(employee.failure),
            transport_mode_supplied=_serialize_transport_mode(employee.transport_mode),
            transport_mode_selected=NOT_APPLICABLE,
            time_transport_mode_supplied=NOT_APPLICABLE,
            time_transport_mode_selected=NOT_APPLICABLE,
            time_walk=NOT_APPLICABLE,
            time_pt=NOT_APPLICABLE,
            time_car=NOT_APPLICABLE,
            time_bicycle=NOT_APPLICABLE,
            site_id=site.id,
            site_name=site.nickname,
            site_address=site.address,
            site_geo_address=_serialize_error(site.failure),
            critical_cases=0,
            error_code=1,
            distance_transport_mode_supplied=NOT_APPLICABLE,
            distance_transport_mode_selected=NOT_APPLICABLE,
            distance_walk=NOT_APPLICABLE,
            distance_pt=NOT_APPLICABLE,
            distance_car=NOT_APPLICABLE,
            distance_bicycle=NOT_APPLICABLE,
            emission_transport_mode_supplied=NOT_APPLICABLE,
            emission_yearly_transport_mode_supplied=NOT_APPLICABLE,
            emission_transport_mode_selected=NOT_APPLICABLE,
            emission_yearly_transport_mode_selected=NOT_APPLICABLE,
            emission_walk=NOT_APPLICABLE,
            emission_yearly_walk=NOT_APPLICABLE,
            emission_pt=NOT_APPLICABLE,
            emission_yearly_pt=NOT_APPLICABLE,
            emission_car=NOT_APPLICABLE,
            emission_yearly_car=NOT_APPLICABLE,
            emission_bicycle=NOT_APPLICABLE,
            emission_yearly_bicycle=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_1=NOT_APPLICABLE,
            bicycle_modal_shift_scenario_2=NOT_APPLICABLE,
            pt_modal_shift_scenario_1=NOT_APPLICABLE,
            pt_modal_shift_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_1=NOT_APPLICABLE,
            remote_worker_scenario_2=NOT_APPLICABLE,
            remote_worker_scenario_3=NOT_APPLICABLE,
            remote_worker_scenario_4=NOT_APPLICABLE,
            time_8_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_00_transport_mode_selected=NOT_APPLICABLE,
            time_9_30_transport_mode_selected=NOT_APPLICABLE,
            time_10_00_transport_mode_selected=NOT_APPLICABLE,
            carpool_cluster_id=NOT_APPLICABLE,
            cluster_id=NOT_APPLICABLE,
            cluster_center_address=NOT_APPLICABLE,
            coworking_name=NOT_APPLICABLE,
            coworking_address=NOT_APPLICABLE,
            coworking_transport_mode=NOT_APPLICABLE,
            coworking_time=NOT_APPLICABLE,
            coworking_emission=NOT_APPLICABLE,
            coworking_yearly_emission=NOT_APPLICABLE,
            coworking_distance=NOT_APPLICABLE,
        )

    @staticmethod
    def get_commute_alternative_duration(
        commute_data: ModalCommuteData, employee: GeoEmployee, hour: int, minute: int
    ) -> ImmutableDict:
        try:
            return commute_data.get_alternative_duration(hour, minute)
        except ValueError:
            return ImmutableDict()

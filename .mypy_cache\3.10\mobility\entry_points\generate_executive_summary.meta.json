{"data_mtime": 1753090389, "dep_lines": [3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.repositories.travel_repository", "mobility.serializers.report_serializer", "mobility.use_cases.scenario_indicators", "<PERSON><PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "mobility.builders", "mobility.ir", "mobility.ir.new_indicators", "mobility.ir.study", "mobility.repositories", "mobility.serializers", "mobility.use_cases", "mobility.use_cases.base_use_case", "typing"], "hash": "c0cf53498bdb25a6de65c80ec5cc728573c0faaa", "id": "mobility.entry_points.generate_executive_summary", "ignore_all": false, "interface_hash": "b41f8eb13e6afc7165baf6dc0b08a46a0211dc6b", "mtime": 1722327416, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\entry_points\\generate_executive_summary.py", "plugin_data": null, "size": 1120, "suppressed": [], "version_id": "1.16.1"}
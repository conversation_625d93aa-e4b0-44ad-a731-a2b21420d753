{"data_mtime": 1752154451, "dep_lines": [6, 7, 2, 13, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.repositories.simulated_company", "mobility.use_cases.simulated_company_potential", "unittest.mock", "webservice.apis", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "flask", "flask.app", "flask.helpers", "flask.testing", "flask.wrappers", "mobility", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "werkzeug", "werkzeug.test", "werkzeug.wrappers"], "hash": "c857e62d2bb390a61889dbe7df5ac4d7ee20d92c", "id": "webservice.tests.apis.test_company_simulation", "ignore_all": false, "interface_hash": "c9d1ad04634ec70e68a8387bd68e871e61eed84a", "mtime": 1722327437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\tests\\apis\\test_company_simulation.py", "plugin_data": null, "size": 9642, "suppressed": [], "version_id": "1.16.1"}
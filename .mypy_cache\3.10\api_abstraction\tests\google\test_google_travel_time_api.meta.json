{"data_mtime": 1753176801, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 3, 14, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.google.api", "api_abstraction.google.base_google_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "unittest.mock", "mobility.quantity", "logging", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "_typeshed", "abc", "api_abstraction.api", "api_abstraction.api.travel_time_api", "api_abstraction.google", "contextlib", "datetime", "enum", "functools", "mobility", "mobility.ir", "mobility.ir.country", "re", "typing_extensions", "unittest"], "hash": "dea22662e15df75f266ae0d6a6cb3c422f9c91f3", "id": "api_abstraction.tests.google.test_google_travel_time_api", "ignore_all": false, "interface_hash": "b5dc5c53bfe84d8f7b59ce855213cf67aeb6c906", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\google\\test_google_travel_time_api.py", "plugin_data": null, "size": 25371, "suppressed": [], "version_id": "1.16.1"}
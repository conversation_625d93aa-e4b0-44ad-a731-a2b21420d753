{"data_mtime": 1753090389, "dep_lines": [6, 7, 8, 9, 10, 11, 15, 16, 17, 5, 12, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.scenario", "mobility.ir.site", "mobility.ir.transport", "mobility.repositories.travel_repository", "mobility.use_cases.base_use_case", "mobility.workers.cost_computer", "mobility.funky", "mobility.quantity", "math", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.repositories", "mobility.workers", "mobility.workers.employee_cost_calculator", "mobility.workers.health_cost_calculator", "mobility.workers.infrastructure_cost_calculator", "mobility.workers.transport_cost_calculator", "mobility.workers.work_accident_cost_calculator", "types", "typing_extensions"], "hash": "4277cde190b60c0f81ac543c433e2602eb96bff0", "id": "mobility.use_cases.compute_trip_costs", "ignore_all": false, "interface_hash": "ab42323bfe8e123d38a51027b3503059cfaa73fe", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\use_cases\\compute_trip_costs.py", "plugin_data": null, "size": 25368, "suppressed": [], "version_id": "1.16.1"}
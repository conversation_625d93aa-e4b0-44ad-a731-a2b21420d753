{".class": "MypyFile", "_fullname": "api_abstraction.flight.flight_travel_time", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirportDatabaseBuilder": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.airport_database.AirportDatabaseBuilder", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "DistanceTravelTimer": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.trivial.trivial_travel_time.DistanceTravelTimer", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "FlightComputerApi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi", "name": "FlightComputerApi", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.flight.flight_travel_time", "mro": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "token", "reporter", "airports_csv", "ground_timer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "token", "reporter", "airports_csv", "ground_timer"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "builtins.str", "api_abstraction.api.event_reporter.EventReporter", "builtins.str", {".class": "UnionType", "items": ["api_abstraction.api.travel_time_api.TravelTimeApi", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FlightComputerApi", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_flight_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi._calculate_flight_journey", "name": "_calculate_flight_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_flight_journey of FlightComputerApi", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_combine_journey_segments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "flight", "access_journey", "access_mode", "egress_journey", "egress_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi._combine_journey_segments", "name": "_combine_journey_segments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "flight", "access_journey", "access_mode", "egress_journey", "egress_mode"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "api_abstraction.api.travel_time_api.JourneyAttribute", "api_abstraction.api.travel_time_api.JourneyAttribute", "mobility.ir.transport.TransportMode", "api_abstraction.api.travel_time_api.JourneyAttribute", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_combine_journey_segments of FlightComputerApi", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_best_ground_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi._get_best_ground_journey", "name": "_get_best_ground_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "arrival_time"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_best_ground_journey of FlightComputerApi", "ret_type": {".class": "TupleType", "implicit": false, "items": ["api_abstraction.api.travel_time_api.JourneyAttribute", "mobility.ir.transport.TransportMode"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_nearest_airports": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi._get_nearest_airports", "name": "_get_nearest_airports", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_nearest_airports of FlightComputerApi", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi._time", "name": "_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.flight.flight_travel_time.FlightComputerApi", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time of FlightComputerApi", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "airport_database": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi.airport_database", "name": "airport_database", "setter_type": null, "type": "mobility.builders.airport_database.AirportDatabase"}}, "ground_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi.ground_timer", "name": "ground_timer", "setter_type": null, "type": "api_abstraction.api.travel_time_api.TravelTimeApi"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.flight.flight_travel_time.FlightComputerApi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.flight.flight_travel_time.FlightComputerApi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.flight.flight_travel_time.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.distance_computer.compute_distance", "kind": "Gdef"}, "compute_mode_emission_from_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.emission_computer.compute_mode_emission_from_distance", "kind": "Gdef"}, "kilometers": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.kilometers", "kind": "Gdef"}}, "path": "api_abstraction\\flight\\flight_travel_time.py"}
{"data_mtime": 1752154427, "dep_lines": [3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.ir.scenario_data", "mobility.ir.study", "mobility.ir.study_types", "mobility.serializers.json_serializer", "datetime", "builtins", "_frozen_importlib", "abc", "enum", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.site", "mobility.ir.transport", "mobility.quantity", "mobility.serializers", "typing"], "hash": "e9f2818212fa0347034fb5b99cd0caa52accb91f", "id": "scripts.make_diagnoses_from_study", "ignore_all": false, "interface_hash": "df768724aee120ab12622b2746754b49cbc87530", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\make_diagnoses_from_study.py", "plugin_data": null, "size": 1509, "suppressed": [], "version_id": "1.16.1"}
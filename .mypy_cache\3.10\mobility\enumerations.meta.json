{"data_mtime": 1753176773, "dep_lines": [3, 4, 12, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.mode_shift_readiness_classification", "mobility.ir.onboarding_form_step", "mobility.ir.plan", "mobility.ir.transport", "mobility.quantity", "dataclasses", "builtins", "_frozen_importlib", "abc", "enum", "mobility.ir", "typing", "typing_extensions"], "hash": "3e737b1c4d8a1191ab8fb217e5e478d394162ff4", "id": "mobility.enumerations", "ignore_all": false, "interface_hash": "5559daffceccb767270d60665be6b39098a1f5d5", "mtime": 1723449311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\enumerations.py", "plugin_data": null, "size": 1390, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "api_abstraction.beautiful_soup.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Address": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Address", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "BanFranceGeocoder": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.geopy.ban_france.BanFranceGeocoder", "kind": "Gdef"}, "BeautifulSoup": {".class": "SymbolTableNode", "cross_ref": "bs4.BeautifulSoup", "kind": "Gdef"}, "BeautifulSoupTravelTimeAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "name": "BeautifulSoupTravelTimeAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.beautiful_soup.api", "mro": ["api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "reporter", "url_maker", "soup_journey_parser", "post_data_maker"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "reporter", "url_maker", "soup_journey_parser", "post_data_maker"], "arg_types": ["api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "api_abstraction.api.event_reporter.EventReporter", {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["bs4.BeautifulSoup"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["mobility.ir.geo_study.Address", "mobility.ir.geo_study.Address", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BeautifulSoupTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_beautiful_soup_caller": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI._beautiful_soup_caller", "name": "_beautiful_soup_caller", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["url", "post_data"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "bs4.BeautifulSoup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_call_bs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "post_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI._call_bs", "name": "_call_bs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "post_data"], "arg_types": ["api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_call_bs of BeautifulSoupTravelTimeAPI", "ret_type": "bs4.BeautifulSoup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI._time", "name": "_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time of BeautifulSoupTravelTimeAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ban_france": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.ban_france", "name": "ban_france", "setter_type": null, "type": "api_abstraction.geopy.ban_france.BanFranceGeocoder"}}, "compute_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.compute_isochrone", "name": "compute_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "arg_types": ["api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "mobility.ir.territory.Territory", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isochrone of BeautifulSoupTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_call_per_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.max_call_per_period", "name": "max_call_per_period", "setter_type": null, "type": "builtins.int"}}, "period_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.period_in_seconds", "name": "period_in_seconds", "setter_type": null, "type": "builtins.int"}}, "post_data_maker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.post_data_maker", "name": "post_data_maker", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["mobility.ir.geo_study.Address", "mobility.ir.geo_study.Address", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.retries", "name": "retries", "setter_type": null, "type": "builtins.int"}}, "retry_delay_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.retry_delay_in_seconds", "name": "retry_delay_in_seconds", "setter_type": null, "type": "builtins.int"}}, "soup_journey_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.soup_journey_parser", "name": "soup_journey_parser", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["bs4.BeautifulSoup"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_maker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.url_maker", "name": "url_maker", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.beautiful_soup.api.BeautifulSoupTravelTimeAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.beautiful_soup.api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.distance_computer.compute_distance", "kind": "Gdef"}, "meters": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.meters", "kind": "Gdef"}, "nmp_ihm_canaltp_soup_journey_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["soup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.nmp_ihm_canaltp_soup_journey_parser", "name": "nmp_ihm_canaltp_soup_journey_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["soup"], "arg_types": ["bs4.BeautifulSoup"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "nmp_ihm_canaltp_soup_journey_parser", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nmp_ihm_canaltp_url_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.nmp_ihm_canaltp_url_maker", "name": "nmp_ihm_canaltp_url_maker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "nmp_ihm_canaltp_url_maker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oura_soup_journey_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["soup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.oura_soup_journey_parser", "name": "oura_soup_journey_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["soup"], "arg_types": ["bs4.BeautifulSoup"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "oura_soup_journey_parser", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oura_soup_post_data_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.oura_soup_post_data_maker", "name": "oura_soup_post_data_maker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "arg_types": ["mobility.ir.geo_study.Address", "mobility.ir.geo_study.Address", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "oura_soup_post_data_maker", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oura_soup_url_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.oura_soup_url_maker", "name": "oura_soup_url_maker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["origin", "destination", "mode", "arrival_time"], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "oura_soup_url_maker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_hour_min_text_as_duration_in_seconds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.beautiful_soup.api.parse_hour_min_text_as_duration_in_seconds", "name": "parse_hour_min_text_as_duration_in_seconds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_hour_min_text_as_duration_in_seconds", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.beautiful_soup.api.regex", "name": "regex", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.beautiful_soup.api.regex", "source_any": null, "type_of_any": 3}}}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}}, "path": "api_abstraction\\beautiful_soup\\api.py"}
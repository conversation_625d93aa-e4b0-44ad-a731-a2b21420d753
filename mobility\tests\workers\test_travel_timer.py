from typing import Any

import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import ApiFail, ApiTimeout, TravelTimeApi
from api_abstraction.trivial.trivial_travel_time import TrivialTravelTimer
from mobility.funky import ImmutableDict
from mobility.ir.commute_data import BaseCommuteData, TimedCommuteData
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.infrastructure import PublicTransportStop
from mobility.ir.poi import PointOfInterest
from mobility.ir.study_types import <PERSON>mm<PERSON>, Scenario, Scenarios
from mobility.ir.transport import TransportMode
from mobility.workers.travel_timer import (
    CommuteTimer,
    TravelTimer,
    retime_with_alternatives,
    time_with_alternatives,
)


class FailingTravelTimer(TravelTimeApi):
    def time(self, *args, **kwargs):
        raise ApiFail("woups")


class TimeoutTravelTimer(TravelTimeApi):
    def time(self, *args, **kwargs):
        raise ApiTimeout("woups")


class TestTravelTimer:
    def test_travel_timer_returns_one_commute_travel_time(
        self,
        geo_employee: Any,
        geo_site: Any,
        scenario_data: Any,
        timed_commute_data_factory: Any,
        infrastructure: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        localised_study_factory: Any,
        coworking_site: Any,
        base_commute_data: Any,
        study_data: Any,
    ) -> None:
        travel_timer = TravelTimer(
            TrivialTravelTimer("", EventReporter()), (8, 30), [(10, 0)]
        )
        commutes = {(geo_employee, geo_site, None)}
        scenarios = Scenarios.from_commutes({scenario_data: commutes})
        poi = PointOfInterest(name="Part-Dieu", coordinates=GeoCoordinates(45.76, 4.86))
        poi_commutes = {(geo_site, poi, None)}
        poi_scenarios = Scenarios.from_commutes({scenario_data: poi_commutes})
        empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})
        infrastructure = infrastructure()
        coworking_site = coworking_site()
        coworking_commutes = {(geo_employee, coworking_site, None)}
        coworking_scenario = Scenario.from_commutes(None, coworking_commutes)
        data = study_data(mission_id="24M000.0")
        loc_study = localised_study_factory(
            scenarios=scenarios,
            poi_scenarios=poi_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            coworking_scenario=coworking_scenario,
        )

        study = travel_timer.time(loc_study)

        def fill_data(delta: int = 0) -> ImmutableDict:
            return ImmutableDict(
                {
                    TransportMode.WALK: 152 + delta,
                    TransportMode.PUBLIC_TRANSPORT: 34 + delta,
                    TransportMode.CAR: 23 + delta,
                    TransportMode.BICYCLE: 32 + delta,
                    TransportMode.CARPOOLING: 23 + delta,
                    TransportMode.ELECTRIC_BICYCLE: 32 + delta,
                    TransportMode.ELECTRIC_CAR: 23 + delta,
                    TransportMode.MOTORCYCLE: 23 + delta,
                    TransportMode.AIRPLANE: 7200 + delta,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90 + delta,
                    TransportMode.FAST_BICYCLE: 91 + delta,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92 + delta,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + delta,
                }
            )

        filler_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        filler_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        commute_data = timed_commute_data_factory(
            duration=fill_data(),
            distance=filler_distance,
            emission=filler_emission,
            alternative_arrival_time=ImmutableDict(
                {
                    (10, 0): base_commute_data(
                        duration=fill_data(2),
                        distance=filler_distance,
                        emission=filler_emission,
                    )
                }
            ),
        )
        commute_data_no_alternatives = timed_commute_data_factory(
            duration=fill_data(),
            distance=filler_distance,
            emission=filler_emission,
            alternative_arrival_time=ImmutableDict(),
        )
        timed_commutes = {(geo_employee, geo_site, commute_data)}
        timed_poi_commutes = {(geo_site, poi, commute_data_no_alternatives)}
        poi_scenarios = Scenarios.from_commutes({scenario_data: timed_poi_commutes})

        timed_infrastructure = timed_infrastructure()
        timed_coworking_commutes = {
            (geo_employee, coworking_site, commute_data_no_alternatives)
        }
        timed_coworking_scenario = Scenario.from_commutes(
            None, timed_coworking_commutes
        )
        assert study == timed_study_factory(
            scenarios=Scenarios.from_commutes({scenario_data: timed_commutes}),
            poi_scenarios=poi_scenarios,
            infrastructure=timed_infrastructure,
            data=data,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
            coworking_scenario=timed_coworking_scenario,
        )

    def test_travel_timer_keeps_one_failed_geocoding_info(
        self,
        geo_employee: Any,
        scenario_data: Any,
        failed_geo_site: Any,
        infrastructure: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        localised_study_factory: Any,
        study_data: Any,
    ) -> None:
        travel_timer = TravelTimer(TrivialTravelTimer("", EventReporter()))
        empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})
        commutes = {(geo_employee, failed_geo_site, None)}
        failed_site_scenarios = Scenarios.from_commutes({scenario_data: commutes})
        infrastructure = infrastructure()
        data = study_data(mission_id="24M000.0")
        loc_study = localised_study_factory(
            scenarios=empty_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=failed_site_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
        )

        study = travel_timer.time(loc_study)

        timed_infrastructure = timed_infrastructure()
        assert study == timed_study_factory(
            scenarios=empty_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=timed_infrastructure,
            data=data,
            geocode_failed_sites=failed_site_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )


class TestTimeCommute:
    def test_should_compute_duration_with_all_transport_mode_when_duration_is_empty(
        self,
        geo_employee: Any,
        geo_site: Any,
        scenario_data: Any,
    ) -> None:
        travel_timer = TravelTimer(TrivialTravelTimer("", EventReporter()))
        commute = Commute(geo_employee, geo_site, None)

        commute_data = travel_timer.time_commute_with_alternatives(
            commute, scenario_data
        )

        filler_duration = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 7200,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        filler_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        filler_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        assert commute_data == TimedCommuteData(
            duration=filler_duration,
            distance=filler_distance,
            emission=filler_emission,
            alternative_arrival_time=ImmutableDict(),
        )

    @pytest.mark.parametrize("travel_timer", [FailingTravelTimer, TimeoutTravelTimer])
    def test_should_return_empty_duration_when_api_is_down(
        self, scenario_data: Any, geo_employee: Any, geo_site: Any, travel_timer: Any
    ) -> None:
        travel_timer = TravelTimer(travel_timer("", EventReporter()))
        commute = Commute(geo_employee, geo_site, None)

        commute_data = travel_timer.time_commute_with_walking_mode(
            commute, scenario_data
        )

        assert commute_data.duration == ImmutableDict()

    def test_should_recompute_commute_data_if_missing_distance(
        self,
        scenario_data: Any,
        geo_employee: Any,
        geo_site: Any,
    ) -> None:
        travel_timer = TravelTimer(TrivialTravelTimer("", EventReporter()))
        duration = ImmutableDict(
            {
                TransportMode.WALK: 10,
                TransportMode.BICYCLE: 20,
                TransportMode.CAR: 30,
                TransportMode.PUBLIC_TRANSPORT: 40,
                TransportMode.CARPOOLING: 50,
                TransportMode.ELECTRIC_BICYCLE: 60,
                TransportMode.ELECTRIC_CAR: 70,
                TransportMode.MOTORCYCLE: 80,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        emission: ImmutableDict = ImmutableDict(
            {
                TransportMode.WALK: 0,
                TransportMode.BICYCLE: 0,
                TransportMode.CAR: 300,
                TransportMode.PUBLIC_TRANSPORT: 400,
                TransportMode.CARPOOLING: 500,
                TransportMode.ELECTRIC_BICYCLE: 100,
                TransportMode.ELECTRIC_CAR: 200,
                TransportMode.MOTORCYCLE: 600,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        distance: ImmutableDict = ImmutableDict()
        data = TimedCommuteData(
            duration=duration,
            distance=distance,
            emission=emission,
            alternative_arrival_time=ImmutableDict(),
        )
        commute = Commute(geo_employee, geo_site, data)

        commute_data = travel_timer.time_commute_with_alternatives(
            commute, scenario_data
        )

        trivial_duration = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 7200,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        trivial_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        trivial_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        assert commute_data == TimedCommuteData(
            duration=trivial_duration,
            distance=trivial_distance,
            emission=trivial_emission,
            alternative_arrival_time=ImmutableDict(),
        )

    def test_should_recompute_commute_data_if_missing_emission(
        self,
        scenario_data: Any,
        geo_employee: Any,
        geo_site: Any,
    ) -> None:
        travel_timer = TravelTimer(TrivialTravelTimer("", EventReporter()))
        duration = ImmutableDict(
            {
                TransportMode.WALK: 10,
                TransportMode.BICYCLE: 20,
                TransportMode.CAR: 30,
                TransportMode.PUBLIC_TRANSPORT: 40,
                TransportMode.CARPOOLING: 50,
                TransportMode.ELECTRIC_BICYCLE: 60,
                TransportMode.ELECTRIC_CAR: 70,
                TransportMode.MOTORCYCLE: 80,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        distance: ImmutableDict = ImmutableDict(
            {
                TransportMode.WALK: 100,
                TransportMode.BICYCLE: 200,
                TransportMode.CAR: 300,
                TransportMode.PUBLIC_TRANSPORT: 400,
                TransportMode.CARPOOLING: 500,
                TransportMode.ELECTRIC_BICYCLE: 600,
                TransportMode.ELECTRIC_CAR: 700,
                TransportMode.MOTORCYCLE: 800,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        emission: ImmutableDict = ImmutableDict()
        data = TimedCommuteData(
            duration=duration,
            distance=distance,
            emission=emission,
            alternative_arrival_time=ImmutableDict(),
        )
        commute = Commute(geo_employee, geo_site, data)

        commute_data = travel_timer.time_commute_with_alternatives(
            commute, scenario_data
        )

        trivial_duration = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 7200,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        trivial_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        trivial_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.BICYCLE: 32,
                TransportMode.CAR: 23,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        assert commute_data == TimedCommuteData(
            duration=trivial_duration,
            distance=trivial_distance,
            emission=trivial_emission,
            alternative_arrival_time=ImmutableDict(),
        )

    def test_should_time_missing_duration_in_commute(
        self,
        geo_employee: Any,
        geo_site: Any,
        scenario_data: Any,
        timed_commute_data_factory,
    ) -> None:
        travel_timer = TravelTimer(TrivialTravelTimer("", EventReporter()))
        duration = ImmutableDict(
            {TransportMode.WALK: 15, TransportMode.PUBLIC_TRANSPORT: 14}
        )
        distance = ImmutableDict(
            {TransportMode.WALK: 150, TransportMode.PUBLIC_TRANSPORT: 250}
        )
        emission = ImmutableDict(
            {TransportMode.WALK: 0, TransportMode.PUBLIC_TRANSPORT: 250}
        )
        data = TimedCommuteData(
            duration=duration,
            distance=distance,
            emission=emission,
            alternative_arrival_time=ImmutableDict(),
        )
        commute = Commute(geo_employee, geo_site, data)

        commute_data = travel_timer.time_commute_with_alternatives(
            commute, scenario_data
        )

        assert commute_data == TimedCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 15,
                    TransportMode.PUBLIC_TRANSPORT: 14,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 150,
                    TransportMode.PUBLIC_TRANSPORT: 250,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 0,
                    TransportMode.PUBLIC_TRANSPORT: 250,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            alternative_arrival_time=ImmutableDict(),
        )

    def test_should_compute_alternative_arrival_time_data(
        self,
        geo_employee: Any,
        geo_site: Any,
        scenario_data: Any,
    ) -> None:
        travel_timer = TravelTimer(
            TrivialTravelTimer("", EventReporter()),
            alternative_arrival_time=[(9, 0), (10, 0), (8, 0), (9, 30), (7, 0)],
        )
        commute = Commute(geo_employee, geo_site, None)

        commute_data = travel_timer.time_commute_with_alternatives(
            commute, scenario_data
        )

        def fill_data(delta: int = 0) -> ImmutableDict:
            return ImmutableDict(
                {
                    TransportMode.WALK: 152 + delta,
                    TransportMode.PUBLIC_TRANSPORT: 34 + delta,
                    TransportMode.CAR: 23 + delta,
                    TransportMode.BICYCLE: 32 + delta,
                    TransportMode.CARPOOLING: 23 + delta,
                    TransportMode.ELECTRIC_BICYCLE: 32 + delta,
                    TransportMode.ELECTRIC_CAR: 23 + delta,
                    TransportMode.MOTORCYCLE: 23 + delta,
                    TransportMode.AIRPLANE: 7200 + delta,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90 + delta,
                    TransportMode.FAST_BICYCLE: 91 + delta,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92 + delta,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + delta,
                }
            )

        expected_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        expected_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        expected_alt_arrival_time = ImmutableDict(
            {
                (7, 0): BaseCommuteData(
                    duration=fill_data(-1),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
                (8, 0): BaseCommuteData(
                    duration=fill_data(),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
                (9, 0): BaseCommuteData(
                    duration=fill_data(1),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
                (9, 30): BaseCommuteData(
                    duration=fill_data(1),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
                (10, 0): BaseCommuteData(
                    duration=fill_data(2),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
            }
        )
        assert commute_data == TimedCommuteData(
            duration=fill_data(),
            distance=expected_distance,
            emission=expected_emission,
            alternative_arrival_time=expected_alt_arrival_time,
        )


class TestTimeTcStationCommute:
    def test_should_time_walk_in_localised_commute(self, geo_site):
        public_transport_stop = PublicTransportStop(
            name="Mon STOP",
            coordinates=GeoCoordinates(0.0, 1.0),
            lines_connected=frozenset(),
            lines_kinds=frozenset(),
        )
        travel_timer = TravelTimer(
            TrivialTravelTimer("", EventReporter()),
            alternative_arrival_time=[(9, 0), (10, 0), (8, 0), (9, 30), (7, 0)],
        )
        commute = Commute(geo_site, public_transport_stop, None)

        commute_data = travel_timer.time_commute_with_walking_mode(commute, None)

        assert commute_data == TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 152}),
            distance=ImmutableDict({TransportMode.WALK: 152}),
            emission=ImmutableDict({TransportMode.WALK: 152}),
            alternative_arrival_time=ImmutableDict(),
        )

    def test_should_time_walk_in_timed_commute(self, geo_site):
        public_transport_stop = PublicTransportStop(
            name="Mon STOP",
            coordinates=GeoCoordinates(0.0, 1.0),
            lines_connected=frozenset(),
            lines_kinds=frozenset(),
        )
        travel_timer = TravelTimer(
            TrivialTravelTimer("", EventReporter()),
            alternative_arrival_time=[(9, 0), (10, 0), (8, 0), (9, 30), (7, 0)],
        )
        already_present_data = TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 2}),
            distance=ImmutableDict({}),
            emission=ImmutableDict({TransportMode.WALK: 2}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = Commute(geo_site, public_transport_stop, already_present_data)

        commute_data = travel_timer.time_commute_with_walking_mode(commute, None)

        assert commute_data == TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 152}),
            distance=ImmutableDict({TransportMode.WALK: 152}),
            emission=ImmutableDict({TransportMode.WALK: 152}),
            alternative_arrival_time=ImmutableDict(),
        )

    def test_should_not_time_walk_in_fully_timed_commute(self, geo_site):
        public_transport_stop = PublicTransportStop(
            name="Mon STOP",
            coordinates=GeoCoordinates(0.0, 1.0),
            lines_connected=frozenset(),
            lines_kinds=frozenset(),
        )
        travel_timer = TravelTimer(
            TrivialTravelTimer("", EventReporter()),
            alternative_arrival_time=[(9, 0), (10, 0), (8, 0), (9, 30), (7, 0)],
        )
        already_present_data = TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 22}),
            distance=ImmutableDict({TransportMode.WALK: 22}),
            emission=ImmutableDict({TransportMode.WALK: 22}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = Commute(geo_site, public_transport_stop, already_present_data)

        commute_data = travel_timer.time_commute_with_walking_mode(commute, None)

        assert commute_data == TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 22}),
            distance=ImmutableDict({TransportMode.WALK: 22}),
            emission=ImmutableDict({TransportMode.WALK: 22}),
            alternative_arrival_time=ImmutableDict(),
        )


class TestCommuteTimer:
    def test_should_time_all_transport_modes(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        timer = CommuteTimer(trivial_travel_timer, origin, destination, arrival_time)

        times = timer.time_all_transport_modes()

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
        )

    def test_should_set_pt_commute_as_walk_commute_if_walk_is_faster(
        self, trivial_travel_timer
    ):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        commute_data = BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 1500,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 2000,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 0,
                    TransportMode.PUBLIC_TRANSPORT: 2000,
                }
            ),
        )
        timer = CommuteTimer(
            trivial_travel_timer, origin, destination, arrival_time, commute_data
        )

        times = timer.time_all_transport_modes()

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 1000,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 1000,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 0,
                    TransportMode.PUBLIC_TRANSPORT: 0,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
        )

    def test_should_retime_transport_modes_with_no_complete_data(
        self, trivial_travel_timer
    ):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        commute_data = BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 2000,
                    TransportMode.CAR: 3000,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 2000,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                }
            ),
        )
        timer = CommuteTimer(
            trivial_travel_timer, origin, destination, arrival_time, commute_data
        )

        times = timer.time_all_transport_modes()

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
        )

    def test_should_retime_all_transport_modes(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        commute_data = BaseCommuteData(
            duration=ImmutableDict({}),
            distance=ImmutableDict({}),
            emission=ImmutableDict({}),
        )
        timer = CommuteTimer(
            trivial_travel_timer, origin, destination, arrival_time, commute_data
        )

        times = timer.time_all_transport_modes()

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
        )

    def test_should_time_a_transport_mode(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        timer = CommuteTimer(trivial_travel_timer, origin, destination, arrival_time)

        times = timer.time_a_transport_mode(TransportMode.WALK)

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
        )

    def test_should_retime_a_transport_mode_with_no_complete_data(
        self, trivial_travel_timer
    ):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        data = BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                }
            ),
            distance=ImmutableDict({}),
            emission=ImmutableDict({}),
        )
        timer = CommuteTimer(
            trivial_travel_timer, origin, destination, arrival_time, data
        )

        times = timer.time_a_transport_mode(TransportMode.WALK)

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                }
            ),
        )

    def test_should_not_retime_a_transport_mode_when_is_complete(
        self, trivial_travel_timer
    ):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        arrival_time = (8, 30)
        data = BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
        )
        timer = CommuteTimer(
            trivial_travel_timer, origin, destination, arrival_time, data
        )

        times = timer.time_a_transport_mode(TransportMode.WALK)

        assert times == BaseCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 1000,
                    TransportMode.CAR: 2000,
                }
            ),
        )


class TestTimeWithAlternatives:
    def test_time_with_alternatives(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        reference_arrival_time = (8, 30)
        alternative_arrival_times = [(9, 0), (10, 30)]

        times = time_with_alternatives(
            trivial_travel_timer,
            origin,
            destination,
            reference_arrival_time,
            alternative_arrival_times,
        )

        def fill_data(delta: int = 0) -> ImmutableDict:
            return ImmutableDict(
                {
                    TransportMode.WALK: 152 + delta,
                    TransportMode.PUBLIC_TRANSPORT: 34 + delta,
                    TransportMode.CAR: 23 + delta,
                    TransportMode.BICYCLE: 32 + delta,
                    TransportMode.CARPOOLING: 23 + delta,
                    TransportMode.ELECTRIC_BICYCLE: 32 + delta,
                    TransportMode.ELECTRIC_CAR: 23 + delta,
                    TransportMode.MOTORCYCLE: 23 + delta,
                    TransportMode.AIRPLANE: 7200 + delta,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90 + delta,
                    TransportMode.FAST_BICYCLE: 91 + delta,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92 + delta,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + delta,
                }
            )

        expected_distance = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 500000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )

        expected_emission = ImmutableDict(
            {
                TransportMode.WALK: 152,
                TransportMode.PUBLIC_TRANSPORT: 34,
                TransportMode.CAR: 23,
                TransportMode.BICYCLE: 32,
                TransportMode.CARPOOLING: 23,
                TransportMode.ELECTRIC_BICYCLE: 32,
                TransportMode.ELECTRIC_CAR: 23,
                TransportMode.MOTORCYCLE: 23,
                TransportMode.AIRPLANE: 115000,
                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                TransportMode.FAST_BICYCLE: 91,
                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
            }
        )
        expected_alt_arrival_time = ImmutableDict(
            {
                (9, 0): BaseCommuteData(
                    duration=fill_data(1),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
                (10, 30): BaseCommuteData(
                    duration=fill_data(2),
                    distance=expected_distance,
                    emission=expected_emission,
                ),
            }
        )
        assert times == TimedCommuteData(
            duration=fill_data(),
            distance=expected_distance,
            emission=expected_emission,
            alternative_arrival_time=expected_alt_arrival_time,
        )

    def test_retime_with_alternatives(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        reference_arrival_time = (8, 30)
        alternative_arrival_times = [(9, 0), (10, 30)]
        data = TimedCommuteData(
            duration=ImmutableDict({}),
            distance=ImmutableDict({}),
            emission=ImmutableDict({}),
            alternative_arrival_time=ImmutableDict(
                {
                    (9, 0): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                            }
                        ),
                    ),
                    (10, 30): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                    ),
                }
            ),
        )

        times = retime_with_alternatives(
            trivial_travel_timer,
            origin,
            destination,
            data,
            reference_arrival_time,
            alternative_arrival_times,
        )

        assert times == TimedCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            alternative_arrival_time=ImmutableDict(
                {
                    (9, 0): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 24,
                                TransportMode.BICYCLE: 33,
                                TransportMode.CARPOOLING: 24,
                                TransportMode.ELECTRIC_BICYCLE: 33,
                                TransportMode.ELECTRIC_CAR: 24,
                                TransportMode.MOTORCYCLE: 24,
                                TransportMode.AIRPLANE: 7201,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90 + 1,
                                TransportMode.FAST_BICYCLE: 91 + 1,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92 + 1,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + 1,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 500000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 115000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                    ),
                    (10, 30): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                                TransportMode.CARPOOLING: 25,
                                TransportMode.ELECTRIC_BICYCLE: 34,
                                TransportMode.ELECTRIC_CAR: 25,
                                TransportMode.MOTORCYCLE: 25,
                                TransportMode.AIRPLANE: 7202,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90 + 2,
                                TransportMode.FAST_BICYCLE: 91 + 2,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92 + 2,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + 2,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 500000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 115000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                    ),
                }
            ),
        )

    def test_retime_with_new_alternatives(self, trivial_travel_timer):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(0.0, 1.0)
        reference_arrival_time = (8, 30)
        alternative_arrival_times = [(9, 0), (11, 30)]
        data = TimedCommuteData(
            duration=ImmutableDict({}),
            distance=ImmutableDict({}),
            emission=ImmutableDict({}),
            alternative_arrival_time=ImmutableDict(
                {
                    (9, 0): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2500,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                            }
                        ),
                    ),
                    (10, 30): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2500,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 3000,
                                TransportMode.BICYCLE: 4000,
                            }
                        ),
                    ),
                }
            ),
        )

        times = retime_with_alternatives(
            trivial_travel_timer,
            origin,
            destination,
            data,
            reference_arrival_time,
            alternative_arrival_times,
        )

        assert times == TimedCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 7200,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 500000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 152,
                    TransportMode.PUBLIC_TRANSPORT: 34,
                    TransportMode.CAR: 23,
                    TransportMode.BICYCLE: 32,
                    TransportMode.CARPOOLING: 23,
                    TransportMode.ELECTRIC_BICYCLE: 32,
                    TransportMode.ELECTRIC_CAR: 23,
                    TransportMode.MOTORCYCLE: 23,
                    TransportMode.AIRPLANE: 115000,
                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                    TransportMode.FAST_BICYCLE: 91,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                }
            ),
            alternative_arrival_time=ImmutableDict(
                {
                    (9, 0): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 2500,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 24,
                                TransportMode.BICYCLE: 33,
                                TransportMode.CARPOOLING: 24,
                                TransportMode.ELECTRIC_BICYCLE: 33,
                                TransportMode.ELECTRIC_CAR: 24,
                                TransportMode.MOTORCYCLE: 24,
                                TransportMode.AIRPLANE: 7201,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90 + 1,
                                TransportMode.FAST_BICYCLE: 91 + 1,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92 + 1,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + 1,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 500000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 1000,
                                TransportMode.PUBLIC_TRANSPORT: 2000,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 115000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                    ),
                    (11, 30): BaseCommuteData(
                        duration=ImmutableDict(
                            {
                                TransportMode.WALK: 155,
                                TransportMode.PUBLIC_TRANSPORT: 37,
                                TransportMode.CAR: 26,
                                TransportMode.BICYCLE: 35,
                                TransportMode.CARPOOLING: 26,
                                TransportMode.ELECTRIC_BICYCLE: 35,
                                TransportMode.ELECTRIC_CAR: 26,
                                TransportMode.MOTORCYCLE: 26,
                                TransportMode.AIRPLANE: 7203,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90 + 3,
                                TransportMode.FAST_BICYCLE: 91 + 3,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92 + 3,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93 + 3,
                            }
                        ),
                        distance=ImmutableDict(
                            {
                                TransportMode.WALK: 152,
                                TransportMode.PUBLIC_TRANSPORT: 34,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 500000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 152,
                                TransportMode.PUBLIC_TRANSPORT: 34,
                                TransportMode.CAR: 23,
                                TransportMode.BICYCLE: 32,
                                TransportMode.CARPOOLING: 23,
                                TransportMode.ELECTRIC_BICYCLE: 32,
                                TransportMode.ELECTRIC_CAR: 23,
                                TransportMode.MOTORCYCLE: 23,
                                TransportMode.AIRPLANE: 115000,
                                TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                TransportMode.FAST_BICYCLE: 91,
                                TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                            }
                        ),
                    ),
                }
            ),
        )

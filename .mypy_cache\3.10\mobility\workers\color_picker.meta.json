{"data_mtime": 1753176775, "dep_lines": [8, 9, 10, 11, 12, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 4], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["mobility.ir.color_mode", "mobility.ir.cost", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.ir.work_mode", "random", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.funky", "mobility.ir", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "types", "typing_extensions"], "hash": "25831dbb34f92dd338f9fc3999dcc369d42a518d", "id": "mobility.workers.color_picker", "ignore_all": false, "interface_hash": "3294e6f7bf79dd9119423a5a2bd728e9b517b524", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\color_picker.py", "plugin_data": null, "size": 12261, "suppressed": ["scipy.spatial", "networkx"], "version_id": "1.16.1"}
{"data_mtime": 1751444407, "dep_lines": [5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.enumerations", "typing", "flask", "builtins", "_frozen_importlib", "abc", "flask.blueprints", "flask.helpers", "flask.templating", "flask.wrappers", "mobility", "types", "werkzeug", "werkzeug.wrappers"], "hash": "7972407ec74a54aa86052e2b715f1f2202e7960e", "id": "webservice.apps.routes.main", "ignore_all": false, "interface_hash": "951258febee6f95652529eba13e4661618632fe5", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\routes\\main.py", "plugin_data": null, "size": 331, "suppressed": [], "version_id": "1.16.1"}
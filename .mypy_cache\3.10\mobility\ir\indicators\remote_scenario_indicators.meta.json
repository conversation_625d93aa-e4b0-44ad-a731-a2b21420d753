{"data_mtime": 1753176776, "dep_lines": [5, 6, 7, 9, 4, 8, 1, 2, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.employee", "mobility.ir.transport", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "enum", "mobility.ir.geo_study", "mobility.workers"], "hash": "e7d4dcb127fd9ee43d666d217db1db9e74c34044", "id": "mobility.ir.indicators.remote_scenario_indicators", "ignore_all": false, "interface_hash": "2e95b3a0266c4259416ea576b6682071c879d199", "mtime": 1722327416, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\remote_scenario_indicators.py", "plugin_data": null, "size": 1264, "suppressed": [], "version_id": "1.16.1"}
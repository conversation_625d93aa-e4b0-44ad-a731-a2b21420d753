{".class": "MypyFile", "_fullname": "api_abstraction.tests.otp.test_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "OTPTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.otp.api.OTPTravelTimeAPI", "kind": "Gdef"}, "OTP_APT_MODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.tests.otp.test_api.OTP_APT_MODES", "name": "OTP_APT_MODES", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "OTP_INAPT_MODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.tests.otp.test_api.OTP_INAPT_MODES", "name": "OTP_INAPT_MODES", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TestOTPTravelTimeAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", "name": "TestOTPTravelTimeAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.otp.test_api", "mro": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_arrival_request_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "test_input_time", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_compute_arrival_request_time", "name": "test_compute_arrival_request_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "test_input_time", "expected"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "datetime.time"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_arrival_request_time of TestOTPTravelTimeAPI", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_compute_arrival_request_time", "name": "test_compute_arrival_request_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_filter_itineraries_above_max_transfers_should_filter_out_high_transfer_routes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_filter_itineraries_above_max_transfers_should_filter_out_high_transfer_routes", "name": "test_filter_itineraries_above_max_transfers_should_filter_out_high_transfer_routes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_filter_itineraries_above_max_transfers_should_filter_out_high_transfer_routes of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_filter_itineraries_above_max_transfers_should_handle_missing_transfers_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_filter_itineraries_above_max_transfers_should_handle_missing_transfers_attribute", "name": "test_filter_itineraries_above_max_transfers_should_handle_missing_transfers_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_filter_itineraries_above_max_transfers_should_handle_missing_transfers_attribute of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_filter_itineraries_above_max_transfers_should_return_all_when_none_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_filter_itineraries_above_max_transfers_should_return_all_when_none_match", "name": "test_filter_itineraries_above_max_transfers_should_return_all_when_none_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_filter_itineraries_above_max_transfers_should_return_all_when_none_match of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_get_fastest_itinerary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_get_fastest_itinerary", "name": "test_get_fastest_itinerary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_get_fastest_itinerary of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_get_itinerary_should_return_fastest_if_less_than_3_hours": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_request_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_get_itinerary_should_return_fastest_if_less_than_3_hours", "name": "test_get_itinerary_should_return_fastest_if_less_than_3_hours", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_request_date"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_get_itinerary_should_return_fastest_if_less_than_3_hours of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_get_itinerary_should_return_fastest_if_less_than_3_hours", "name": "test_get_itinerary_should_return_fastest_if_less_than_3_hours", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_get_itinerary_should_return_fastest_if_less_than_3_hours of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_get_itinerary_should_return_first_if_more_than_3_hours": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_request_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_get_itinerary_should_return_first_if_more_than_3_hours", "name": "test_get_itinerary_should_return_first_if_more_than_3_hours", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_request_date"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_get_itinerary_should_return_first_if_more_than_3_hours of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_get_itinerary_should_return_first_if_more_than_3_hours", "name": "test_get_itinerary_should_return_first_if_more_than_3_hours", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_get_itinerary_should_return_first_if_more_than_3_hours of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_compute_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "otp_plan_response", "monkeypatch", "mode", "expected_mode_params", "expected_journey"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_compute_journey", "name": "test_should_compute_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "otp_plan_response", "monkeypatch", "mode", "expected_mode_params", "expected_journey"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "mobility.ir.transport.TransportMode", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "api_abstraction.api.travel_time_api.JourneyAttribute"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_journey of TestOTPTravelTimeAPI", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_compute_journey", "name": "test_should_compute_journey", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_compute_one_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "monkeypatch", "otp_default_router_response", "otp_one_isochrone_response", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_compute_one_isochrone", "name": "test_should_compute_one_isochrone", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_compute_one_isochrone", "name": "test_should_compute_one_isochrone", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_fail_init_if_requests_cant_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "monkeypatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_fail_init_if_requests_cant_connect", "name": "test_should_fail_init_if_requests_cant_connect", "type": null}}, "test_should_fail_to_compute_unhandled_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "monkeypatch", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_fail_to_compute_unhandled_modes", "name": "test_should_fail_to_compute_unhandled_modes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "monkeypatch", "mode"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_fail_to_compute_unhandled_modes of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_fail_to_compute_unhandled_modes", "name": "test_should_fail_to_compute_unhandled_modes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_init_request_date_based_on_territory_availability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "otp_default_router_response", "monkeypatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_init_request_date_based_on_territory_availability", "name": "test_should_init_request_date_based_on_territory_availability", "type": null}}, "test_should_jitter_coordinates_around_given_coords_sorted_by_proximity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_jitter_coordinates_around_given_coords_sorted_by_proximity", "name": "test_should_jitter_coordinates_around_given_coords_sorted_by_proximity", "type": null}}, "test_should_not_fail_to_compute_handled_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "monkeypatch", "mode", "otp_plan_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_not_fail_to_compute_handled_modes", "name": "test_should_not_fail_to_compute_handled_modes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "monkeypatch", "mode", "otp_plan_response"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.ir.transport.TransportMode", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_not_fail_to_compute_handled_modes of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_not_fail_to_compute_handled_modes", "name": "test_should_not_fail_to_compute_handled_modes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_not_jitter_and_return_just_the_given_coordinates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_not_jitter_and_return_just_the_given_coordinates", "name": "test_should_not_jitter_and_return_just_the_given_coordinates", "type": null}}, "test_should_two_jitter_coordinates_around_given_coords_sorted_by_proximity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_should_two_jitter_coordinates_around_given_coords_sorted_by_proximity", "name": "test_should_two_jitter_coordinates_around_given_coords_sorted_by_proximity", "type": null}}, "test_time_should_apply_max_transfers_filter_for_public_transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_apply_max_transfers_filter_for_public_transport", "name": "test_time_should_apply_max_transfers_filter_for_public_transport", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_should_apply_max_transfers_filter_for_public_transport of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_apply_max_transfers_filter_for_public_transport", "name": "test_time_should_apply_max_transfers_filter_for_public_transport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_time_should_apply_max_transfers_filter_for_public_transport of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_should_not_apply_filter_when_max_transfers_is_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_not_apply_filter_when_max_transfers_is_none", "name": "test_time_should_not_apply_filter_when_max_transfers_is_none", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_should_not_apply_filter_when_max_transfers_is_none of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_not_apply_filter_when_max_transfers_is_none", "name": "test_time_should_not_apply_filter_when_max_transfers_is_none", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_time_should_not_apply_filter_when_max_transfers_is_none of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_should_not_apply_max_transfers_filter_for_non_public_transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_not_apply_max_transfers_filter_for_non_public_transport", "name": "test_time_should_not_apply_max_transfers_filter_for_non_public_transport", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_compute_date", "mock_call_requests"], "arg_types": ["api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_should_not_apply_max_transfers_filter_for_non_public_transport of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.test_time_should_not_apply_max_transfers_filter_for_non_public_transport", "name": "test_time_should_not_apply_max_transfers_filter_for_non_public_transport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_time_should_not_apply_max_transfers_filter_for_non_public_transport of TestOTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.otp.test_api.TestOTPTravelTimeAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.otp.test_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "api_abstraction\\tests\\otp\\test_api.py"}
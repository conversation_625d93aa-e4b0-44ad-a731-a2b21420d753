{".class": "MypyFile", "_fullname": "api_abstraction.tests.here.test_here_api_clients", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "HereIntermodalAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.HereIntermodalAPI", "kind": "Gdef"}, "HereIntermodalRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIntermodalRequestParameters", "kind": "Gdef"}, "HereIsolineAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.HereIsolineAPI", "kind": "Gdef"}, "HereIsolineRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIsolineRequestParameters", "kind": "Gdef"}, "HerePublicTransitAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.HerePublicTransitAPI", "kind": "Gdef"}, "HerePublicTransitRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HerePublicTransitRequestParameters", "kind": "Gdef"}, "HereRoutingAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.HereRoutingAPI", "kind": "Gdef"}, "HereRoutingRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereRoutingRequestParameters", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "Polygon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.Polygon", "name": "Polygon", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.tests.here.test_here_api_clients.Polygon", "source_any": null, "type_of_any": 3}}}, "PrivateVehicleEnabled": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.PrivateVehicleEnabled", "kind": "Gdef"}, "RangeType": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.RangeType", "kind": "Gdef"}, "RoutingMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.RoutingMode", "kind": "Gdef"}, "TestHereIntermodalAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "name": "TestHereIntermodalAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.here.test_here_api_clients", "mro": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_route_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes", "name": "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_input_parameters_should_handle_both_include_and_exclude_vehicle_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_format_input_parameters_should_handle_both_include_and_exclude_vehicle_modes", "name": "test_format_input_parameters_should_handle_both_include_and_exclude_vehicle_modes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_handle_both_include_and_exclude_vehicle_modes of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_input_parameters_should_return_correct_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_format_input_parameters_should_return_correct_dict", "name": "test_format_input_parameters_should_return_correct_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_return_correct_dict of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_format_result_should_return_journey_attribute", "name": "test_format_result_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_should_return_journey_attribute of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_with_invalid_structure_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.test_format_result_with_invalid_structure_should_raise", "name": "test_format_result_with_invalid_structure_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_with_invalid_structure_should_raise of TestHereIntermodalAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.here.test_here_api_clients.TestHereIntermodalAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHereIsolineAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI", "name": "TestHereIsolineAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.here.test_here_api_clients", "mro": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_isoline_without_mode_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.test_compute_isoline_without_mode_should_raise", "name": "test_compute_isoline_without_mode_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isoline_without_mode_should_raise of TestHereIsolineAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_compute_isoline_without_range_values_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.test_compute_isoline_without_range_values_should_raise", "name": "test_compute_isoline_without_range_values_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isoline_without_range_values_should_raise of TestHereIsolineAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_input_parameters_should_return_correct_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.test_format_input_parameters_should_return_correct_dict", "name": "test_format_input_parameters_should_return_correct_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_return_correct_dict of TestHereIsolineAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_should_return_polygon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.test_format_result_should_return_polygon", "name": "test_format_result_should_return_polygon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_should_return_polygon of TestHereIsolineAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_with_invalid_structure_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.test_format_result_with_invalid_structure_should_raise", "name": "test_format_result_with_invalid_structure_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_with_invalid_structure_should_raise of TestHereIsolineAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.here.test_here_api_clients.TestHereIsolineAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHerePublicTransitAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "name": "TestHerePublicTransitAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.here.test_here_api_clients", "mro": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_route_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes", "name": "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_handle_both_include_and_exclude_transit_modes of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_input_parameters_should_return_correct_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_format_input_parameters_should_return_correct_dict", "name": "test_format_input_parameters_should_return_correct_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_return_correct_dict of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_format_result_should_return_journey_attribute", "name": "test_format_result_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_should_return_journey_attribute of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_with_invalid_structure_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_format_result_with_invalid_structure_should_raise", "name": "test_format_result_with_invalid_structure_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_with_invalid_structure_should_raise of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_public_transit_api_should_send_max_transfers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_get"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_public_transit_api_should_send_max_transfers", "name": "test_public_transit_api_should_send_max_transfers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_get"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_public_transit_api_should_send_max_transfers of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.test_public_transit_api_should_send_max_transfers", "name": "test_public_transit_api_should_send_max_transfers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_public_transit_api_should_send_max_transfers of TestHerePublicTransitAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.here.test_here_api_clients.TestHerePublicTransitAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHereRoutingAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "name": "TestHereRoutingAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.here.test_here_api_clients", "mro": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_route_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_compute_route_should_return_journey_attribute", "name": "test_compute_route_should_return_journey_attribute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_format_result", "mock_make_request"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "unittest.mock.Mock", "unittest.mock.Mock"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_should_return_journey_attribute of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_format_input_parameters_should_return_correct_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_format_input_parameters_should_return_correct_dict", "name": "test_format_input_parameters_should_return_correct_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_should_return_correct_dict of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_input_parameters_with_missing_mode_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_format_input_parameters_with_missing_mode_should_raise", "name": "test_format_input_parameters_with_missing_mode_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_input_parameters_with_missing_mode_should_raise of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_should_return_journey_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_format_result_should_return_journey_attribute", "name": "test_format_result_should_return_journey_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_should_return_journey_attribute of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_result_with_invalid_structure_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.test_format_result_with_invalid_structure_should_raise", "name": "test_format_result_with_invalid_structure_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_format_result_with_invalid_structure_should_raise of TestHereRoutingAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.here.test_here_api_clients.TestHereRoutingAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransitMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.TransitMode", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "VehicleMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.VehicleMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_here_api_clients.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_calculate_total_duration_from_timestamps": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients._calculate_total_duration_from_timestamps", "kind": "Gdef"}, "_extract_route_distance": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients._extract_route_distance", "kind": "Gdef"}, "_validate_here_routes": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients._validate_here_routes", "kind": "Gdef"}, "convert_here_result_to_journey_attribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.convert_here_result_to_journey_attribute", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_max_transfers_in_range": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.here_api_clients.get_max_transfers_in_range", "kind": "Gdef"}, "make_geo_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["lat", "lon"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.make_geo_coordinates", "name": "make_geo_coordinates", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["lat", "lon"], "arg_types": ["builtins.float", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_geo_coordinates", "ret_type": "mobility.ir.geo_study.GeoCoordinates", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_intermodal_request_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "max_transfers", "walk_speed", "bicycle_max_distance", "private_vehicle_enabled"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.make_intermodal_request_params", "name": "make_intermodal_request_params", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "max_transfers", "walk_speed", "bicycle_max_distance", "private_vehicle_enabled"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_intermodal_request_params", "ret_type": "mobility.ir.here.HereIntermodalRequestParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_isoline_request_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["center_point", "mode", "arrival_time", "range_values", "range_type", "routing_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.make_isoline_request_params", "name": "make_isoline_request_params", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["center_point", "mode", "arrival_time", "range_values", "range_type", "routing_mode"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_isoline_request_params", "ret_type": "mobility.ir.here.HereIsolineRequestParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_public_transit_request_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "max_transfers", "walk_speed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.make_public_transit_request_params", "name": "make_public_transit_request_params", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "max_transfers", "walk_speed"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_public_transit_request_params", "ret_type": "mobility.ir.here.HerePublicTransitRequestParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_routing_request_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "routing_mode", "return_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.make_routing_request_params", "name": "make_routing_request_params", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["origin", "destination", "mode", "arrival_time", "routing_mode", "return_values"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_routing_request_params", "ret_type": "mobility.ir.here.HereRoutingRequestParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "test_calculate_total_duration_from_timestamps_should_return_correct_duration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_calculate_total_duration_from_timestamps_should_return_correct_duration", "name": "test_calculate_total_duration_from_timestamps_should_return_correct_duration", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_calculate_total_duration_from_timestamps_should_return_correct_duration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_calculate_total_duration_from_timestamps_with_invalid_data_should_raise_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_calculate_total_duration_from_timestamps_with_invalid_data_should_raise_error", "name": "test_calculate_total_duration_from_timestamps_with_invalid_data_should_raise_error", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_calculate_total_duration_from_timestamps_with_invalid_data_should_raise_error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_calculate_total_duration_from_timestamps_with_missing_fields_should_raise_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_calculate_total_duration_from_timestamps_with_missing_fields_should_raise_error", "name": "test_calculate_total_duration_from_timestamps_with_missing_fields_should_raise_error", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_calculate_total_duration_from_timestamps_with_missing_fields_should_raise_error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_empty_routes_and_no_notices_should_raise_apifail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_empty_routes_and_no_notices_should_raise_apifail", "name": "test_empty_routes_and_no_notices_should_raise_apifail", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_empty_routes_and_no_notices_should_raise_apifail", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_empty_routes_with_notices_missing_title_should_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_empty_routes_with_notices_missing_title_should_default", "name": "test_empty_routes_with_notices_missing_title_should_default", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_empty_routes_with_notices_missing_title_should_default", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_empty_routes_with_notices_should_raise_apifail_with_titles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_empty_routes_with_notices_should_raise_apifail_with_titles", "name": "test_empty_routes_with_notices_should_raise_apifail_with_titles", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_empty_routes_with_notices_should_raise_apifail_with_titles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_extract_route_distance_should_sum_lengths_of_sections": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_extract_route_distance_should_sum_lengths_of_sections", "name": "test_extract_route_distance_should_sum_lengths_of_sections", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_extract_route_distance_should_sum_lengths_of_sections", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_get_max_transfers_in_range_forces_value_in_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_get_max_transfers_in_range_forces_value_in_range", "name": "test_get_max_transfers_in_range_forces_value_in_range", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_get_max_transfers_in_range_forces_value_in_range", "name": "test_get_max_transfers_in_range_forces_value_in_range", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_get_max_transfers_in_range_should_log_error_with_invalid_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "caplog"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_get_max_transfers_in_range_should_log_error_with_invalid_values", "name": "test_get_max_transfers_in_range_should_log_error_with_invalid_values", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_get_max_transfers_in_range_should_log_error_with_invalid_values", "name": "test_get_max_transfers_in_range_should_log_error_with_invalid_values", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_multiple_routes_should_raise_apifail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_multiple_routes_should_raise_apifail", "name": "test_multiple_routes_should_raise_apifail", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_multiple_routes_should_raise_apifail", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_result_with_invalid_structure_should_raise": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_result_with_invalid_structure_should_raise", "name": "test_result_with_invalid_structure_should_raise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["result"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_result_with_invalid_structure_should_raise", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_result_with_invalid_structure_should_raise", "name": "test_result_with_invalid_structure_should_raise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_validate_here_routes_with_no_routes_and_no_notices_should_raise_apifail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_validate_here_routes_with_no_routes_and_no_notices_should_raise_apifail", "name": "test_validate_here_routes_with_no_routes_and_no_notices_should_raise_apifail", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_validate_here_routes_with_no_routes_and_no_notices_should_raise_apifail", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_validate_here_routes_with_single_route_should_return_route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_here_api_clients.test_validate_here_routes_with_single_route_should_return_route", "name": "test_validate_here_routes_with_single_route_should_return_route", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_validate_here_routes_with_single_route_should_return_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "api_abstraction\\tests\\here\\test_here_api_clients.py"}
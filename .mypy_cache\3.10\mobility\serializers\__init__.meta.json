{"data_mtime": 1752154451, "dep_lines": [1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mobility.serializers.excel_serializer", "mobility.serializers.geopackage_serializer", "mobility.serializers.json_serializer", "mobility.serializers.report_serializer", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "4d4223c3ec892da3ec51d2796806f5d39974b5e4", "id": "mobility.serializers", "ignore_all": false, "interface_hash": "b125f2c415707ed4ea2bc6d2bc4a878cc862a17c", "mtime": 1722327417, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\__init__.py", "plugin_data": null, "size": 286, "suppressed": [], "version_id": "1.16.1"}
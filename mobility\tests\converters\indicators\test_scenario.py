import pytest

from mobility.converters.indicators.scenario import (
    compute_average_travel_time,
    compute_carbon_emission,
    compute_distance,
    compute_scenario_indicators,
)
from mobility.funky import ImmutableDict
from mobility.ir.transport import CommuterProfile, TransportMode
from mobility.quantity import gramEC, meters, seconds, trip


class TestComputeScenarioIndicators:
    def test_should_fail_to_compute_indicators_from_empty_scenario(
        self,
        consolidated_scenario_factory,
    ):
        scenario = consolidated_scenario_factory(
            commutes=frozenset(),
        )

        with pytest.raises(ValueError):
            compute_scenario_indicators(scenario)

    def test_should_compute_indicators_from_basic_scenario(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        new_scenario_indicators,
        modal_commute_data_factory,
        geo_employee_factory,
    ):
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(id=0),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 1000}),
                            distance=ImmutableDict({TransportMode.CAR: 2000}),
                            emission=ImmutableDict({TransportMode.CAR: 3000}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(id=1),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 2000}),
                            distance=ImmutableDict({TransportMode.WALK: 1000}),
                            emission=ImmutableDict({TransportMode.WALK: 0}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(id=2),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 1500}),
                            distance=ImmutableDict({TransportMode.CAR: 3000}),
                            emission=ImmutableDict({TransportMode.CAR: 4000}),
                        ),
                    ),
                }
            ),
        )

        indicators = compute_scenario_indicators(scenario)

        assert indicators == new_scenario_indicators(
            nickname=scenario.data.nickname,
            average_travel_time=1500 * seconds,
            carbon_emission=7000 * gramEC / trip,
            employees_count_per_mode=ImmutableDict(
                {
                    TransportMode.CAR: 2,
                    TransportMode.BICYCLE: 0,
                    TransportMode.WALK: 1,
                    TransportMode.PUBLIC_TRANSPORT: 0,
                    TransportMode.CARPOOLING: 0,
                    TransportMode.ELECTRIC_BICYCLE: 0,
                    TransportMode.ELECTRIC_CAR: 0,
                    TransportMode.MOTORCYCLE: 0,
                    TransportMode.AIRPLANE: 0,
                    TransportMode.ELECTRIC_MOTORCYCLE: 0,
                    TransportMode.FAST_BICYCLE: 0,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
                }
            ),
            costs=indicators.costs,
            zfe_impact_calendar=indicators.zfe_impact_calendar,
            employees_count_per_profile=ImmutableDict(
                {
                    CommuterProfile.CAR_USER: 2,
                    CommuterProfile.WALKER: 1,
                    CommuterProfile.CARPOOLER: 0,
                    CommuterProfile.COWORKER: 0,
                    CommuterProfile.REMOTE_WORKER: 0,
                }
            ),
        )

    def test_should_compute_data_on_commute_list(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration=ImmutableDict({TransportMode.CAR: 1000}),
                    distance=ImmutableDict({TransportMode.CAR: 2000}),
                    emission=ImmutableDict({TransportMode.CAR: 3000}),
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 2000}),
                    distance=ImmutableDict({TransportMode.WALK: 1000}),
                    emission=ImmutableDict({TransportMode.WALK: 0}),
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration=ImmutableDict({TransportMode.CAR: 3000}),
                    distance=ImmutableDict({TransportMode.CAR: 3000}),
                    emission=ImmutableDict({TransportMode.CAR: 4000}),
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.PUBLIC_TRANSPORT,
                    duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 4000}),
                    distance=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 4000}),
                    emission=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 3500}),
                ),
            ),
        ]

        average_travel_time = compute_average_travel_time(commutes)
        carbon_emission = compute_carbon_emission(commutes)
        distance = compute_distance(commutes)

        assert average_travel_time == 2500 * seconds
        assert distance == 10000 * meters
        assert carbon_emission == 10500 * gramEC / trip

    def test_should_compute_data_on_empty_commute_list(self):
        commutes = []

        average_travel_time = compute_average_travel_time(commutes)
        carbon_emission = compute_carbon_emission(commutes)
        distance = compute_distance(commutes)

        assert average_travel_time is None
        assert distance == 0 * meters
        assert carbon_emission == 0 * gramEC / trip

{"data_mtime": 1752154445, "dep_lines": [8, 9, 10, 11, 13, 15, 16, 18, 19, 14, 17, 1, 2, 3, 4, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.travel_time_api", "mobility.builders.address_expander", "mobility.ir.employee", "mobility.ir.transport", "mobility.serializers.report_quantity_formatters", "mobility.workers.emission_computer", "mobility.funky", "mobility.quantity", "sys", "time", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_sitebuiltins", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.geocode_api", "configparser", "enum", "functools", "mobility.builders", "mobility.ir", "mobility.ir.geo_study", "mobility.serializers", "typing_extensions", "mobility.workers"], "hash": "2a2fad02a8a2e9375e1923700ada52cadd17cb36", "id": "audit.compare_travel_timers", "ignore_all": false, "interface_hash": "38dca0a41e034b2daf7db226a0aefd1d1412a6a1", "mtime": 1723449306, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "audit\\compare_travel_timers.py", "plugin_data": null, "size": 7806, "suppressed": ["pandas"], "version_id": "1.16.1"}
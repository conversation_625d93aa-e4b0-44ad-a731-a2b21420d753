{"data_mtime": 1752154445, "dep_lines": [8, 9, 10, 12, 13, 15, 16, 17, 18, 19, 20, 21, 23, 24, 14, 22, 1, 2, 3, 4, 6, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.geocode_api", "mobility.builders.crit_air_computer", "mobility.builders.json_builder", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.serializers.json_serializer", "mobility.workers.distance_computer", "mobility.funky", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "datetime", "typing", "numpy", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "configparser", "enum", "functools", "mobility.builders", "mobility.ir", "mobility.ir.crit_air", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.serializers", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "typing_extensions"], "hash": "622571a198344d11d6f78e28e3b1890cd84483da", "id": "scripts.regeocode", "ignore_all": false, "interface_hash": "03448828843c4cdc80cc72798eebc67ded29e41b", "mtime": 1743001148, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\regeocode.py", "plugin_data": null, "size": 11213, "suppressed": [], "version_id": "1.16.1"}
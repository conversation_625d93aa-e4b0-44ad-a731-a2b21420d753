{"data_mtime": 1751444409, "dep_lines": [4, 5, 12, 13, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.crit_air", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.funky", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.scenario_data", "mobility.ir.site", "typing_extensions"], "hash": "2e2a13be24684d4f7f8eb0dc912fa70459e6825c", "id": "mobility.tests.ir.test_timed_study", "ignore_all": false, "interface_hash": "8229942fd720671062f72e6c6f02af55cdd3e01c", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\test_timed_study.py", "plugin_data": null, "size": 13707, "suppressed": [], "version_id": "1.16.1"}
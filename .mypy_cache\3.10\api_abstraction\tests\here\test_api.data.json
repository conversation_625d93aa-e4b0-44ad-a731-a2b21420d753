{".class": "MypyFile", "_fullname": "api_abstraction.tests.here.test_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "HereIntermodalRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIntermodalRequestParameters", "kind": "Gdef"}, "HereIsolineRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereIsolineRequestParameters", "kind": "Gdef"}, "HerePublicTransitRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HerePublicTransitRequestParameters", "kind": "Gdef"}, "HereRoutingRequestParameters": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.HereRoutingRequestParameters", "kind": "Gdef"}, "HereTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.api.HereTravelTimeAPI", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "RangeType": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.RangeType", "kind": "Gdef"}, "RoutingMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.RoutingMode", "kind": "Gdef"}, "TestHereTravelTimeAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "name": "TestHereTravelTimeAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.here.test_api", "mro": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_compute_isochrone_logs_warning_on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_logs_warning_on_exception", "name": "test_compute_isochrone_logs_warning_on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isochrone_logs_warning_on_exception of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_compute_isochrone_raises_api_inapt_for_public_transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_raises_api_inapt_for_public_transport", "name": "test_compute_isochrone_raises_api_inapt_for_public_transport", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isochrone_raises_api_inapt_for_public_transport of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_compute_isochrone_returns_geometry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mock_json", "mock_geopandas", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_returns_geometry", "name": "test_compute_isochrone_returns_geometry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mock_json", "mock_geopandas", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isochrone_returns_geometry of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_returns_geometry", "name": "test_compute_isochrone_returns_geometry", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_compute_isochrone_returns_geometry of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_compute_isochrone_with_arrival_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_with_arrival_time", "name": "test_compute_isochrone_with_arrival_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isochrone_with_arrival_time of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_compute_isochrone_with_arrival_time", "name": "test_compute_isochrone_with_arrival_time", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_isochrone_with_arrival_time of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_at_uses_car_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_car_mode", "name": "test_time_at_uses_car_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_at_uses_car_mode of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_car_mode", "name": "test_time_at_uses_car_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_at_uses_car_mode of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_at_uses_intermodal_api_for_intermodalmodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api", "mode", "expected_pre_transit_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_intermodal_api_for_intermodalmodes", "name": "test_time_at_uses_intermodal_api_for_intermodalmodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api", "mode", "expected_pre_transit_mode"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI", "mobility.ir.transport.TransportMode", "mobility.ir.here.VehicleMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_at_uses_intermodal_api_for_intermodalmodes of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_intermodal_api_for_intermodalmodes", "name": "test_time_at_uses_intermodal_api_for_intermodalmodes", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_time_at_uses_transit_api_with_PT_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_transit_api_with_PT_mode", "name": "test_time_at_uses_transit_api_with_PT_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_at_uses_transit_api_with_PT_mode of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_at_uses_transit_api_with_PT_mode", "name": "test_time_at_uses_transit_api_with_PT_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_datetime", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_at_uses_transit_api_with_PT_mode of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_time_catches_exceptions_and_raises_api_fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.test_time_catches_exceptions_and_raises_api_fail", "name": "test_time_catches_exceptions_and_raises_api_fail", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "here_api"], "arg_types": ["api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "api_abstraction.here.api.HereTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_time_catches_exceptions_and_raises_api_fail of TestHereTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.here.test_api.TestHereTravelTimeAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "VehicleMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.here.VehicleMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.here.test_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "here_api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.here.test_api.here_api", "name": "here_api", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "here_api", "ret_type": "api_abstraction.here.api.HereTravelTimeAPI", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.here.test_api.here_api", "name": "here_api", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "here_api", "ret_type": "api_abstraction.here.api.HereTravelTimeAPI", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "make_geo_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["lat", "lon"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_api.make_geo_coordinates", "name": "make_geo_coordinates", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["lat", "lon"], "arg_types": ["builtins.float", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_geo_coordinates", "ret_type": "mobility.ir.geo_study.GeoCoordinates", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_od": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.here.test_api.make_od", "name": "make_od", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["origin", "destination"], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_od", "ret_type": {".class": "TupleType", "implicit": false, "items": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "api_abstraction\\tests\\here\\test_api.py"}
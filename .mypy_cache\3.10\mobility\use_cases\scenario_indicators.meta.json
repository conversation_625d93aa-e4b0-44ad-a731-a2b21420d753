{"data_mtime": 1753090389, "dep_lines": [11, 12, 13, 14, 17, 18, 5, 8, 9, 10, 21, 22, 23, 24, 25, 33, 34, 35, 39, 40, 41, 7, 32, 1, 2, 3, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "api_abstraction.api.travel_time_api", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.new_indicators", "mobility.ir.scenario", "mobility.ir.site", "mobility.ir.study", "mobility.ir.transport", "mobility.repositories.travel_repository", "mobility.use_cases.base_use_case", "mobility.use_cases.compute_trip_costs", "mobility.workers.cost_computer", "mobility.workers.ideal_scenario", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "configparser", "enum", "fractions", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.study_types", "mobility.repositories", "mobility.workers", "mobility.workers.health_cost_calculator", "numbers", "types", "typing_extensions"], "hash": "0fc9126ecd8e2c9e205db3749256474f32ea1470", "id": "mobility.use_cases.scenario_indicators", "ignore_all": false, "interface_hash": "08fb56250c2c718168941e9878ba4b938bb0d08c", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\use_cases\\scenario_indicators.py", "plugin_data": null, "size": 15564, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "api_abstraction.tests.google.test_routes_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiTimeout": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiTimeout", "kind": "Gdef"}, "ComputeRoutesResponse": {".class": "SymbolTableNode", "cross_ref": "google.maps.routing_v2.types.routes_service.ComputeRoutesResponse", "kind": "Gdef"}, "Duration": {".class": "SymbolTableNode", "cross_ref": "google.protobuf.duration_pb2.Duration", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "GoogleRequestParameters": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.base_google_api.GoogleRequestParameters", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "Route": {".class": "SymbolTableNode", "cross_ref": "google.maps.routing_v2.types.route.Route", "kind": "Gdef"}, "RoutesAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.routes_api.RoutesAPI", "kind": "Gdef"}, "TestRoutesAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "name": "TestRoutesAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_routes_api", "mro": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "builtins.object"], "names": {".class": "SymbolTable", "test_api_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_api_error_message_is_explicit", "name": "test_api_error_message_is_explicit", "type": null}}, "test_compute_route_fails_with_proper_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_compute_route_fails_with_proper_exceptions", "name": "test_compute_route_fails_with_proper_exceptions", "type": null}}, "test_compute_route_formats_request_and_returns_journey_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_routes_client", "mock_route_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_compute_route_formats_request_and_returns_journey_attributes", "name": "test_compute_route_formats_request_and_returns_journey_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_routes_client", "mock_route_response"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "google.maps.routing_v2.types.routes_service.ComputeRoutesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_formats_request_and_returns_journey_attributes of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_compute_route_includes_field_mask_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_routes_client", "mock_route_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_compute_route_includes_field_mask_headers", "name": "test_compute_route_includes_field_mask_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_routes_client", "mock_route_response"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "google.maps.routing_v2.types.routes_service.ComputeRoutesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_includes_field_mask_headers of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_deadline_exceeded_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_deadline_exceeded_message_is_explicit", "name": "test_deadline_exceeded_message_is_explicit", "type": null}}, "test_extract_duration_and_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_extract_duration_and_distance", "name": "test_extract_duration_and_distance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_extract_duration_and_distance of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_formats_input_parameters_correctly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_formats_input_parameters_correctly", "name": "test_formats_input_parameters_correctly", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_formats_input_parameters_correctly of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_maps_transport_modes_correctly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_maps_transport_modes_correctly", "name": "test_maps_transport_modes_correctly", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "mobility.ir.transport.TransportMode", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_maps_transport_modes_correctly of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_maps_transport_modes_correctly", "name": "test_maps_transport_modes_correctly", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "mobility.ir.transport.TransportMode", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_maps_transport_modes_correctly of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_raises_error_for_unsupported_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_raises_error_for_unsupported_mode", "name": "test_raises_error_for_unsupported_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_for_unsupported_mode of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_raises_error_for_unsupported_mode", "name": "test_raises_error_for_unsupported_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_for_unsupported_mode of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_raises_error_if_no_routes_returned": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_routes_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_raises_error_if_no_routes_returned", "name": "test_raises_error_if_no_routes_returned", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_routes_client"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_no_routes_returned of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_service_unavailable_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_service_unavailable_message_is_explicit", "name": "test_service_unavailable_message_is_explicit", "type": null}}, "test_skips_car_parameters_for_public_transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_skips_car_parameters_for_public_transport", "name": "test_skips_car_parameters_for_public_transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_routes_api.TestRoutesAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_skips_car_parameters_for_public_transport of TestRoutesAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_too_many_requests_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_too_many_requests_message_is_explicit", "name": "test_too_many_requests_message_is_explicit", "type": null}}, "test_unknown_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_routes_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.test_unknown_error_message_is_explicit", "name": "test_unknown_error_message_is_explicit", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_routes_api.TestRoutesAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_routes_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "fake_compute_route@20": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_routes_api", "mro": ["api_abstraction.tests.google.test_routes_api.fake_compute_route@20", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["api_abstraction.tests.google.test_routes_api.fake_compute_route@20", "builtins.Exception"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of fake_compute_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_routes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20.compute_routes", "name": "compute_routes", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["api_abstraction.tests.google.test_routes_api.fake_compute_route@20", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_routes of fake_compute_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "e": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20.e", "name": "e", "setter_type": null, "type": "builtins.Exception"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_routes_api.fake_compute_route@20", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_routes_exception_raiser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_routes_api.fake_routes_exception_raiser", "name": "fake_routes_exception_raiser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.fake_routes_exception_raiser", "name": "fake_routes_exception_raiser", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fake_routes_exception_raiser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_route_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_routes_api.mock_route_response", "name": "mock_route_response", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_route_response", "ret_type": "google.maps.routing_v2.types.routes_service.ComputeRoutesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.mock_route_response", "name": "mock_route_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_route_response", "ret_type": "google.maps.routing_v2.types.routes_service.ComputeRoutesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_routes_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "api_abstraction.tests.google.test_routes_api.mock_routes_client", "name": "mock_routes_client", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_routes_client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_routes_api.mock_routes_client", "name": "mock_routes_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_routes_client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "routes_api_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}}, "path": "api_abstraction\\tests\\google\\test_routes_api.py"}
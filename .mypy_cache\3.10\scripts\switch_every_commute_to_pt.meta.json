{"data_mtime": 1752154427, "dep_lines": [4, 5, 6, 7, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.ir.commute_data", "mobility.ir.transport", "mobility.serializers.json_serializer", "dataclasses", "datetime", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "mobility", "mobility.builders", "mobility.funky", "mobility.ir", "mobility.ir.crit_air", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.quantity", "mobility.serializers", "typing", "typing_extensions"], "hash": "2bbb70f1eac635e50b07826c6f4abdd7a74f49e1", "id": "scripts.switch_every_commute_to_pt", "ignore_all": false, "interface_hash": "9828da95bf6f31c144b2e6736a0f8a8a3f6690df", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\switch_every_commute_to_pt.py", "plugin_data": null, "size": 1767, "suppressed": [], "version_id": "1.16.1"}
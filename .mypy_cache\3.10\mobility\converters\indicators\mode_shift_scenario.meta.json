{"data_mtime": 1753087664, "dep_lines": [6, 7, 16, 14, 15, 25, 26, 35, 36, 13, 27, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.remote_scenario", "mobility.converters.indicators.scenario", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.ir", "mobility.ir.cost", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.workers", "mobility.workers.health_cost_calculator", "typing_extensions"], "hash": "c5c669335faa9b3ffaa86fd0935b7774cfd63c1e", "id": "mobility.converters.indicators.mode_shift_scenario", "ignore_all": false, "interface_hash": "01b7c28a98806d6561bb3614d69c547b9b79738e", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\mode_shift_scenario.py", "plugin_data": null, "size": 9614, "suppressed": [], "version_id": "1.16.1"}
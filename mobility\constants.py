import os

from mobility.ir.transport import TransportMode
from mobility.quantity import cents, kilometers

NOT_APPLICABLE = "N.A."
SITE_ADDRESSES_SHEET_NAME = "Sites"
EMPLOYEE_ADDRESSES_SHEET_NAME = "Employés"
PARAMETERS_SHEET_NAME = "Paramètres d'étude"
SITES_SHEET_NAME = "Data (site)"
EMPLOYEE_SHEET_NAME = "Data (Employés)"
METADATA_SHEET_NAME = "Data (Metadata)"
COSTS_SHEET_NAME = "Data (Costs)"
JSON_VERSION = "v18"
STUDY_EXCEL_TEMPLATE_PATH = os.path.join(
    os.path.dirname(os.path.realpath(__file__)), "templates", "template_study.xlsx"
)
DIAGNOSIS_EXCEL_TEMPLATE_PATH = os.path.join(
    os.path.dirname(os.path.realpath(__file__)), "templates", "template_diagnosis.xlsx"
)
JSON_SCHEMA_DIR = os.path.join(
    os.path.dirname(os.path.realpath(__file__)), "json_schemas"
)
DEFAULT_MODE_WHEN_NO_AVAILABLE_DURATIONS = TransportMode.WALK
DISTANCE_COST_BY_CAR = 52 * cents / kilometers
FONTS_DIR = os.path.join(os.path.dirname(os.path.realpath(__file__)), "static", "fonts")
GEOPACKAGE_TEMPLATE_PATH = os.path.join(
    os.path.dirname(os.path.realpath(__file__)), "templates", "template.gpkg"
)
WALK_SPEED_METERS_PER_SECOND = 1.12  # 4 km/h

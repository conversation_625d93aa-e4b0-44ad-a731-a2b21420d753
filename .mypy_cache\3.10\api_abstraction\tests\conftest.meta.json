{"data_mtime": 1751444468, "dep_lines": [6, 7, 6, 1, 2, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.tests.otp_plan_response", "mobility.ir.transport", "api_abstraction.tests", "datetime", "typing", "pytest", "api_abstraction", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "enum", "mobility", "mobility.ir", "typing_extensions"], "hash": "c44012beb31cbb37cbe084cf7c8fa91149684d40", "id": "api_abstraction.tests.conftest", "ignore_all": false, "interface_hash": "fbbd52dfa60cf58e4ac0efea2cf169bd19ca1e7e", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\conftest.py", "plugin_data": null, "size": 129923, "suppressed": [], "version_id": "1.16.1"}
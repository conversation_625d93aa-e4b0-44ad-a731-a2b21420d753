{".class": "MypyFile", "_fullname": "mobility.workers.zfe_impact_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractTerritoriesRepository": {".class": "SymbolTableNode", "cross_ref": "mobility.repositories.abstract_repositories.AbstractTerritoriesRepository", "kind": "Gdef"}, "CritAirCategory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.crit_air.CritAirCategory", "kind": "Gdef"}, "CritAirComputer": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.crit_air_computer.CritAirComputer", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TerritoryDatabase": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.territory_database.TerritoryDatabase", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "ZFECalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.zfe.ZFECalendar", "kind": "Gdef"}, "ZFECityListing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing", "name": "ZFECityListing", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 17, "name": "calendar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "zfe_name", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.workers.zfe_impact_computer", "mro": ["mobility.workers.zfe_impact_computer.ZFECityListing", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "calendar", "zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "calendar", "zfe_name"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFECityListing", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ZFECityListing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "calendar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zfe_name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["calendar", "zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["calendar", "zfe_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ZFECityListing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["calendar", "zfe_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ZFECityListing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "calendar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.calendar", "name": "calendar", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.zfe.ZFECalendar"}}}, "zfe_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.zfe_name", "name": "zfe_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.zfe_impact_computer.ZFECityListing.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.workers.zfe_impact_computer.ZFECityListing", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZFEDefinition": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.zfe_data.ZFEDefinition", "kind": "Gdef"}, "ZFEImpact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact", "name": "ZFEImpact", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 23, "name": "nb_workers", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 24, "name": "nb_drivers", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "nb_impacted_drivers", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "nb_impact_by_zfe", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 27, "name": "percent_workers_impacted", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "percent_drivers_impacted", "type": "builtins.float"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.workers.zfe_impact_computer", "mro": ["mobility.workers.zfe_impact_computer.ZFEImpact", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpact", "mobility.workers.zfe_impact_computer.ZFEImpact"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__add__ of ZFEImpact", "ret_type": "mobility.workers.zfe_impact_computer.ZFEImpact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "nb_workers", "nb_drivers", "nb_impacted_drivers", "nb_impact_by_zfe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "nb_workers", "nb_drivers", "nb_impacted_drivers", "nb_impact_by_zfe"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpact", "builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ZFEImpact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nb_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_drivers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_impacted_drivers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_impact_by_zfe"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpact"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of ZFEImpact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nb_workers", "nb_drivers", "nb_impacted_drivers", "nb_impact_by_zfe", "percent_workers_impacted", "percent_drivers_impacted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nb_workers", "nb_drivers", "nb_impacted_drivers", "nb_impact_by_zfe", "percent_workers_impacted", "percent_drivers_impacted"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ZFEImpact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nb_workers", "nb_drivers", "nb_impacted_drivers", "nb_impact_by_zfe", "percent_workers_impacted", "percent_drivers_impacted"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ZFEImpact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpact"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__post_init__ of ZFEImpact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nb_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.nb_drivers", "name": "nb_drivers", "setter_type": null, "type": "builtins.float"}}, "nb_impact_by_zfe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.nb_impact_by_zfe", "name": "nb_impact_by_zfe", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "nb_impacted_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.nb_impacted_drivers", "name": "nb_impacted_drivers", "setter_type": null, "type": "builtins.float"}}, "nb_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.nb_workers", "name": "nb_workers", "setter_type": null, "type": "builtins.float"}}, "percent_drivers_impacted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.percent_drivers_impacted", "name": "percent_drivers_impacted", "setter_type": null, "type": "builtins.float"}}, "percent_workers_impacted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.percent_workers_impacted", "name": "percent_workers_impacted", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.zfe_impact_computer.ZFEImpact.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.workers.zfe_impact_computer.ZFEImpact", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZFEImpactCalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.int", "mobility.workers.zfe_impact_computer.ZFEImpact"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ZFEImpactComputer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer", "name": "ZFEImpactComputer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.workers.zfe_impact_computer", "mro": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "territories_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "territories_dir"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ZFEImpactComputer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_proportion_impacted_drivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "year", "worker_citycode", "company_citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_proportion_impacted_drivers", "name": "compute_proportion_impacted_drivers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "year", "worker_citycode", "company_citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.int", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_proportion_impacted_drivers of ZFEImpactComputer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_zfe_cumulative_events_calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_cumulative_events_calendar", "name": "compute_zfe_cumulative_events_calendar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_cumulative_events_calendar of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_cumulative_events_calendar", "name": "compute_zfe_cumulative_events_calendar", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_cumulative_events_calendar of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compute_zfe_events_calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_events_calendar", "name": "compute_zfe_events_calendar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_events_calendar of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_events_calendar", "name": "compute_zfe_events_calendar", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_events_calendar of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compute_zfe_impact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "company_citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_impact", "name": "compute_zfe_impact", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "company_citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_impact of ZFEImpactComputer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_zfe_impact_for_company_and_employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "company_citycode", "driver_citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_impact_for_company_and_employee", "name": "compute_zfe_impact_for_company_and_employee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "company_citycode", "driver_citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_impact_for_company_and_employee of ZFEImpactComputer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_zfe_impact_for_company_and_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "company_citycode", "nb_non_driver_employees", "drivers_citycodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_impact_for_company_and_employees", "name": "compute_zfe_impact_for_company_and_employees", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "company_citycode", "nb_non_driver_employees", "drivers_citycodes"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_impact_for_company_and_employees of ZFEImpactComputer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_zfe_max_impact_for_company_and_employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "company_citycode", "driver_citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.compute_zfe_max_impact_for_company_and_employee", "name": "compute_zfe_max_impact_for_company_and_employee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "company_citycode", "driver_citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zfe_max_impact_for_company_and_employee of ZFEImpactComputer", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crit_air_computer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.crit_air_computer", "name": "crit_air_computer", "setter_type": null, "type": "mobility.builders.crit_air_computer.CritAirComputer"}}, "crit_air_data_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.crit_air_data_date", "name": "crit_air_data_date", "setter_type": null, "type": "builtins.int"}}, "get_all_crit_air_eventually_forbidden": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.get_all_crit_air_eventually_forbidden", "name": "get_all_crit_air_eventually_forbidden", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_all_crit_air_eventually_forbidden of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.crit_air.CritAirCategory"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_forbidden_crit_air_in_year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "citycode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.get_forbidden_crit_air_in_year", "name": "get_forbidden_crit_air_in_year", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "citycode"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer", "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_forbidden_crit_air_in_year of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.crit_air.CritAirCategory"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_zfe_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zfe_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.get_zfe_definition", "name": "get_zfe_definition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_zfe_definition of ZFEImpactComputer", "ret_type": {".class": "UnionType", "items": ["mobility.builders.zfe_data.ZFEDefinition", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.get_zfe_definition", "name": "get_zfe_definition", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zfe_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_zfe_definition of ZFEImpactComputer", "ret_type": {".class": "UnionType", "items": ["mobility.builders.zfe_data.ZFEDefinition", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_from_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["repository"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.init_from_repository", "name": "init_from_repository", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["repository"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_from_repository of ZFEImpactComputer", "ret_type": "mobility.workers.zfe_impact_computer.ZFEImpactComputer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.init_from_repository", "name": "init_from_repository", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["repository"], "arg_types": ["mobility.repositories.abstract_repositories.AbstractTerritoriesRepository"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_from_repository of ZFEImpactComputer", "ret_type": "mobility.workers.zfe_impact_computer.ZFEImpactComputer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "make_zfe_by_citycode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.make_zfe_by_citycode", "name": "make_zfe_by_citycode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.zfe_impact_computer.ZFEImpactComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_zfe_by_citycode of ZFEImpactComputer", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.workers.zfe_impact_computer.ZFECityListing"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "territory_db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.territory_db", "name": "territory_db", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.builders.territory_database.TerritoryDatabase", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "yearly_renewal_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.yearly_renewal_rate", "name": "yearly_renewal_rate", "setter_type": null, "type": "builtins.float"}}, "zfe_by_citycode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.zfe_by_citycode", "name": "zfe_by_citycode", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "mobility.workers.zfe_impact_computer.ZFECityListing"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.zfe_impact_computer.ZFEImpactComputer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.workers.zfe_impact_computer.ZFEImpactComputer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZFE_DEFINITIONS": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.zfe_data.ZFE_DEFINITIONS", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.zfe_impact_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}}, "path": "mobility\\workers\\zfe_impact_computer.py"}
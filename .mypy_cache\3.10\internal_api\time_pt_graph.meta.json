{"data_mtime": 1751444474, "dep_lines": [6, 7, 10, 11, 12, 14, 15, 1, 2, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.fastest_choice_travel_time_api", "api_abstraction.api.travel_time_api", "api_abstraction.google.api", "api_abstraction.navitia.api", "mobility.ir.transport", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "random", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.google", "api_abstraction.navitia", "configparser", "enum", "functools", "mobility.ir", "typing", "typing_extensions"], "hash": "656fa47cb5e450339bfe8c0f0c1ff1c9565a45ed", "id": "internal_api.time_pt_graph", "ignore_all": false, "interface_hash": "edbe0127d715db16b65a4d6389ccf92695cea026", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\time_pt_graph.py", "plugin_data": null, "size": 2533, "suppressed": ["networkx"], "version_id": "1.16.1"}
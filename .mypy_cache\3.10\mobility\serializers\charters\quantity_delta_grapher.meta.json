{"data_mtime": 1751444472, "dep_lines": [9, 8, 21, 5, 6, 7, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.serializers.charters.svg_interface", "mobility.serializers.chart_writer", "mobility.workers.color_picker", "mobility.constants", "mobility.funky", "mobility.quantity", "os", "typing", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.workers", "ntpath", "typing_extensions"], "hash": "0fa81e98293ed6486df187dd5c91c0f7120bf04d", "id": "mobility.serializers.charters.quantity_delta_grapher", "ignore_all": false, "interface_hash": "d14794a3e4b4629460e07c02d46b67fce123d779", "mtime": 1726151661, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\charters\\quantity_delta_grapher.py", "plugin_data": null, "size": 7886, "suppressed": [], "version_id": "1.16.1"}
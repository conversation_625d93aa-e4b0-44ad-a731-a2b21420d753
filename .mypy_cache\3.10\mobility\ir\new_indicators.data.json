{".class": "MypyFile", "_fullname": "mobility.ir.new_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommuterProfile": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.CommuterProfile", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IndividualCosts": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.IndividualCosts", "kind": "Gdef"}, "MultiScenarioIndicator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator", "name": "MultiScenarioIndicator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 27, "name": "indicators", "type": {".class": "Instance", "args": ["builtins.str", "mobility.ir.new_indicators.ScenarioIndicator"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.new_indicators", "mro": ["mobility.ir.new_indicators.MultiScenarioIndicator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "indicators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "indicators"], "arg_types": ["mobility.ir.new_indicators.MultiScenarioIndicator", {".class": "Instance", "args": ["builtins.str", "mobility.ir.new_indicators.ScenarioIndicator"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MultiScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "indicators"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["indicators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["indicators"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "mobility.ir.new_indicators.ScenarioIndicator"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultiScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["indicators"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "mobility.ir.new_indicators.ScenarioIndicator"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultiScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "indicators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.indicators", "name": "indicators", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "mobility.ir.new_indicators.ScenarioIndicator"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.new_indicators.MultiScenarioIndicator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.new_indicators.MultiScenarioIndicator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedScenarioIndicator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator", "name": "NamedScenarioIndicator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 12, "name": "nickname", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.new_indicators", "mro": ["mobility.ir.new_indicators.NamedScenarioIndicator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nickname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nickname"], "arg_types": ["mobility.ir.new_indicators.NamedScenarioIndicator", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nickname"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["nickname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["nickname"], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NamedScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["nickname"], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NamedScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "nickname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.nickname", "name": "nickname", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.new_indicators.NamedScenarioIndicator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.new_indicators.NamedScenarioIndicator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "ScenarioIndicator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.ir.new_indicators.NamedScenarioIndicator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.new_indicators.ScenarioIndicator", "name": "ScenarioIndicator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.ScenarioIndicator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 12, "name": "nickname", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 17, "name": "average_travel_time", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "carbon_emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "costs", "type": "mobility.ir.cost.IndividualCosts"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "employees_count_per_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 21, "name": "employees_count_per_profile", "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 22, "name": "zfe_impact_calendar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.new_indicators", "mro": ["mobility.ir.new_indicators.ScenarioIndicator", "mobility.ir.new_indicators.NamedScenarioIndicator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "average_travel_time", "carbon_emission", "costs", "employees_count_per_mode", "employees_count_per_profile", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "average_travel_time", "carbon_emission", "costs", "employees_count_per_mode", "employees_count_per_profile", "zfe_impact_calendar"], "arg_types": ["mobility.ir.new_indicators.ScenarioIndicator", "builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nickname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbon_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "costs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_profile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zfe_impact_calendar"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "average_travel_time", "carbon_emission", "costs", "employees_count_per_mode", "employees_count_per_profile", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "average_travel_time", "carbon_emission", "costs", "employees_count_per_mode", "employees_count_per_profile", "zfe_impact_calendar"], "arg_types": ["builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["nickname", "average_travel_time", "carbon_emission", "costs", "employees_count_per_mode", "employees_count_per_profile", "zfe_impact_calendar"], "arg_types": ["builtins.str", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.cost.IndividualCosts", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ScenarioIndicator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "average_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.average_travel_time", "name": "average_travel_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "carbon_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.carbon_emission", "name": "carbon_emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "costs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.costs", "name": "costs", "setter_type": null, "type": "mobility.ir.cost.IndividualCosts"}}, "employees_count_per_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.employees_count_per_mode", "name": "employees_count_per_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_count_per_profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.employees_count_per_profile", "name": "employees_count_per_profile", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "zfe_impact_calendar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.new_indicators.ScenarioIndicator.zfe_impact_calendar", "name": "zfe_impact_calendar", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.new_indicators.ScenarioIndicator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.new_indicators.ScenarioIndicator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "ZFEImpactCalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.new_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}}, "path": "mobility\\ir\\new_indicators.py"}
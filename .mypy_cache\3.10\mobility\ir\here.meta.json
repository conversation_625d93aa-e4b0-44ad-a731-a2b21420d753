{"data_mtime": 1751444403, "dep_lines": [7, 8, 6, 1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mobility.ir.geo_study", "mobility.ir.transport", "mobility.constants", "dataclasses", "datetime", "enum", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "c597da97ec2c36ca5bcd6559987fb5ce7e0c1bb3", "id": "mobility.ir.here", "ignore_all": false, "interface_hash": "185929cd9cc13834f277c2f45f8ce799b666f78f", "mtime": 1750243974, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\here.py", "plugin_data": null, "size": 2854, "suppressed": [], "version_id": "1.16.1"}
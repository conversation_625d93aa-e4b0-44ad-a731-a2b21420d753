{"data_mtime": 1751444473, "dep_lines": [15, 6, 14, 26, 27, 5, 13, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.serializers.charters.svg_interface", "mobility.ir.cost", "mobility.serializers.chart_writer", "mobility.workers.color_picker", "mobility.workers.multi_affine_solver", "mobility.constants", "mobility.quantity", "os", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.funky", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.workers", "ntpath", "types", "typing_extensions"], "hash": "c2f5d8829476ce5a520bfbfaeba853c3cec3dcf4", "id": "mobility.serializers.charters.cost_grapher", "ignore_all": false, "interface_hash": "49aaa0c4dc1af55ed89bb45921740bae655deb21", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\charters\\cost_grapher.py", "plugin_data": null, "size": 22565, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444432, "dep_lines": [6, 7, 8, 9, 17, 18, 19, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.builders.exceptions", "mobility.builders.territory_builder", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.site", "mobility.ir.territory", "mobility.ir.transport", "json", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "abc", "contextlib", "enum", "json.encoder", "mobility.builders", "mobility.ir", "mobility.ir.bounding_box", "re", "typing", "typing_extensions"], "hash": "279111e8c49fc4fea044246b1f9040a5d90754ba", "id": "mobility.tests.builders.test_territory_builder", "ignore_all": false, "interface_hash": "8bc68665e6fbf80268ddaa5f8874ae4be39e863d", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\builders\\test_territory_builder.py", "plugin_data": null, "size": 40036, "suppressed": ["g<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}
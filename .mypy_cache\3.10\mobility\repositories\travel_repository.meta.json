{"data_mtime": 1751444475, "dep_lines": [3, 4, 5, 6, 7, 8, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.scenario", "mobility.ir.transport", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "enum", "functools", "mobility.ir", "types", "typing_extensions"], "hash": "55322e68734bfe96ef522c21e4f32599b5a15fac", "id": "mobility.repositories.travel_repository", "ignore_all": false, "interface_hash": "7a8fd5eb82f12ea217b6e1bbea26c1c9b2213264", "mtime": 1722327417, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\travel_repository.py", "plugin_data": null, "size": 4420, "suppressed": [], "version_id": "1.16.1"}
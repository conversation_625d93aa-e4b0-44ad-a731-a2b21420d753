{"data_mtime": 1752154451, "dep_lines": [5, 6, 8, 9, 10, 11, 12, 14, 21, 13, 1, 2, 3, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "mobility.builders.json_builder", "mobility.builders.territory_builder", "mobility.ir.infrastructure", "mobility.ir.study", "mobility.ir.territory", "mobility.workers.territory_computer", "mobility.workers.travel_timer", "mobility.serializers", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "datetime", "mobility", "builtins", "_frozen_importlib", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "configparser", "enum", "mobility.builders", "mobility.funky", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.transport", "mobility.quantity", "mobility.serializers.json_serializer", "mobility.workers", "typing", "typing_extensions"], "hash": "b4dbe41ba8fe91fd5ac560fa729b0fc316bfb0f9", "id": "mobility.study_tweakers.update_infrastructure", "ignore_all": false, "interface_hash": "89a5b67618c100f809d0b827a9b1ed34a482e43d", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\study_tweakers\\update_infrastructure.py", "plugin_data": null, "size": 4009, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1753176776, "dep_lines": [4, 5, 7, 3, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.transport", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "builtins", "_frozen_importlib", "abc", "enum", "mobility.workers", "typing"], "hash": "920458168913777b6fc994e16a676545f24a5b5b", "id": "mobility.ir.new_indicators", "ignore_all": false, "interface_hash": "3bfd777c17c863e53dbbe955982e98c6bf0c03fd", "mtime": 1722327416, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\new_indicators.py", "plugin_data": null, "size": 806, "suppressed": [], "version_id": "1.16.1"}
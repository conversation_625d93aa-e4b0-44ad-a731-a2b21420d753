import os
import subprocess
from typing import List, Optional, Tuple

from mobility import config
from mobility.builders.territory_builder import <PERSON><PERSON><PERSON><PERSON>, TerritoryDataError
from mobility.funky import make_dir_for
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import BoundingBox, TerritoryData


class TerritoryRepository:
    def __init__(
        self, territories_dir: Optional[str] = None, osm_source: Optional[str] = None
    ) -> None:
        if territories_dir is None:
            self.territories_dir = config["mobility"]["territories_dir"]
        else:
            self.territories_dir = territories_dir
        if osm_source is None:
            self.osm_source = config["assembler"]["OSM_SOURCE"]
        else:
            self.osm_source = osm_source

    def get_location(self) -> str:
        return self.territories_dir

    def get_osm_source(self) -> str:
        return self.osm_source

    def get_all_territories_ids(self) -> List[str]:
        territories = []
        for dir_entry in os.scandir(self.territories_dir):
            if dir_entry.is_dir():
                territories.append(dir_entry.name)
        territories.sort()
        return territories

    def get_territory_bounding_box(self, territory_id: str) -> Optional[BoundingBox]:
        bbox_file = os.path.join(self.territories_dir, territory_id, "bounding_box")
        try:
            with open(bbox_file, "r") as f:
                bbox_data = f.read()
                lgt_west, lat_south, lgt_east, lat_north = bbox_data.split(",")
        except Exception:
            return None
        return BoundingBox(
            south_west=GeoCoordinates(
                latitude=float(lat_south), longitude=float(lgt_west)
            ),
            north_east=GeoCoordinates(
                latitude=float(lat_north), longitude=float(lgt_east)
            ),
        )

    def get_territory_data(
        self, territory_id: str
    ) -> Tuple[Optional[TerritoryData], Optional[str]]:
        try:
            territory = TerritoryBuilder(self.territories_dir, territory_id).build()
            error = None
        except TerritoryDataError as e:
            territory = None
            error = e
        return territory, str(error)

    def deploy_territory(self, territory_id: str) -> None:
        bounding_box = self.get_territory_bounding_box(territory_id)
        if bounding_box is None:
            raise TerritoryDataError(
                "No bounding box file for territory {territory_id}"
            )
        territory_source_path = os.path.join(
            self.territories_dir, territory_id, "source.osm"
        )
        subprocess.run(
            [
                "osmconvert",
                self.osm_source,
                f"-b={bounding_box.to_csv()}",
                f"-o={territory_source_path}",
            ],
            check=True,
            capture_output=True,
        )
        extracted_files = {
            "public_transport_routes.osm": "route=bus =light_rail =subway =train =tram =trolleybus =monorail =ferry =aerialway or ( public_transport=station =platform )",
            "bicycle_routes.osm": "highway*=cycleway or cycleway*=track *=opposite_track *=lane *=opposite_lane *=share_busway *=opposite_share_busway *=opposite *=shared_lane or ( highway*=path and bicycle=designated ) or ( amenity=bicycle_rental =bicycle_parking =bicycle_repair_station =charging_station ) or shop=bicycle",
            "car_routes.osm": "highway*=motorway =trunk amenity=car_sharing =car_pooling =car_rental =charging_station =fuel =parking",
            "buildings.osm": "building=yes =house =residential =apartments =detached =semidetached_house",
            "administrative_boundaries.osm": "boundary=administrative and ref:INSEE",
        }
        for extract_file_name, things_to_keep in extracted_files.items():
            extract_file_path = os.path.join(
                self.territories_dir, territory_id, extract_file_name
            )
            sub_p = [
                "osmfilter",
                territory_source_path,
                f"--keep={things_to_keep}",
                f"-o={extract_file_path}",
            ]
            subprocess.run(sub_p, check=True, capture_output=True)

    def create_territory(self, territory_id: str, bounding_box: BoundingBox) -> None:
        bbox_file = os.path.join(self.territories_dir, territory_id, "bounding_box")
        make_dir_for(bbox_file)
        with open(bbox_file, "w+") as f:
            f.write(bounding_box.to_csv())

{"data_mtime": 1752154446, "dep_lines": [6, 9, 12, 23, 11, 24, 5, 10, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.scenario_indicators", "mobility.serializers.charters.svg_interface", "mobility.serializers.charters.svg_library", "mobility.serializers.chart_writer", "mobility.workers.color_picker", "mobility.constants", "mobility.quantity", "os", "typing", "mobility", "builtins", "_frozen_importlib", "abc", "configparser", "enum", "mobility.funky", "mobility.ir", "mobility.ir.cost", "mobility.ir.indicators", "mobility.ir.transport", "mobility.workers", "mobility.workers.zfe_impact_computer"], "hash": "41dc6fba9c0524538fd79fa87bfa1e0c4c2cb9db", "id": "mobility.serializers.charters.quantity_delta_card_grapher", "ignore_all": false, "interface_hash": "949bd957cc341445cfc5efc5f0659e2320ff9947", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\charters\\quantity_delta_card_grapher.py", "plugin_data": null, "size": 9042, "suppressed": [], "version_id": "1.16.1"}
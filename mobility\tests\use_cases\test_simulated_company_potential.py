from typing import Any, Callable
from unittest import mock

import pytest

from mobility.builders.territory_database import TerritoryDatabase
from mobility.funky import ImmutableDict
from mobility.ir.company_potential import CompanyPotential
from mobility.ir.crit_air import CritAirCategory
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.indicators.indicators import Indicators
from mobility.ir.study_types import Scenarios
from mobility.ir.transport import TransportMode
from mobility.quantity import car_parking_spot, euros, gramEC, minutes, trip, yearly
from mobility.repositories.abstract_repositories import (
    AbstractGeocoderTerritoriesTravelTimerApiRepository,
)
from mobility.use_cases.simulated_company_potential import (
    CompanyCarbonOrientedPotentialCacheGetter,
    CompanyCarbonOrientedPotentialSimulator,
    InvalidKeyError,
)


class TestCompanyCarbonOrientedPotentialSimulator:
    def test_should_compute_carbon_oriented_carbon_potential_on_simulated_company(
        self,
        address: Any,
        trivial_geocoder: Any,
        trivial_travel_timer: Any,
        fake_territory: Any,
        company_potential: Callable,
        trip: Any,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        building = address(
            full="200 Route de Plan de Baix 26400 Plan-de-Baix",
            normalized="200 Route de Plan de Baix 26400 Plan-de-Baix",
            city="Plan-de-Baix",
            citycode="26240",
            postcode="26400",
            coordinates=GeoCoordinates(44.812267, 5.167915),
        )
        buildings = [building, building, building]
        repo.get_cities_buildings.return_value = buildings
        repo.get_geocoder.return_value = trivial_geocoder
        repo.get_travel_timer.return_value = trivial_travel_timer
        territories_dir, _ = fake_territory()
        repo.get_territories_dir.return_value = territories_dir
        repo.is_valid_key.return_value = True
        repo.get_cached_company_potential.return_value = {}
        db = TerritoryDatabase(territories_dir)
        db.add_buildings_to_database(buildings)
        db.add_trips_to_database(
            [
                trip(
                    city_code_origin="26240",
                    city_code_destination="26240",
                    nb_workers=1000,
                    transport_mode="total",
                )
            ]
        )
        db.add_trips_to_database(
            [
                trip(
                    city_code_origin="26240",
                    city_code_destination="26240",
                    nb_workers=1000,
                    transport_mode="CAR",
                )
            ]
        )
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        computed_potential = use_case("26240", 12, "fake_key")

        assert isinstance(computed_potential, CompanyPotential)
        assert computed_potential.nb_employees == 12

    def test_should_return_cached_data_if_cache_is_available(
        self,
        company_potential: Callable,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.is_valid_key.return_value = True
        repo.get_cached_company_potential.return_value = {
            27: company_potential(nb_employees=27),
        }
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        computed_potential = use_case("26240", 27, "fake_key")

        assert isinstance(computed_potential, CompanyPotential)
        assert computed_potential.nb_employees == 27

    def test_should_fail_if_given_key_is_invalid(
        self,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.is_valid_key.return_value = False
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        with pytest.raises(InvalidKeyError):
            use_case("26240", 12, "fake_key")


class TestSimulateCompany:
    def test_should_simulate_company_from_citycode_and_nb_employees(
        self,
        address: Any,
        consolidated_study_factory: Any,
        trivial_geocoder: Any,
        fake_territory: Any,
        trip: Any,
        trivial_travel_timer: Any,
        study_data: Any,
        consolidated_scenario_factory: Any,
        scenario_data_factory: Any,
        consolidated_commute_factory: Any,
        modal_scenario_factory: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        building = address(
            full="200 Route de Plan de Baix 26400 Plan-de-Baix",
            normalized="200 Route de Plan de Baix 26400 Plan-de-Baix",
            city="Plan-de-Baix",
            citycode="26240",
            postcode="26400",
            coordinates=GeoCoordinates(44.812267, 5.167915),
        )
        buildings = [building, building, building]
        repo.get_cities_buildings.return_value = buildings
        repo.get_geocoder.return_value = trivial_geocoder
        repo.get_travel_timer.return_value = trivial_travel_timer
        all_non_classes = """
        {
        "26240": {
          "Crit'Air 1": {"total": 0},
          "Crit'Air 2": {"total": 0},
          "Crit'Air 3": {"total": 0},
          "Crit'Air 4": {"total": 0},
          "Crit'Air 5": {"total": 0},
          "Electriques": {"total": 0},
          "Non classés": {"total": 1000},
          "total": {"total": 1000}}
        }
        """
        territories_dir, _ = fake_territory(crit_air_content=all_non_classes)
        repo.get_territories_dir.return_value = territories_dir
        db = TerritoryDatabase(territories_dir)
        db.add_buildings_to_database(buildings)
        db.add_trips_to_database(
            [
                trip(
                    city_code_origin="26240",
                    city_code_destination="26240",
                    nb_workers=1000,
                    transport_mode="total",
                )
            ]
        )
        db.add_trips_to_database(
            [
                trip(
                    city_code_origin="26240",
                    city_code_destination="26240",
                    nb_workers=1000,
                    transport_mode="CAR",
                )
            ]
        )
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        computed_consolidated_study = use_case.simulate_company("26240", 10)

        assert computed_consolidated_study == consolidated_study_factory(
            data=study_data(company="", mission_id=""),
            scenarios=Scenarios(
                frozenset(
                    {
                        consolidated_scenario_factory(
                            id=1,
                            nickname="Actuel",
                            is_present=True,
                            commutes=frozenset(
                                {
                                    consolidated_commute_factory(
                                        employee=geo_employee_factory(
                                            id=i,
                                            nickname=f"S{i:02}",
                                            address=building.full,
                                            address_details=building,
                                            transport_mode=None,
                                            remote=True,
                                            coordinates=building.coordinates,
                                        ),
                                        site=geo_site_factory(
                                            id=1,
                                            nickname="Simulated Site",
                                            address=building.full,
                                            address_details=building,
                                            coordinates=building.coordinates,
                                        ),
                                        data=modal_commute_data_factory(
                                            best_mode=TransportMode.CAR,
                                            duration=ImmutableDict(
                                                {
                                                    TransportMode.CAR: 23,
                                                    TransportMode.WALK: 152,
                                                    TransportMode.BICYCLE: 32,
                                                    TransportMode.PUBLIC_TRANSPORT: 34,
                                                    TransportMode.CARPOOLING: 23,
                                                    TransportMode.ELECTRIC_BICYCLE: 32,
                                                    TransportMode.ELECTRIC_CAR: 23,
                                                    TransportMode.MOTORCYCLE: 23,
                                                    TransportMode.AIRPLANE: 7200,
                                                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                                    TransportMode.FAST_BICYCLE: 91,
                                                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                                                }
                                            ),
                                            emission=ImmutableDict(
                                                {
                                                    TransportMode.CAR: 23,
                                                    TransportMode.WALK: 152,
                                                    TransportMode.BICYCLE: 32,
                                                    TransportMode.PUBLIC_TRANSPORT: 34,
                                                    TransportMode.CARPOOLING: 23,
                                                    TransportMode.ELECTRIC_BICYCLE: 32,
                                                    TransportMode.ELECTRIC_CAR: 23,
                                                    TransportMode.MOTORCYCLE: 23,
                                                    TransportMode.AIRPLANE: 115000,
                                                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                                    TransportMode.FAST_BICYCLE: 91,
                                                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                                                }
                                            ),
                                            distance=ImmutableDict(
                                                {
                                                    TransportMode.CAR: 23,
                                                    TransportMode.WALK: 152,
                                                    TransportMode.BICYCLE: 32,
                                                    TransportMode.PUBLIC_TRANSPORT: 34,
                                                    TransportMode.CARPOOLING: 23,
                                                    TransportMode.ELECTRIC_BICYCLE: 32,
                                                    TransportMode.ELECTRIC_CAR: 23,
                                                    TransportMode.MOTORCYCLE: 23,
                                                    TransportMode.AIRPLANE: 500000,
                                                    TransportMode.ELECTRIC_MOTORCYCLE: 90,
                                                    TransportMode.FAST_BICYCLE: 91,
                                                    TransportMode.CAR_PUBLIC_TRANSPORT: 92,
                                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 93,
                                                }
                                            ),
                                            alternative_arrival_time=ImmutableDict({}),
                                        ),
                                    )
                                    for i in range(0, 10)
                                }
                            ),
                        )
                    }
                )
            ),
            poi_scenarios=Scenarios.empty(),
            time_failed=Scenarios(
                frozenset(
                    {
                        modal_scenario_factory(
                            data=scenario_data_factory(
                                id=1, nickname="Actuel", is_present=True
                            ),
                            commutes=frozenset(),
                        )
                    }
                )
            ),
        )


class TestTranslateIndicatorsToCompanyPotential:
    def test_should_compute_company_potential(
        self,
        fake_territory: Any,
        geo_employee_factory: Any,
        address: Any,
        zfe_calendar_event: Any,
        zfe_definition: Any,
        consolidated_study_factory: Any,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        company_potential: Any,
        mobility_potential: Any,
        potential_details_by_plan: Any,
        potential_details_by_modal_shift: Any,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        territories_dir, _ = fake_territory()
        repo.get_territories_dir.return_value = territories_dir
        repo.get_zfe_calendar.return_value = {2022: [CritAirCategory.CRITAIR_5]}
        zfe_calendar = {
            2024: zfe_calendar_event(
                forbid_crit_air=[CritAirCategory.CRITAIR_4, CritAirCategory.CRITAIR_5]
            )
        }
        mock_zfe_definitions = {
            "My ZFE": zfe_definition(citycodes=["26240"], calendar=zfe_calendar)
        }
        with mock.patch.dict(
            "mobility.repositories.simulated_company.ZFE_DEFINITIONS",
            mock_zfe_definitions,
        ):
            use_case = CompanyCarbonOrientedPotentialSimulator(repo)
            consolidated_study = consolidated_study_factory(
                scenarios=Scenarios(
                    frozenset(
                        {
                            consolidated_scenario_factory(
                                commutes=frozenset(
                                    {
                                        consolidated_commute_factory(
                                            employee=geo_employee_factory(
                                                crit_air=CritAirCategory.CRITAIR_5,
                                                address_details=address(
                                                    citycode="26240"
                                                ),
                                            ),
                                            data=modal_commute_data_factory(
                                                best_mode=TransportMode.CAR,
                                                duration=ImmutableDict(
                                                    {
                                                        TransportMode.CAR: 45 * 60,
                                                        TransportMode.WALK: 2 * 60 * 60,
                                                        TransportMode.BICYCLE: 30 * 60,
                                                        TransportMode.PUBLIC_TRANSPORT: 35
                                                        * 60,
                                                    }
                                                ),
                                                emission=ImmutableDict(
                                                    {
                                                        TransportMode.CAR: 5000,
                                                        TransportMode.WALK: 0,
                                                        TransportMode.BICYCLE: 0,
                                                        TransportMode.PUBLIC_TRANSPORT: 2000,
                                                    }
                                                ),
                                                distance=ImmutableDict(
                                                    {
                                                        TransportMode.CAR: 50000,
                                                        TransportMode.WALK: 45000,
                                                        TransportMode.BICYCLE: 46000,
                                                        TransportMode.PUBLIC_TRANSPORT: 55000,
                                                    }
                                                ),
                                            ),
                                        )
                                    }
                                ),
                            )
                        }
                    )
                ),
            )
            indicators = Indicators(consolidated_study)
            indicators.present_scenario.active_zfes = ["ZFE Grand Paris"]

            computed_company_potential = (
                use_case.compute_ideal_scenario_company_potential(indicators)
            )

        current_cost = indicators.present_scenario.costs.total / (euros / yearly)
        pt_cost = indicators.present_scenario.get_public_transport_shift_scenarios()[
            1
        ].costs.total / (euros / yearly)
        assert computed_company_potential == company_potential(
            nb_employees=1,
            zfe_calendar={2022: [CritAirCategory.CRITAIR_5]},
            emission_potential=mobility_potential(
                unit=gramEC / trip,
                current_value=5000,
                future_value=2000,
                detail_by_plan=potential_details_by_plan(
                    unit=gramEC / trip, public_transport_shift_delta=-3000
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    unit=gramEC / trip, car_to_public_transport_shift_delta=-3000
                ),
            ),
            cost_potential=mobility_potential(
                unit=euros / yearly,
                current_value=current_cost,
                future_value=pt_cost,
                detail_by_plan=potential_details_by_plan(
                    unit=euros / yearly,
                    public_transport_shift_delta=pt_cost - current_cost,
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    unit=euros / yearly,
                    car_to_public_transport_shift_delta=pt_cost - current_cost,
                ),
            ),
            zfe_impact_potential=mobility_potential(
                current_value=1.0,
                future_value=0.0,
                detail_by_plan=potential_details_by_plan(
                    public_transport_shift_delta=-1
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    car_to_public_transport_shift_delta=-1
                ),
            ),
            travel_time_potential=mobility_potential(
                unit=minutes,
                current_value=45.0,
                future_value=35.0,
                detail_by_plan=potential_details_by_plan(
                    unit=minutes, public_transport_shift_delta=-10
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    unit=minutes, car_to_public_transport_shift_delta=-10
                ),
            ),
            parking_spots_potential=mobility_potential(
                unit=car_parking_spot,
                current_value=1,
                future_value=0,
                detail_by_plan=potential_details_by_plan(
                    unit=car_parking_spot, public_transport_shift_delta=-1
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    unit=car_parking_spot, car_to_public_transport_shift_delta=-1
                ),
            ),
            nb_employees_potential=mobility_potential(
                current_value=1,
                future_value=1,
                detail_by_plan=potential_details_by_plan(
                    public_transport_shift_delta=1
                ),
                detail_by_modal_shift=potential_details_by_modal_shift(
                    car_to_public_transport_shift_delta=1
                ),
            ),
        )

    def test_should_compute_company_potential_if_no_active_zfe(
        self,
        consolidated_study: Any,
        company_potential: Any,
        mobility_potential: Any,
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_zfe_calendar.return_value = {2022: [CritAirCategory.CRITAIR_5]}
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)
        indicators = Indicators(consolidated_study)
        indicators.present_scenario.active_zfes = []

        computed_company_potential = use_case.compute_ideal_scenario_company_potential(
            indicators
        )

        assert computed_company_potential.zfe_calendar == {}


class TestFindTheMiddleBuildingInCity:
    def test_should_find_the_middle_building_among_one_single_building(
        self, address: Any
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_cities_buildings.return_value = [address()]
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        computed_address = use_case.find_the_middle_building_in_city("52164")

        assert computed_address == address()

    def test_should_find_the_middle_building_among_several_buildings(
        self, address_factory: Any, geo_coordinates_factory: Any
    ) -> None:
        middle_coordinates = geo_coordinates_factory(latitude=0.1, longitude=0.1)
        middle_address = address_factory(coordinates=middle_coordinates)
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_cities_buildings.return_value = [
            address_factory(
                coordinates=geo_coordinates_factory(latitude=0.0, longitude=0.0)
            ),
            middle_address,
            address_factory(
                coordinates=geo_coordinates_factory(latitude=1.0, longitude=1.0)
            ),
        ]
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        computed_address = use_case.find_the_middle_building_in_city("52164")

        assert computed_address == middle_address

    def test_should_fail_if_no_building_can_be_found(self, address: Any) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_cities_buildings.return_value = []
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)

        with pytest.raises(ValueError):
            use_case.find_the_middle_building_in_city("52164")


class TestMakeGeoSiteFromAddress:
    def test_should_return_geo_site_with_correct_address(
        self, address: Any, geo_site_factory: Any
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        use_case = CompanyCarbonOrientedPotentialSimulator(repo)
        site_address = address()

        computed_site = use_case.make_geo_site_from_address(site_address)

        assert computed_site == geo_site_factory(
            nickname="Simulated Site",
            address_details=site_address,
            address=site_address.full,
            coordinates=site_address.coordinates,
        )


class TestCompanyCarbonOrientedPotentialCacheGetter:
    def test_should_return_list_of_nb_employees_cached(
        self, company_potential: Any
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_cached_company_potential.return_value = {
            100: company_potential(nb_employees=100),
            500: company_potential(nb_employees=500),
            1000: company_potential(nb_employees=1000),
        }
        repo.is_valid_key.return_value = True
        use_case = CompanyCarbonOrientedPotentialCacheGetter(repo)

        cached_nb_employees = use_case("23435", "the_key")

        assert cached_nb_employees == [100, 500, 1000]

    def test_should_not_return_list_of_nb_employees_cached_if_key_is_invalid(
        self, company_potential: Any
    ) -> None:
        repo = mock.Mock(AbstractGeocoderTerritoriesTravelTimerApiRepository)
        repo.get_cached_company_potential.return_value = {
            100: company_potential(nb_employees=100),
            500: company_potential(nb_employees=500),
            1000: company_potential(nb_employees=1000),
        }
        repo.is_valid_key.return_value = False
        use_case = CompanyCarbonOrientedPotentialCacheGetter(repo)

        with pytest.raises(InvalidKeyError):
            use_case("23435", "the_key")

{"data_mtime": 1752056505, "dep_lines": [14, 7, 8, 9, 13, 15, 16, 18, 19, 11, 12, 17, 1, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 5, 4], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["mobility.ir.indicators.clustering_indicators", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.geopy.ban_france", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.workers.minmax_kmeans", "mobility.constants", "mobility.funky", "mobility.quantity", "typing", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "configparser", "enum", "functools", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.workers", "typing_extensions"], "hash": "f9dc312835baf1bbbffd6df983890ae9ac92816a", "id": "mobility.converters.indicators.clustering", "ignore_all": false, "interface_hash": "50b7daf74c9c56fec2702156b31dd3ae59c46926", "mtime": 1752052790, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\clustering.py", "plugin_data": null, "size": 6484, "suppressed": ["geopy.distance", "sklearn.cluster", "pandas"], "version_id": "1.16.1"}
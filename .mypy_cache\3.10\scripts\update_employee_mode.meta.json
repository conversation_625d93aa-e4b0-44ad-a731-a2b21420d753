{"data_mtime": 1752154427, "dep_lines": [9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.ir.employee", "mobility.ir.transport", "mobility.serializers.json_serializer", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "json", "logging", "datetime", "typing", "mobility", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "enum", "io", "json.decoder", "mobility.builders", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.quantity", "mobility.serializers", "mobility.workers", "os", "types", "typing_extensions"], "hash": "acdbfeb16d5acb47f1cd03c8753a628c684959d4", "id": "scripts.update_employee_mode", "ignore_all": false, "interface_hash": "82ee7a2eb38deafd0c792d0719357fb4c9fdf664", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\update_employee_mode.py", "plugin_data": null, "size": 3714, "suppressed": [], "version_id": "1.16.1"}
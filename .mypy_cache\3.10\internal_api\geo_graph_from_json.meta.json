{"data_mtime": 1751444419, "dep_lines": [12, 10, 11, 13, 1, 2, 3, 4, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 6], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["mobility.ir.transport", "internal_api.geo_graph", "internal_api.json_to_graph", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "json", "os", "pickle", "numpy", "builtins", "_frozen_importlib", "_io", "_pickle", "_typeshed", "abc", "enum", "genericpath", "io", "json.decoder", "mobility", "mobility.ir", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy.core", "numpy.core.multiarray", "typing", "typing_extensions"], "hash": "7c72af71435891bbe60ce921c7ae335cffdd16b0", "id": "internal_api.geo_graph_from_json", "ignore_all": false, "interface_hash": "5670dad33568c373417f58e459e218844b770dbd", "mtime": 1723449311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\geo_graph_from_json.py", "plugin_data": null, "size": 6974, "suppressed": ["scipy.spatial", "networkx"], "version_id": "1.16.1"}
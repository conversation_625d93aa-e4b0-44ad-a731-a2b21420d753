{".class": "MypyFile", "_fullname": "mobility.workers.territory_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmenityType": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.AmenityType", "kind": "Gdef"}, "BicycleAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleAmenity", "kind": "Gdef"}, "BicycleLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleLine", "kind": "Gdef"}, "CarAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarAmenity", "kind": "Gdef"}, "CarWay": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarWay", "kind": "Gdef"}, "Constraints": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_constraints.Constraints", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "GeoStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.GeoStudy", "kind": "Gdef"}, "Infrastructure": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.Infrastructure", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LineType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.LineType", "name": "LineType", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleLine", "mobility.ir.infrastructure.CarWay"], "variance": 0}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LocalisedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.LocalisedStudy", "kind": "Gdef"}, "PublicTransportLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportLine", "kind": "Gdef"}, "PublicTransportStop": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportStop", "kind": "Gdef"}, "Scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenario", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "SpecificAmenityType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "name": "SpecificAmenityType", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}}, "StudyData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.StudyData", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TerritoryData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.TerritoryData", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.territory_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_empty_infrastructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.add_empty_infrastructure", "name": "add_empty_infrastructure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.GeoStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_empty_infrastructure", "ret_type": "mobility.ir.study.LocalisedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_transport_infrastructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["study", "territory_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.add_transport_infrastructure", "name": "add_transport_infrastructure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["study", "territory_data"], "arg_types": ["mobility.ir.study.GeoStudy", "mobility.ir.territory.TerritoryData"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_transport_infrastructure", "ret_type": "mobility.ir.study.LocalisedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_amenity_scenarios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sites", "stops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.compute_amenity_scenarios", "name": "compute_amenity_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sites", "stops"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.compute_amenity_scenarios", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_amenity_scenarios", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.site.GeoSite", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.compute_amenity_scenarios", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.compute_amenity_scenarios", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}]}}}, "determine_study_territory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.determine_study_territory", "name": "determine_study_territory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.GeoStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "determine_study_territory", "ret_type": "mobility.ir.territory.Territory", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.workers.territory_computer.distance", "name": "distance", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.workers.territory_computer.distance", "source_any": null, "type_of_any": 3}}}, "extract_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.extract_coordinates", "name": "extract_coordinates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scenario"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract_coordinates", "ret_type": "mobility.ir.geo_study.GeoCoordinates", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_closest_city_center": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["current_site"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.find_closest_city_center", "name": "find_closest_city_center", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["current_site"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_closest_city_center", "ret_type": "mobility.ir.territory.Territory", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bicycle_infrastructures_around_sites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.get_bicycle_infrastructures_around_sites", "name": "get_bicycle_infrastructures_around_sites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "arg_types": ["mobility.ir.territory.TerritoryData", {".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_bicycle_infrastructures_around_sites", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_car_amenities_by_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["territory", "types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.get_car_amenities_by_type", "name": "get_car_amenities_by_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["territory", "types"], "arg_types": ["mobility.ir.territory.TerritoryData", {".class": "Instance", "args": ["mobility.ir.infrastructure.AmenityType"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_car_amenities_by_type", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_car_infrastructures_around_sites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.get_car_infrastructures_around_sites", "name": "get_car_infrastructures_around_sites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "arg_types": ["mobility.ir.territory.TerritoryData", {".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_car_infrastructures_around_sites", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_site_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.get_current_site_coordinates", "name": "get_current_site_coordinates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.GeoStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_current_site_coordinates", "ret_type": "mobility.ir.geo_study.GeoCoordinates", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public_transport_infrastructures_around_sites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.get_public_transport_infrastructures_around_sites", "name": "get_public_transport_infrastructures_around_sites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["territory", "sites"], "arg_types": ["mobility.ir.territory.TerritoryData", {".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_public_transport_infrastructures_around_sites", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_amenities_around_sites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["amenities", "sites"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.select_amenities_around_sites", "name": "select_amenities_around_sites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["amenities", "sites"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.select_amenities_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "select_amenities_around_sites", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.select_amenities_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.SpecificAmenityType", "id": -1, "name": "SpecificAmenityType", "namespace": "mobility.workers.territory_computer.select_amenities_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleAmenity", "mobility.ir.infrastructure.PublicTransportStop", "mobility.ir.infrastructure.CarAmenity"], "variance": 0}]}}}, "select_lines_around_sites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lines", "sites"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.select_lines_around_sites", "name": "select_lines_around_sites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["lines", "sites"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.LineType", "id": -1, "name": "LineType", "namespace": "mobility.workers.territory_computer.select_lines_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleLine", "mobility.ir.infrastructure.CarWay"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["mobility.ir.site.GeoSite"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "select_lines_around_sites", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.LineType", "id": -1, "name": "LineType", "namespace": "mobility.workers.territory_computer.select_lines_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleLine", "mobility.ir.infrastructure.CarWay"], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.territory_computer.LineType", "id": -1, "name": "LineType", "namespace": "mobility.workers.territory_computer.select_lines_around_sites", "upper_bound": "builtins.object", "values": ["mobility.ir.infrastructure.BicycleLine", "mobility.ir.infrastructure.CarWay"], "variance": 0}]}}}, "select_lines_connected_to_stops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lines", "stops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.territory_computer.select_lines_connected_to_stops", "name": "select_lines_connected_to_stops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["lines", "stops"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "select_lines_connected_to_stops", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "mobility\\workers\\territory_computer.py"}
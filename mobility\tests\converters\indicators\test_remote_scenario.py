import pytest

from mobility.converters.indicators.remote_scenario import (
    AverageTravelTimeComputer,
    compute_emission,
    compute_nb_employees_per_mode,
    compute_remote_emissions,
    compute_remote_scenario_indicators,
    compute_remote_workers_travel_time_saved_per_remote_day,
    split_remote_commutes,
)
from mobility.funky import ImmutableDict, UnorderedList
from mobility.ir.transport import CommuterProfile, TransportMode
from mobility.quantity import (
    adimensional,
    daily,
    gramEC,
    hours,
    meters,
    minutes,
    remote_trip,
    remote_trip_saved,
    seconds,
    trip,
    weekly,
)


class TestComputeRemoteScenarioIndicators:
    def test_should_compute_remote_scenario_indicators(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        consolidated_scenario_factory,
        new_remote_scenario_indicators,
        geo_employee_factory,
    ):
        dede = geo_employee_factory(id=0, remote=True)
        toto = geo_employee_factory(id=1, remote=True)
        lulu = geo_employee_factory(id=2, remote=True)
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=dede,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 1001}),
                            distance=ImmutableDict({TransportMode.CAR: 2000}),
                            emission=ImmutableDict({TransportMode.CAR: 3000}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=toto,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 3000}),
                            distance=ImmutableDict({TransportMode.WALK: 1000}),
                            emission=ImmutableDict({TransportMode.WALK: 0}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=lulu,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 2000}),
                            distance=ImmutableDict({TransportMode.CAR: 3000}),
                            emission=ImmutableDict({TransportMode.CAR: 4000}),
                        ),
                    ),
                }
            ),
        )

        indicators = compute_remote_scenario_indicators(
            "", scenario, min_bound=1200 * seconds
        )

        assert indicators == new_remote_scenario_indicators(
            nickname="",
            min_bound=1200 * seconds,
            average_travel_time=1667 * seconds,
            remote_workers_average_travel_time=2500 * seconds,
            carbon_emission=6200 * gramEC / trip,
            remote_workers=UnorderedList([toto, lulu]),
            non_remote_workers=[dede],
            nb_remote_workers=2,
            remote_employees_count_per_mode=ImmutableDict(
                {
                    TransportMode.CAR: 1,
                    TransportMode.WALK: 1,
                    TransportMode.BICYCLE: 0,
                    TransportMode.PUBLIC_TRANSPORT: 0,
                    TransportMode.CARPOOLING: 0,
                    TransportMode.ELECTRIC_BICYCLE: 0,
                    TransportMode.ELECTRIC_CAR: 0,
                    TransportMode.MOTORCYCLE: 0,
                    TransportMode.AIRPLANE: 0,
                    TransportMode.ELECTRIC_MOTORCYCLE: 0,
                    TransportMode.FAST_BICYCLE: 0,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
                }
            ),
            remote_workers_travel_time_saved_per_remote_day=5000
            * seconds
            / (trip / daily),
            remote_workers_travel_time_saved=5000 * seconds / remote_trip_saved,
            remote_workers_annual_distance_saved=4000 * meters / remote_trip_saved,
            remote_drivers_annual_distance_saved=3000 * meters / remote_trip_saved,
            emission=3000 * gramEC / trip + 4000 * gramEC / remote_trip,
            percent_diff_emission=(
                (4000 * gramEC / remote_trip) - (4000 * gramEC / trip)
            )
            / (7000 * gramEC / trip)
            * 100,
            costs=indicators.costs,
            zfe_impact_calendar=indicators.zfe_impact_calendar,
            employees_count_per_mode=ImmutableDict(
                {
                    TransportMode.CAR: 2,
                    TransportMode.WALK: 1,
                    TransportMode.PUBLIC_TRANSPORT: 0,
                    TransportMode.BICYCLE: 0,
                    TransportMode.CARPOOLING: 0,
                    TransportMode.ELECTRIC_BICYCLE: 0,
                    TransportMode.ELECTRIC_CAR: 0,
                    TransportMode.MOTORCYCLE: 0,
                    TransportMode.AIRPLANE: 0,
                    TransportMode.ELECTRIC_MOTORCYCLE: 0,
                    TransportMode.FAST_BICYCLE: 0,
                    TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
                }
            ),
            employees_count_per_profile=ImmutableDict(
                {
                    CommuterProfile.CAR_USER: 1,
                    CommuterProfile.CARPOOLER: 0,
                    CommuterProfile.COWORKER: 0,
                    CommuterProfile.REMOTE_WORKER: 2,
                }
            ),
        )
        assert all(
            employee in indicators.costs.cost_by_employee
            for employee in [dede, toto, lulu]
        )

    def test_should_compute_carbon_emissions(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    emission={TransportMode.CAR: 1250},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    emission={TransportMode.CAR: 900, TransportMode.WALK: 0},
                ),
            ),
        ]
        non_remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                    emission={TransportMode.CAR: 900, TransportMode.BICYCLE: 0},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.PUBLIC_TRANSPORT,
                    emission={
                        TransportMode.CAR: 900,
                        TransportMode.PUBLIC_TRANSPORT: 2000,
                    },
                ),
            ),
        ]

        average_time = compute_remote_emissions(remote_commutes, non_remote_commutes)

        assert average_time == 3000 * gramEC / trip

    def test_should_split_no_commutes_into_empty_lists(self):
        remote, non_remote = split_remote_commutes([], 0 * seconds)

        assert remote == []
        assert non_remote == []

    def test_should_split_commutes_on_bound(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        short_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 900},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration={TransportMode.CAR: 900, TransportMode.WALK: 1000},
                ),
            ),
        ]
        long_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                    duration={TransportMode.CAR: 900, TransportMode.BICYCLE: 1100},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.PUBLIC_TRANSPORT,
                    duration={
                        TransportMode.CAR: 900,
                        TransportMode.PUBLIC_TRANSPORT: 1500,
                    },
                ),
            ),
        ]
        commutes = short_commutes + long_commutes

        remote_commutes, non_remote_commutes = split_remote_commutes(
            commutes, 1000 * seconds
        )

        assert remote_commutes == long_commutes
        assert non_remote_commutes == short_commutes

    def test_should_filter_commutes_with_no_remote_employee(
        self, geo_employee_factory, consolidated_commute_factory
    ):
        remote_employee = geo_employee_factory(remote=True)
        remote_commute = consolidated_commute_factory(employee=remote_employee)
        no_remote_employee = geo_employee_factory(remote=False)
        no_remote_commute = consolidated_commute_factory(employee=no_remote_employee)
        commutes = [remote_commute, no_remote_commute]

        remote_commutes, no_remote_commutes = split_remote_commutes(
            commutes, 0 * seconds
        )

        assert remote_commutes == [remote_commute]
        assert no_remote_commutes == [no_remote_commute]


class TestAverageTravelTimeComputer:
    def test_should_compute_average_travel_time(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 1250},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration={TransportMode.CAR: 900, TransportMode.WALK: 5000},
                ),
            ),
        ]
        non_remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                    duration={TransportMode.CAR: 900, TransportMode.BICYCLE: 3000},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.PUBLIC_TRANSPORT,
                    duration={
                        TransportMode.CAR: 900,
                        TransportMode.PUBLIC_TRANSPORT: 2000,
                    },
                ),
            ),
        ]
        computer = AverageTravelTimeComputer(remote_commutes, non_remote_commutes)

        average_time = computer.compute()

        assert average_time == 2500 * seconds

    def test_should_raise_an_error_when_computing_average_travel_time_with_no_commute(
        self,
    ):
        remote_commutes = []
        non_remote_commutes = []
        computer = AverageTravelTimeComputer(remote_commutes, non_remote_commutes)

        with pytest.raises(ValueError):
            computer.compute()

    def test_should_compute_remoter_average_travel_time(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 2000},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration={TransportMode.CAR: 900, TransportMode.WALK: 5000},
                ),
            ),
        ]
        non_remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                    duration={TransportMode.CAR: 900, TransportMode.BICYCLE: 3000},
                ),
            )
        ]
        computer = AverageTravelTimeComputer(remote_commutes, non_remote_commutes)

        average_time = computer.compute_for_remoter()

        assert average_time == 3500 * seconds

    def test_should_return_none_when_computing_remoter_average_travel_time_with_no_remoter(
        self,
    ):
        computer = AverageTravelTimeComputer([], [])

        average_time = computer.compute_for_remoter()

        assert average_time is None


class TestComputeNbEmployeesPerMode:
    def test_should_return_zero_to_each_mode_when_no_commutes(self):
        remotes = []

        nb_employees_per_mode = compute_nb_employees_per_mode(remotes)

        assert nb_employees_per_mode == {
            TransportMode.CAR: 0,
            TransportMode.PUBLIC_TRANSPORT: 0,
            TransportMode.WALK: 0,
            TransportMode.BICYCLE: 0,
            TransportMode.CARPOOLING: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.AIRPLANE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }

    def test_should_assign_employee_to_its_commute_best_mode(
        self,
        consolidated_commute_factory,
        geo_employee_factory,
        modal_commute_data_factory,
    ):
        remotes = [
            consolidated_commute_factory(
                employee=geo_employee_factory(transport_mode=None),
                data=modal_commute_data_factory(best_mode=TransportMode.CAR),
            ),
            consolidated_commute_factory(
                employee=geo_employee_factory(transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(best_mode=TransportMode.BICYCLE),
            ),
        ]

        nb_employees_per_mode = compute_nb_employees_per_mode(remotes)

        assert nb_employees_per_mode == {
            TransportMode.CAR: 1,
            TransportMode.PUBLIC_TRANSPORT: 0,
            TransportMode.WALK: 0,
            TransportMode.BICYCLE: 1,
            TransportMode.CARPOOLING: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.AIRPLANE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }

    def test_should_deduplicate_employees_when_it_has_several_commutes(
        self,
        consolidated_commute_factory,
        geo_employee_factory,
        modal_commute_data_factory,
    ):
        remotes = [
            consolidated_commute_factory(
                employee=geo_employee_factory(),
                data=modal_commute_data_factory(best_mode=TransportMode.CAR),
            ),
            consolidated_commute_factory(
                employee=geo_employee_factory(),
                data=modal_commute_data_factory(best_mode=TransportMode.CAR),
            ),
        ]

        nb_employees_per_mode = compute_nb_employees_per_mode(remotes)

        assert nb_employees_per_mode == {
            TransportMode.CAR: 1,
            TransportMode.PUBLIC_TRANSPORT: 0,
            TransportMode.WALK: 0,
            TransportMode.BICYCLE: 0,
            TransportMode.CARPOOLING: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.AIRPLANE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }


class TestRemoteWorkerTravelTimeSaved:
    def test_should_return_time_saved(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        remote_commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 60 * 60},
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration={TransportMode.CAR: 900, TransportMode.WALK: 30 * 60},
                ),
            ),
        ]

        travel_time_saved = compute_remote_workers_travel_time_saved_per_remote_day(
            remote_commutes
        )

        assert travel_time_saved == (1 * hours + 30 * minutes) * 2

    def test_should_return_zero_save_hours_per_week_when_no_remote_workers(self):
        remote_commutes = []

        travel_time_saved = compute_remote_workers_travel_time_saved_per_remote_day(
            remote_commutes
        )

        assert travel_time_saved == 0


class TestEmission:
    def test_should_return_emission_teq_per_year(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        geo_employee_factory,
    ):
        no_remote_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR, emission={TransportMode.CAR: 1000}
        )
        no_remote_employee = geo_employee_factory(remote=False)
        no_remote_commute = consolidated_commute_factory(
            employee=no_remote_employee, data=no_remote_data
        )
        remote_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR, emission={TransportMode.CAR: 500}
        )
        remote_employee = geo_employee_factory(remote=True)
        remote_commute = consolidated_commute_factory(
            employee=remote_employee, data=remote_data
        )

        emission, percent_diff_emission = compute_emission(
            [remote_commute], [no_remote_commute]
        )

        assert emission == ((1000 * 10) + (500 * 8)) * gramEC / weekly
        assert (
            percent_diff_emission
            == adimensional * (-500 * 2) / (1000 * 10 + 500 * 10) * 100
        )

    def test_should_return_zero_when_no_remote(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        geo_employee_factory,
    ):
        no_remote_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR, emission={TransportMode.CAR: 1000}
        )
        no_remote_employee = geo_employee_factory(remote=False)
        no_remote_commute = consolidated_commute_factory(
            employee=no_remote_employee, data=no_remote_data
        )

        emission, percent_diff_emission = compute_emission([], [no_remote_commute])

        assert emission == (1000 * 10) * gramEC / weekly
        assert percent_diff_emission == adimensional * 0

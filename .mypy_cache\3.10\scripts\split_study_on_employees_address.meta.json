{"data_mtime": 1752154427, "dep_lines": [9, 10, 11, 1, 2, 3, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.json_builder", "mobility.ir.employee", "mobility.serializers.json_serializer", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "logging", "os", "datetime", "typing", "builtins", "_frozen_importlib", "abc", "enum", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.quantity", "mobility.serializers", "types"], "hash": "8d0c2f86557f898c8265084ab9fc1fec9046f316", "id": "scripts.split_study_on_employees_address", "ignore_all": false, "interface_hash": "f87a43c8e2d9f6240b5ad9215f5e69896b99770a", "mtime": 1746019442, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\split_study_on_employees_address.py", "plugin_data": null, "size": 1815, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "mobility.ir.scenario", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Addressed": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Addressed", "kind": "Gdef"}, "BaseTransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.BaseTransportMode", "kind": "Gdef"}, "ConsolidatedScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ConsolidatedScenario", "kind": "Gdef"}, "DetailedTransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.DetailedTransportMode", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Scenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.scenario.<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.scenario.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "data", "type": "mobility.ir.scenario_data.ScenarioData"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "employees_trips", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.scenario.Trip"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "days_at_location", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "employees_with_benefits", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.scenario", "mro": ["mobility.ir.scenario.<PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.scenario.Scenario.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "data", "employees_trips", "days_at_location", "employees_with_benefits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.scenario.Scenario.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "data", "employees_trips", "days_at_location", "employees_with_benefits"], "arg_types": ["mobility.ir.scenario.<PERSON><PERSON>", "mobility.ir.scenario_data.ScenarioData", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.scenario.Trip"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Scenario", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.scenario.Scenario.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_trips"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "days_at_location"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_with_benefits"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["data", "employees_trips", "days_at_location", "employees_with_benefits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.scenario.<PERSON><PERSON>rio.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["data", "employees_trips", "days_at_location", "employees_with_benefits"], "arg_types": ["mobility.ir.scenario_data.ScenarioData", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.scenario.Trip"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.scenario.<PERSON><PERSON>rio.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["data", "employees_trips", "days_at_location", "employees_with_benefits"], "arg_types": ["mobility.ir.scenario_data.ScenarioData", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.scenario.Trip"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Scenario.data", "name": "data", "setter_type": null, "type": "mobility.ir.scenario_data.ScenarioData"}}, "days_at_location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Scenario.days_at_location", "name": "days_at_location", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_trips": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Scenario.employees_trips", "name": "employees_trips", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "Instance", "args": ["mobility.ir.scenario.Trip"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_with_benefits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Scenario.employees_with_benefits", "name": "employees_with_benefits", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.scenario.Scenario.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.scenario.<PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Trip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.scenario.Trip", "name": "Trip", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.scenario.Trip", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 15, "name": "origin", "type": "mobility.ir.geo_study.Addressed"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 16, "name": "destination", "type": "mobility.ir.geo_study.Addressed"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 17, "name": "steps", "type": {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed"], "extra_attrs": null, "type_ref": "typing.Sequence"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "mode_detail", "type": {".class": "Instance", "args": ["mobility.ir.transport.DetailedTransportMode"], "extra_attrs": null, "type_ref": "typing.Sequence"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "number_of_trips_per_year", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "arrival_time", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.scenario", "mro": ["mobility.ir.scenario.Trip", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.scenario.Trip.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "steps", "mode_detail", "number_of_trips_per_year", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.scenario.Trip.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "steps", "mode_detail", "number_of_trips_per_year", "arrival_time"], "arg_types": ["mobility.ir.scenario.Trip", "mobility.ir.geo_study.Addressed", "mobility.ir.geo_study.Addressed", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["mobility.ir.transport.DetailedTransportMode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Trip", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.scenario.Trip.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "origin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "destination"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode_detail"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "number_of_trips_per_year"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arrival_time"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["origin", "destination", "steps", "mode_detail", "number_of_trips_per_year", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.scenario.Trip.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["origin", "destination", "steps", "mode_detail", "number_of_trips_per_year", "arrival_time"], "arg_types": ["mobility.ir.geo_study.Addressed", "mobility.ir.geo_study.Addressed", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["mobility.ir.transport.DetailedTransportMode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.scenario.Trip.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["origin", "destination", "steps", "mode_detail", "number_of_trips_per_year", "arrival_time"], "arg_types": ["mobility.ir.geo_study.Addressed", "mobility.ir.geo_study.Addressed", {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["mobility.ir.transport.DetailedTransportMode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "arrival_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.arrival_time", "name": "arrival_time", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "destination": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.destination", "name": "destination", "setter_type": null, "type": "mobility.ir.geo_study.Addressed"}}, "iterate_on_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "mobility.ir.scenario.Trip.iterate_on_steps", "name": "iterate_on_steps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.scenario.Trip"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_on_steps of Trip", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["mobility.ir.geo_study.Addressed", "mobility.ir.geo_study.Addressed", "mobility.ir.transport.DetailedTransportMode"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode_detail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.mode_detail", "name": "mode_detail", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.DetailedTransportMode"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "number_of_trips_per_year": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.number_of_trips_per_year", "name": "number_of_trips_per_year", "setter_type": null, "type": "builtins.int"}}, "origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.origin", "name": "origin", "setter_type": null, "type": "mobility.ir.geo_study.Addressed"}}, "steps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.scenario.Trip.steps", "name": "steps", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.geo_study.Addressed"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.scenario.Trip.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.scenario.Trip", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.scenario.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "convert_consolidated_scenario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.scenario.convert_consolidated_scenario", "name": "convert_consolidated_scenario", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scenario"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedScenario"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_consolidated_scenario", "ret_type": "mobility.ir.scenario.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "daily": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.daily", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "trip": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.trip", "kind": "Gdef"}, "yearly": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.yearly", "kind": "Gdef"}}, "path": "mobility\\ir\\scenario.py"}
from typing import Any

import pytest

from mobility.converters.indicators.mode_shift_scenario import (
    compute_mode_shift_scenario_indicators,
    compute_shifted_commutes,
    qualify_no_shift_reason,
    split_shifters_commutes,
)
from mobility.funky import ImmutableDict, UnorderedList
from mobility.ir.transport import CommuterProfile, TransportMode
from mobility.quantity import (
    adimensional,
    car_parking_spot,
    gramEC,
    meters,
    seconds,
    trip,
)


class TestComputeModeShiftScenarios:
    def test_should_fail_to_create_indicators_without_commutes(
        self,
        consolidated_scenario_factory: Any,
        mode_shift_specification: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(commutes=[])

        with pytest.raises(ValueError):
            compute_mode_shift_scenario_indicators(scenario, mode_shift_specification())

    def test_should_compute_mode_shift_indicators_from_scenario(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
        geo_employee_factory,
        mode_shift_specification,
        mode_shift_scenario_indicators,
        no_shift_time_available,
        current_mode_not_compatible,
        transport_mode_mapping,
        individual_costs,
        cost,
    ):
        jojo = geo_employee_factory(id=0)
        dede = geo_employee_factory(id=1)
        toto = geo_employee_factory(id=2)
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=jojo,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 1000}),
                            distance=ImmutableDict({TransportMode.CAR: 2000}),
                            emission=ImmutableDict({TransportMode.CAR: 3000}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=dede,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 3000}),
                            distance=ImmutableDict({TransportMode.WALK: 1000}),
                            emission=ImmutableDict({TransportMode.WALK: 0}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=toto,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.CAR: 1500, TransportMode.BICYCLE: 2000}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.CAR: 3000, TransportMode.BICYCLE: 2000}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.CAR: 4000, TransportMode.BICYCLE: 0}
                            ),
                        ),
                    ),
                }
            ),
        )
        spec = mode_shift_specification(
            mode_to_shift_to=TransportMode.BICYCLE,
            modes_to_shift_from=[TransportMode.CAR],
        )

        indicators = compute_mode_shift_scenario_indicators(scenario, spec)

        assert indicators == mode_shift_scenario_indicators(
            spec=spec,
            average_travel_time=2000 * seconds,
            mean_time_for_shifters=2000 * seconds,
            mean_time_for_shifters_before_shift=1500 * seconds,
            carbon_emission=3000 * gramEC / trip,
            diff_emissions_percent=-4000 * adimensional / 7000 * 100,
            shifters_original_emissions=4000 * gramEC / trip,
            shifters_emissions_after_shift=0 * gramEC / trip,
            annual_distance_for_shifters=2000 * meters / trip,
            annual_distance_by_car_for_car_shifters=3000 * meters / trip,
            delta_time_per_shifter={toto: 500 * seconds},
            no_shift_reason={
                jojo: no_shift_time_available(),
                dede: current_mode_not_compatible(),
            },
            shifter_employees=[toto],
            non_shifter_employees=UnorderedList([dede, jojo]),
            nb_car_parking_saved=1 * car_parking_spot,
            number_of_shifters=1,
            employees_count_per_mode=transport_mode_mapping(
                bicycle=1,
                car=1,
                walk=1,
                public_transport=0,
                carpooling=0,
                motorcycle=0,
                electric_bicycle=0,
                electric_car=0,
                airplane=0,
                electric_motorcycle=0,
                fast_bicycle=0,
                car_public_transport=0,
                bicycle_public_transport=0,
            ),
            shifters_count_per_current_mode=transport_mode_mapping(
                bicycle=0,
                car=1,
                walk=0,
                public_transport=0,
                carpooling=0,
                motorcycle=0,
                electric_bicycle=0,
                electric_car=0,
                airplane=0,
                electric_motorcycle=0,
                fast_bicycle=0,
                car_public_transport=0,
                bicycle_public_transport=0,
            ),
            shifters_travel_time=ImmutableDict({toto: 2000 * seconds}),
            costs=indicators.costs,
            zfe_impact_calendar=indicators.zfe_impact_calendar,
            employees_count_per_profile=ImmutableDict(
                {
                    CommuterProfile.WALKER: 1,
                    CommuterProfile.CYCLIST: 1,
                    CommuterProfile.CAR_USER: 1,
                    CommuterProfile.CARPOOLER: 0,
                    CommuterProfile.COWORKER: 0,
                    CommuterProfile.REMOTE_WORKER: 0,
                }
            ),
        )
        assert toto in indicators.costs.cost_by_employee
        assert jojo in indicators.costs.cost_by_employee
        assert dede in indicators.costs.cost_by_employee

    def test_should_compute_no_car_stuff_if_best_mode_is_not_car(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
        geo_employee_factory,
        mode_shift_specification,
        mode_shift_scenario_indicators,
        no_shift_time_available,
        current_mode_not_compatible,
        transport_mode_mapping,
    ):
        jojo = geo_employee_factory(id=0)
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=jojo,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=transport_mode_mapping(
                                public_transport=1000, bicycle=999
                            ),
                            distance=transport_mode_mapping(
                                public_transport=2000, bicycle=600
                            ),
                            emission=transport_mode_mapping(
                                public_transport=3000, bicycle=0
                            ),
                        ),
                    ),
                }
            ),
        )
        spec = mode_shift_specification(
            mode_to_shift_to=TransportMode.BICYCLE,
            modes_to_shift_from=[TransportMode.PUBLIC_TRANSPORT],
            max_resulting_time=None,
            max_delta_time=1000 * seconds,
        )

        indicators = compute_mode_shift_scenario_indicators(scenario, spec)

        assert indicators.shifter_employees == [jojo]
        assert indicators.annual_distance_by_car_for_car_shifters == 0 * meters / trip

    def test_should_split_commutes_into_shifters(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        geo_employee_factory,
        mode_shift_specification,
        mode_shift_commutes_split,
        current_mode_not_compatible,
        no_shift_time_available,
        maximum_resulting_time_exceeded,
        maximum_delta_time_exceeded,
    ):
        robert = geo_employee_factory(id=0)
        jeanine = geo_employee_factory(id=1)
        mireille = geo_employee_factory(id=2)
        bernardin = geo_employee_factory(id=3)
        clementine = geo_employee_factory(id=4)
        shifting_bernard = consolidated_commute_factory(
            employee=robert,
            data=modal_commute_data_factory(
                best_mode=TransportMode.CAR,
                duration={TransportMode.CAR: 1000, TransportMode.WALK: 1500},
            ),
        )
        non_shifters_commutes = [
            consolidated_commute_factory(
                employee=jeanine,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration={TransportMode.CAR: 1000, TransportMode.WALK: 1500},
                ),
            ),
            consolidated_commute_factory(
                employee=mireille,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR, duration={TransportMode.CAR: 1000}
                ),
            ),
            consolidated_commute_factory(
                employee=bernardin,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 1500, TransportMode.WALK: 2000},
                ),
            ),
            consolidated_commute_factory(
                employee=clementine,
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                    duration={TransportMode.CAR: 200, TransportMode.WALK: 1500},
                ),
            ),
        ]
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.CAR],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=2000 * seconds,
            max_delta_time=1000 * seconds,
        )

        commutes_split = split_shifters_commutes(
            [shifting_bernard] + non_shifters_commutes, split_spec
        )

        assert commutes_split == mode_shift_commutes_split(
            shifters=[shifting_bernard],
            non_shifters=non_shifters_commutes,
            no_shift_reason={
                jeanine: current_mode_not_compatible(),
                mireille: no_shift_time_available(),
                bernardin: maximum_resulting_time_exceeded(2000 * seconds),
                clementine: maximum_delta_time_exceeded(1300 * seconds),
            },
        )

    def test_should_split_empty_list_of_commutes(
        self, mode_shift_specification, mode_shift_commutes_split
    ):
        commutes = []
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.CAR],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=2001 * seconds,
            max_delta_time=1001 * seconds,
        )

        commutes_split = split_shifters_commutes(commutes, split_spec)

        assert commutes_split == mode_shift_commutes_split()

    def test_should_classify_commute_as_shifter(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        mode_shift_specification,
    ):
        commute = consolidated_commute_factory(
            data=modal_commute_data_factory(
                best_mode=TransportMode.CAR,
                duration={TransportMode.CAR: 1000, TransportMode.WALK: 2000},
            ),
        )
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.CAR],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=2001 * seconds,
            max_delta_time=1001 * seconds,
        )

        no_shift_reason = qualify_no_shift_reason(commute, split_spec)

        assert no_shift_reason is None

    def test_should_classify_commute_as_non_shifter_if_commuter_not_using_mode_to_shift_from(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        mode_shift_specification,
        current_mode_not_compatible,
    ):
        commute = consolidated_commute_factory(
            data=modal_commute_data_factory(
                best_mode=TransportMode.CAR,
                duration={TransportMode.CAR: 1000, TransportMode.WALK: 2000},
            ),
        )
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.BICYCLE, TransportMode.PUBLIC_TRANSPORT],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=None,
            max_delta_time=None,
        )

        no_shift_reason = qualify_no_shift_reason(commute, split_spec)

        assert no_shift_reason == current_mode_not_compatible()

    def test_should_classify_commute_as_non_shifter_if_no_time_in_mode_to_shift_to(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        mode_shift_specification,
        no_shift_time_available,
    ):
        commute = consolidated_commute_factory(
            data=modal_commute_data_factory(
                best_mode=TransportMode.BICYCLE,
                duration={
                    TransportMode.WALK: 2000,
                    TransportMode.PUBLIC_TRANSPORT: 1000,
                    TransportMode.BICYCLE: 500,
                },
            ),
        )
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.BICYCLE],
            mode_to_shift_to=TransportMode.CAR,
            max_resulting_time=None,
            max_delta_time=None,
        )

        no_shift_reason = qualify_no_shift_reason(commute, split_spec)

        assert no_shift_reason == no_shift_time_available()

    def test_should_classify_commute_as_non_shifter_if_resulting_time_is_too_high(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        mode_shift_specification,
        maximum_resulting_time_exceeded,
    ):
        commute = consolidated_commute_factory(
            data=modal_commute_data_factory(
                best_mode=TransportMode.CAR,
                duration={
                    TransportMode.CAR: 1000,
                    TransportMode.WALK: 1500,
                    TransportMode.PUBLIC_TRANSPORT: 1000,
                    TransportMode.BICYCLE: 500,
                },
            ),
        )
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.CAR],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=1500 * seconds,
            max_delta_time=None,
        )

        no_shift_reason = qualify_no_shift_reason(commute, split_spec)

        assert no_shift_reason == maximum_resulting_time_exceeded(1500 * seconds)

    def test_should_classify_commute_as_non_shifter_if_delta_time_is_too_high(
        self,
        consolidated_commute_factory,
        modal_commute_data_factory,
        mode_shift_specification,
        maximum_delta_time_exceeded,
    ):
        commute = consolidated_commute_factory(
            data=modal_commute_data_factory(
                best_mode=TransportMode.CAR,
                duration={
                    TransportMode.CAR: 1000,
                    TransportMode.WALK: 1500,
                    TransportMode.PUBLIC_TRANSPORT: 1000,
                    TransportMode.BICYCLE: 500,
                },
            ),
        )
        split_spec = mode_shift_specification(
            modes_to_shift_from=[TransportMode.CAR],
            mode_to_shift_to=TransportMode.WALK,
            max_resulting_time=None,
            max_delta_time=500 * seconds,
        )

        no_shift_reason = qualify_no_shift_reason(commute, split_spec)

        assert no_shift_reason == maximum_delta_time_exceeded(500 * seconds)

    def test_should_compute_shifted_version_of_commutes(
        self, consolidated_commute_factory, modal_commute_data_factory
    ):
        commutes = [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.CAR,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.PUBLIC_TRANSPORT,
                ),
            ),
        ]

        shifted_commutes = compute_shifted_commutes(commutes, TransportMode.BICYCLE)

        assert shifted_commutes == [
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                ),
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    best_mode=TransportMode.BICYCLE,
                ),
            ),
        ]

    def test_should_return_empty_shifted_commute_list_from_no_commutes(self):
        commutes = []

        shifted_commutes = compute_shifted_commutes(commutes, TransportMode.BICYCLE)

        assert shifted_commutes == []

import logging
from typing import List

from api_abstraction.api.travel_time_api import ApiFail, ApiTimeout, TravelTimeApi
from mobility.ir.site import GeoSite
from mobility.ir.study import SiteIsochrone, TimedStudy
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import minutes, seconds


def compute_sites_isochrones(
    sites: List[GeoSite], territory: Territory, travel_timer: TravelTimeApi
) -> SiteIsochrone:
    isochrones_to_compute = {
        TransportMode.WALK: [10 * minutes, 15 * minutes, 20 * minutes],
        TransportMode.PUBLIC_TRANSPORT: [30 * minutes, 45 * minutes, 60 * minutes],
        TransportMode.BICYCLE: [10 * minutes, 20 * minutes, 30 * minutes],
        TransportMode.ELECTRIC_BICYCLE: [10 * minutes, 20 * minutes, 30 * minutes],
        TransportMode.FAST_BICYCLE: [10 * minutes, 20 * minutes, 30 * minutes],
    }
    isochrones: SiteIsochrone = {}
    for destination in sites:
        isochrones[destination] = {}
        try:
            for mode in isochrones_to_compute:
                isochrones[destination][mode] = {}
                for boundary in isochrones_to_compute[mode]:
                    isochrones[destination][mode][boundary] = (
                        travel_timer.compute_isochrone(
                            territory,
                            destination.coordinates,
                            mode,
                            int(boundary // seconds),
                        )
                    )
        except ApiTimeout as e:
            logging.warning(
                f"Travel Time API isochrone computation timed out on {destination}\n{e}"
            )
            continue
        except ApiFail as e:
            logging.warning(
                f"Travel Time API isochrone computation failed on {destination}\n{e}"
            )
            continue
    return isochrones


def compute_isochrone(study: TimedStudy, travel_timer: TravelTimeApi) -> TimedStudy:
    sites = [site for site, _ in study.scenarios.iterate_on_destinations()]
    isochrones = compute_sites_isochrones(sites, study.data.territory, travel_timer)
    return TimedStudy(
        scenarios=study.scenarios,
        poi_scenarios=study.poi_scenarios,
        infrastructure=study.infrastructure,
        data=study.data,
        geocode_failed_sites=study.geocode_failed_sites,
        geocode_failed_employees=study.geocode_failed_employees,
        geocode_failed_both=study.geocode_failed_both,
        isochrones=isochrones,
        coworking_scenario=study.coworking_scenario,
    )

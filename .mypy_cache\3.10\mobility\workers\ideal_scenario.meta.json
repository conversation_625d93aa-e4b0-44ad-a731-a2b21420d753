{"data_mtime": 1752154446, "dep_lines": [6, 16, 17, 21, 24, 25, 8, 13, 14, 15, 26, 27, 28, 29, 34, 40, 54, 55, 7, 41, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.carpool", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.company_potential", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.plan", "mobility.ir.scenario", "mobility.ir.scenario_data", "mobility.ir.study", "mobility.ir.transport", "mobility.ir.work_mode", "mobility.repositories.abstract_repositories", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "dataclasses", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.crit_air", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.site", "mobility.ir.study_types", "mobility.repositories", "typing_extensions"], "hash": "26ee9611bb6d8519b29c9a64017a3a3c37f50dc6", "id": "mobility.workers.ideal_scenario", "ignore_all": false, "interface_hash": "536cf4f4bc209772b98ff054c6c5287d75d9d648", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\ideal_scenario.py", "plugin_data": null, "size": 51149, "suppressed": [], "version_id": "1.16.1"}
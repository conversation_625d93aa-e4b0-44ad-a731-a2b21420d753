{"data_mtime": 1753087663, "dep_lines": [3, 9, 10, 11, 24, 8, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.scenario", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.funky", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "fractions", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.workers", "mobility.workers.health_cost_calculator", "mobility.workers.zfe_impact_computer", "numbers", "typing_extensions"], "hash": "2f2e26d52d9a007f45cdc638a825f3704ca5ebff", "id": "mobility.converters.indicators.remote_scenario", "ignore_all": false, "interface_hash": "b586977a34d3993b552ffa808f8db4087e970543", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\remote_scenario.py", "plugin_data": null, "size": 7862, "suppressed": [], "version_id": "1.16.1"}
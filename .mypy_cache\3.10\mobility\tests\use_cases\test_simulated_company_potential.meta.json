{"data_mtime": 1752154450, "dep_lines": [11, 6, 8, 9, 10, 12, 13, 15, 18, 2, 7, 14, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "mobility.builders.territory_database", "mobility.ir.company_potential", "mobility.ir.crit_air", "mobility.ir.geo_study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.repositories.abstract_repositories", "mobility.use_cases.simulated_company_potential", "unittest.mock", "mobility.funky", "mobility.quantity", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.builders", "mobility.ir", "mobility.ir.cost", "mobility.ir.indicators", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.site", "mobility.ir.study", "mobility.ir.zfe", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "re", "types", "typing_extensions"], "hash": "f96f595af31f6fe953f33dc2c342028559b8475c", "id": "mobility.tests.use_cases.test_simulated_company_potential", "ignore_all": false, "interface_hash": "2011ded59e0e99a67a9f77e9e03ed7c87013a5bb", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_simulated_company_potential.py", "plugin_data": null, "size": 25433, "suppressed": [], "version_id": "1.16.1"}
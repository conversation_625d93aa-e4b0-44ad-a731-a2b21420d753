{"data_mtime": 1753176774, "dep_lines": [11, 12, 13, 14, 15, 21, 27, 28, 29, 1, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 7, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 5], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.date_iterators", "api_abstraction.api.event_reporter", "api_abstraction.api.hypotheses", "api_abstraction.api.travel_time_api", "api_abstraction.navitia.settings", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "re", "collections", "datetime", "functools", "typing", "requests", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "enum", "http", "http.cookiejar", "json", "json.decoder", "mobility", "mobility.ir", "mobility.ir.country", "requests.api", "requests.auth", "requests.exceptions", "requests.models", "types", "typing_extensions"], "hash": "cd28afb3fa66a07d0c7d400c623443983ee73613", "id": "api_abstraction.navitia.api", "ignore_all": false, "interface_hash": "4abda7471b38bb36a6560620bdb5205a4dc54b8b", "mtime": 1751895952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\navitia\\api.py", "plugin_data": null, "size": 13218, "suppressed": ["geopy.distance", "geopy", "g<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}
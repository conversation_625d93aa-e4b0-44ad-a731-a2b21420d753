import os

from calibration.whats_the_best_mode import (
    CalibrationData,
    CalibrationDataEntry,
    ImmutableDict,
    TransportMode,
)


class TestCalibrationDataEntry:
    def test_create_empty_entry(self):
        entry = CalibrationDataEntry.empty()

        assert entry.nb_samples == 0

    def test_should_output_jsonable_format(self):
        entry = CalibrationDataEntry.empty()
        entry.nb_samples = 1
        entry.nb_times_mode_preferred[TransportMode.WALK] = 1

        json_entry = entry.to_json()

        assert json_entry == {
            "nb_samples": 1,
            "nb_times_mode_preferred": {
                TransportMode.WALK: 1,
                TransportMode.PUBLIC_TRANSPORT: 0,
                TransportMode.CAR: 0,
                TransportMode.BICYCLE: 0,
                TransportMode.CARPOOLING: 0,
                TransportMode.ELECTRIC_BICYCLE: 0,
                TransportMode.ELECTRIC_CAR: 0,
                TransportMode.MOTORCYCLE: 0,
                TransportMode.AIRPLANE: 0,
                TransportMode.ELECTRIC_MOTORCYCLE: 0,
                TransportMode.FAST_BICYCLE: 0,
                TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
            },
        }

    def test_should_init_from_json(self):
        json_entry = {
            "nb_samples": 1,
            "nb_times_mode_preferred": {
                TransportMode.WALK: 1,
                TransportMode.PUBLIC_TRANSPORT: 0,
                TransportMode.CAR: 0,
                TransportMode.BICYCLE: 0,
                TransportMode.CARPOOLING: 0,
                TransportMode.ELECTRIC_BICYCLE: 0,
                TransportMode.ELECTRIC_CAR: 0,
                TransportMode.MOTORCYCLE: 0,
                TransportMode.AIRPLANE: 0,
                TransportMode.ELECTRIC_MOTORCYCLE: 0,
                TransportMode.FAST_BICYCLE: 0,
                TransportMode.CAR_PUBLIC_TRANSPORT: 0,
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
            },
        }

        entry_from_json = CalibrationDataEntry.from_json(json_entry)

        expected_entry = CalibrationDataEntry.empty()
        expected_entry.nb_samples = 1
        expected_entry.nb_times_mode_preferred[TransportMode.WALK] = 1
        assert entry_from_json == expected_entry

    def test_should_add_inner_data(self):
        entry1 = CalibrationDataEntry.empty()
        entry1.nb_samples = 1
        entry1.nb_times_mode_preferred[TransportMode.WALK] = 1
        entry2 = CalibrationDataEntry.empty()
        entry2.nb_samples = 2
        entry2.nb_times_mode_preferred[TransportMode.CAR] = 1
        entry2.nb_times_mode_preferred[TransportMode.WALK] = 1

        entrysum = entry1 + entry2

        expected_sum = CalibrationDataEntry.empty()
        expected_sum.nb_samples = 3
        expected_sum.nb_times_mode_preferred[TransportMode.CAR] = 1
        expected_sum.nb_times_mode_preferred[TransportMode.WALK] = 2
        assert entrysum == expected_sum


class TestCalibrationData:
    def test_should_init_without_file(self):
        calibration_data = CalibrationData("")

        assert len(calibration_data.data) == 0

    def test_should_record_additional_data(self):
        calibration_data = CalibrationData("")

        calibration_data.add_data(
            ImmutableDict({TransportMode.WALK: 1}), {TransportMode.WALK}
        )

        assert len(calibration_data.data) == 1

    def test_should_save_itself_to_file(self, tmp_path):
        file_name = str(tmp_path / "my.json")
        calibration_data = CalibrationData(file_name)
        calibration_data.add_data(
            ImmutableDict({TransportMode.WALK: 1}), {TransportMode.WALK}
        )

        calibration_data.save()

        assert os.path.exists(file_name)

    def test_should_construct_data_from_file(self, tmp_path):
        file_name = str(tmp_path / "my.json")
        calibration_data = CalibrationData(file_name)
        calibration_data.add_data(
            ImmutableDict({TransportMode.WALK: 1}), {TransportMode.WALK}
        )
        calibration_data.save()

        loaded_calibration_data = CalibrationData(file_name)

        assert loaded_calibration_data.data == calibration_data.data

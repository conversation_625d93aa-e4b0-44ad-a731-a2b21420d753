{"data_mtime": 1752154451, "dep_lines": [19, 25, 26, 9, 10, 11, 12, 13, 21, 22, 23, 24, 27, 36, 37, 38, 39, 40, 71, 72, 73, 75, 81, 14, 20, 74, 1, 2, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.converters.indicators.mode_shift_scenario", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.trivial.trivial_geocoder", "api_abstraction.trivial.trivial_travel_time", "mobility.builders.territory_builder", "mobility.ir.commute_data", "mobility.ir.crit_air", "mobility.ir.error", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.map_elements", "mobility.ir.mode_constraints", "mobility.ir.poi", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.territory", "mobility.ir.transport", "mobility.serializers.geopackage_serializer", "mobility.workers.carpool_computer", "mobility.constants", "mobility.funky", "mobility.serializers", "dataclasses", "typing", "factory", "pytest", "pytest_factoryboy", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pytest", "_pytest.config", "_pytest.fixtures", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "api_abstraction.trivial", "enum", "factory.base", "io", "mobility.builders", "mobility.converters", "mobility.converters.indicators", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.country", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.scenario_data", "mobility.quantity", "mobility.serializers.json_serializer", "mobility.workers", "os", "pytest_factoryboy.fixture", "typing_extensions"], "hash": "3f9c73ddc4f1d89bd9f0d6a73bbe6e9659061c52", "id": "mobility.tests.conftest", "ignore_all": false, "interface_hash": "b0d83a98dc521ba438c066d8fb5ce072c6d0b01d", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\conftest.py", "plugin_data": null, "size": 51017, "suppressed": ["pandas"], "version_id": "1.16.1"}
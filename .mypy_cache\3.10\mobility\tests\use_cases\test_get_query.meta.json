{"data_mtime": 1751444419, "dep_lines": [4, 5, 2, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.repositories.abstract_repositories", "mobility.use_cases.get_query", "unittest.mock", "typing", "unittest", "builtins", "_frozen_importlib", "abc", "mobility.ir", "mobility.ir.query_record", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case"], "hash": "0fe9cb39dee5285def53b7f2dd31d77992b22e5d", "id": "mobility.tests.use_cases.test_get_query", "ignore_all": false, "interface_hash": "f537d61bd56a1f9a28ea4b23d93b4fe7cdbeea4b", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_get_query.py", "plugin_data": null, "size": 3937, "suppressed": [], "version_id": "1.16.1"}
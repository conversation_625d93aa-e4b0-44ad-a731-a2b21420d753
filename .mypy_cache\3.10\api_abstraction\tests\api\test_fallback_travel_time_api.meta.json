{"data_mtime": 1752056504, "dep_lines": [5, 6, 7, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.fallback_travel_time_api", "mobility.ir.geo_study", "mobility.ir.transport", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "abc", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "contextlib", "enum", "functools", "mobility", "mobility.ir", "re", "typing_extensions"], "hash": "0ad5a4f3ae271d0fd6ebbc996d1b0708be0d3f29", "id": "api_abstraction.tests.api.test_fallback_travel_time_api", "ignore_all": false, "interface_hash": "4066541cb80db53d33bae328b3c512a6bf1a4222", "mtime": 1752052790, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\api\\test_fallback_travel_time_api.py", "plugin_data": null, "size": 2485, "suppressed": [], "version_id": "1.16.1"}
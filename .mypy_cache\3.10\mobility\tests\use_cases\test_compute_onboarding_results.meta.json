{"data_mtime": 1751444428, "dep_lines": [7, 12, 14, 15, 2, 6, 13, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.onboarding_form_step", "mobility.ir.plan", "mobility.repositories.abstract_repositories", "mobility.use_cases.compute_onboarding_results", "unittest.mock", "mobility.funky", "mobility.quantity", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.ir", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "re", "typing_extensions"], "hash": "0bc988894d32c10a7d789e671cdc5cd1690ab7ef", "id": "mobility.tests.use_cases.test_compute_onboarding_results", "ignore_all": false, "interface_hash": "96df5fac6f4a3ae4aa528a5b9ee8d8cd33e70339", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_compute_onboarding_results.py", "plugin_data": null, "size": 10163, "suppressed": [], "version_id": "1.16.1"}
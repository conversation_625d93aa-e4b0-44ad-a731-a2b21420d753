{".class": "MypyFile", "_fullname": "mobility.tests.workers.test_emission_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "Scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenario", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "TestEmissionComputation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation", "name": "TestEmissionComputation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_emission_computer", "mro": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", "builtins.object"], "names": {".class": "SymbolTable", "test_should_compute_emission_for_10km_in_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "emission"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_compute_emission_for_10km_in_mode", "name": "test_should_compute_emission_for_10km_in_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "emission"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_emission_for_10km_in_mode of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_compute_emission_for_10km_in_mode", "name": "test_should_compute_emission_for_10km_in_mode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_compute_emission_for_any_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_compute_emission_for_any_mode", "name": "test_should_compute_emission_for_any_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_emission_for_any_mode of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_compute_emission_for_any_mode", "name": "test_should_compute_emission_for_any_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_emission_for_any_mode of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_compute_emission_in_alternative_arrival_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "base_commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_compute_emission_in_alternative_arrival_time", "name": "test_should_compute_emission_in_alternative_arrival_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "base_commute_data"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_compute_emission_in_alternative_arrival_time of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_fail_to_compute_emission_if_distance_missing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_fail_to_compute_emission_if_distance_missing", "name": "test_should_fail_to_compute_emission_if_distance_missing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_fail_to_compute_emission_if_distance_missing of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_fail_to_compute_emission_if_distance_missing", "name": "test_should_fail_to_compute_emission_if_distance_missing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_not_compute_emission_if_duration_missing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timed_commute_data_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_not_compute_emission_if_duration_missing", "name": "test_should_not_compute_emission_if_duration_missing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timed_commute_data_factory"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_not_compute_emission_if_duration_missing of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_set_emissions_to_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_set_emissions_to_zero", "name": "test_should_set_emissions_to_zero", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_set_emissions_to_zero of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_set_emissions_to_zero", "name": "test_should_set_emissions_to_zero", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_set_emissions_to_zero_even_without_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_set_emissions_to_zero_even_without_distance", "name": "test_should_set_emissions_to_zero_even_without_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "timed_commute_data_factory", "mode"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestEmissionComputation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_set_emissions_to_zero_even_without_distance of TestEmissionComputation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.test_should_set_emissions_to_zero_even_without_distance", "name": "test_should_set_emissions_to_zero_even_without_distance", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_emission_computer.TestEmissionComputation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_emission_computer.TestEmissionComputation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestStudyFilling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.workers.test_emission_computer.TestStudyFilling", "name": "TestStudyFilling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.workers.test_emission_computer.TestStudyFilling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.workers.test_emission_computer", "mro": ["mobility.tests.workers.test_emission_computer.TestStudyFilling", "builtins.object"], "names": {".class": "SymbolTable", "test_should_fill_missing_emissions_in_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "timed_study_factory", "geo_employee", "geo_site", "scenario_data_factory", "point_of_interest", "timed_infrastructure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.workers.test_emission_computer.TestStudyFilling.test_should_fill_missing_emissions_in_study", "name": "test_should_fill_missing_emissions_in_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "timed_study_factory", "geo_employee", "geo_site", "scenario_data_factory", "point_of_interest", "timed_infrastructure"], "arg_types": ["mobility.tests.workers.test_emission_computer.TestStudyFilling", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_fill_missing_emissions_in_study of TestStudyFilling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.workers.test_emission_computer.TestStudyFilling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.workers.test_emission_computer.TestStudyFilling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.workers.test_emission_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_missing_emissions_from_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.emission_computer.compute_missing_emissions_from_distance", "kind": "Gdef"}, "compute_missing_emissions_from_distance_with_alternatives": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.emission_computer.compute_missing_emissions_from_distance_with_alternatives", "kind": "Gdef"}, "compute_mode_emission_from_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.emission_computer.compute_mode_emission_from_distance", "kind": "Gdef"}, "fill_missing_emissions": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.emission_computer.fill_missing_emissions", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "mobility\\tests\\workers\\test_emission_computer.py"}
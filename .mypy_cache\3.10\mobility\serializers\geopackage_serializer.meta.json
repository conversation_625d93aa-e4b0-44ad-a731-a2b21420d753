{"data_mtime": 1752154448, "dep_lines": [18, 19, 12, 15, 16, 17, 20, 27, 28, 29, 30, 38, 40, 41, 13, 14, 39, 1, 2, 3, 4, 5, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 9, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.indicators.scenario_indicators", "mobility.builders.territory_database", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.plan", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.ideal_scenario", "mobility.workers.zfe_impact_computer", "mobility.constants", "mobility.funky", "mobility.quantity", "dataclasses", "logging", "shutil", "pathlib", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.builders", "mobility.ir", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.poi", "mobility.ir.study_types", "mobility.workers", "os", "types", "typing_extensions"], "hash": "d685c002e28a354b1db5d081d8799d172d31c245", "id": "mobility.serializers.geopackage_serializer", "ignore_all": false, "interface_hash": "d41c86736a27eca545e0d8d6284d4bb3921b8b91", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\geopackage_serializer.py", "plugin_data": null, "size": 31895, "suppressed": ["fiona.crs", "shapely.geometry", "fiona"], "version_id": "1.16.1"}
{"data_mtime": 1752154447, "dep_lines": [3, 4, 10, 11, 12, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.scenario_indicators", "mobility.ir.transport", "mobility.ir.work_mode", "mobility.workers.ideal_scenario", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "abc", "configparser", "enum", "mobility.ir", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.plan", "mobility.ir.site", "mobility.quantity", "mobility.workers", "types", "typing", "typing_extensions"], "hash": "217b597ed5fe22c9763e0bb6e3cb8ab9c46ce0da", "id": "mobility.serializers.phraser", "ignore_all": false, "interface_hash": "a480f5366305f09d9fca6f615400f0642ef646d7", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\phraser.py", "plugin_data": null, "size": 8682, "suppressed": [], "version_id": "1.16.1"}
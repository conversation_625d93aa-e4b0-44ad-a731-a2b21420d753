{"data_mtime": 1751444404, "dep_lines": [6, 10, 11, 16, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.osm_infrastructure", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.transport", "dataclasses", "typing", "osmium", "builtins", "_frozen_importlib", "abc", "enum", "mobility.builders", "mobility.ir", "osmium.io", "osmium.simple_handler", "typing_extensions"], "hash": "3172b52f489efaabf4482adb542d7b4e8ac92aa2", "id": "mobility.tests.builders.test_osm_infrastructure", "ignore_all": false, "interface_hash": "a4d2ec1ad6b855b365329b24f3f02caad296bba2", "mtime": 1748872968, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\builders\\test_osm_infrastructure.py", "plugin_data": null, "size": 22389, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1752154450, "dep_lines": [13, 12, 14, 15, 16, 18, 5, 11, 17, 1, 2, 3, 4, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.geo_study", "mobility.ir.plan", "mobility.ir.study", "mobility.ir.transport", "mobility.serializers.geopackage_serializer", "unittest.mock", "mobility.funky", "mobility.quantity", "logging", "os", "pathlib", "typing", "pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.infrastructure", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "re", "types", "typing_extensions", "unittest"], "hash": "46d8a6eeb046001c87a92be6266b60ea9347f3f5", "id": "mobility.tests.serializers.test_geopackage_serializer", "ignore_all": false, "interface_hash": "e3ee93a51be27c326b652327c52d2625ffa9c9b4", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\serializers\\test_geopackage_serializer.py", "plugin_data": null, "size": 91692, "suppressed": ["fiona.crs", "shapely.geometry"], "version_id": "1.16.1"}
import logging
import os
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import Mock, call, patch

import pytest
from fiona.crs import CRS
from shapely.geometry import MultiPolygon, Point, mapping, shape

from mobility.funky import ImmutableDict
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.indicators.indicators import Indicators
from mobility.ir.plan import Plan
from mobility.ir.study import Commute, Scenario, Scenarios
from mobility.ir.transport import TransportMode, TransportType
from mobility.quantity import minutes
from mobility.serializers.geopackage_serializer import (
    GeopackageStudy,
    POIFeatureLayer,
    PostCodeModalShareFeatureLayer,
    StudyGeopackageSerializer,
)


@pytest.fixture
def temp_dir(tmpdir: Any) -> str:
    temp_dir = tmpdir.mkdir("territories_dir")
    file = temp_dir.join("postcodes_coordinates.json")
    file.write_text(
        """{
            "12123": "43.1 2.1",
            "31000": "43.2 2.2",
            "69001": "43.3 2.3"}
    """,
        encoding="utf-8",
    )
    return str(temp_dir)


def _side_effect(layer_name: str, features: List[Dict]):
    raise ValueError(f"No features found for layer {layer_name}.")


class TestGeopackageStudy:

    def test_construct_sites_returns_employees_features(
        self,
        consolidated_study_factory: Any,
        geo_site_factory: Any,
        address_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        sites = [
            geo_site_factory(
                nickname="Site1",
                address="Address1",
                coordinates=GeoCoordinates(10, 20),
                address_details=address_factory(normalized="Address1"),
            ),
            geo_site_factory(
                nickname="Site2",
                address="Address2",
                coordinates=GeoCoordinates(30, 40),
                address_details=address_factory(normalized="Address2"),
            ),
        ]

        sites = gpkg_study._construct_sites(sites)

        expected_sites = [
            {
                "geometry": mapping(Point(20, 10)),
                "properties": {
                    "name": "Site1",
                    "address_provided": "Address1",
                    "address_normalized": "Address1",
                },
            },
            {
                "geometry": mapping(Point(40, 30)),
                "properties": {
                    "name": "Site2",
                    "address_provided": "Address2",
                    "address_normalized": "Address2",
                },
            },
        ]
        assert sites == expected_sites

    @patch(
        "mobility.serializers.geopackage_serializer.Indicators.get_scenario_indicators"
    )
    @patch("mobility.serializers.geopackage_serializer.IdealScenarioComputer")
    def test_construct_employees_present_scenario_data(
        self,
        mock_IdealScenarioComputer: Any,
        mock_get_scenario_indicators: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
        consolidated_study_factory: Any,
        scenario_indicators: Any,
        scenario_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        scenario_data = scenario_data_factory(is_present=True, nickname="Actuel")
        employees = {
            geo_employee_factory(coordinates=GeoCoordinates(10, 20)): {
                (
                    scenario_data,
                    geo_site_factory(),
                    modal_commute_data_factory(),
                )
            },
            geo_employee_factory(coordinates=GeoCoordinates(30, 40)): {
                (
                    scenario_data,
                    geo_site_factory(),
                    modal_commute_data_factory(),
                )
            },
        }
        mock_get_scenario_indicators.return_value = scenario_indicators()
        scenario_indicators = mock_get_scenario_indicators.return_value
        scenario_indicators._bike_shift_scenarios = []
        scenario_indicators._public_transport_shift_scenarios = []
        scenario_indicators._remote_scenarios = []

        result = gpkg_study._construct_employees(employees)

        assert len(result) == 2
        for feature, employee in zip(result, employees.keys()):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(employee.coordinates.longitude, employee.coordinates.latitude)
            )
            assert set(feature["properties"].keys()) == {
                "employee",
                "site",
                "mode",
                "mode_declared",
                "duration_walk_Actuel",
                "duration_bicycle_Actuel",
                "duration_car_Actuel",
                "duration_public_transport_Actuel",
                "duration_carpooling_Actuel",
                "duration_electric_bicycle_Actuel",
                "duration_electric_car_Actuel",
                "duration_motorcycle_Actuel",
                "duration_airplane_Actuel",
                "duration_electric_motorcycle_Actuel",
                "duration_fast_bicycle_Actuel",
                "duration_car_public_transport_Actuel",
                "duration_bicycle_public_transport_Actuel",
                "distance_walk_Actuel",
                "distance_bicycle_Actuel",
                "distance_car_Actuel",
                "distance_public_transport_Actuel",
                "distance_carpooling_Actuel",
                "distance_electric_bicycle_Actuel",
                "distance_electric_car_Actuel",
                "distance_motorcycle_Actuel",
                "distance_airplane_Actuel",
                "distance_electric_motorcycle_Actuel",
                "distance_fast_bicycle_Actuel",
                "distance_car_public_transport_Actuel",
                "distance_bicycle_public_transport_Actuel",
                "emission_walk_Actuel",
                "emission_bicycle_Actuel",
                "emission_car_Actuel",
                "emission_public_transport_Actuel",
                "emission_carpooling_Actuel",
                "emission_electric_bicycle_Actuel",
                "emission_electric_car_Actuel",
                "emission_motorcycle_Actuel",
                "emission_airplane_Actuel",
                "emission_electric_motorcycle_Actuel",
                "emission_fast_bicycle_Actuel",
                "emission_car_public_transport_Actuel",
                "emission_bicycle_public_transport_Actuel",
                "duration_current_mode",
                "distance_current_mode",
                "emission_current_mode",
                "carpool_group_id_Actuel",
                "is_carpool_driver_Actuel",
                "ideal_mode_Actuel",
            }

    @patch(
        "mobility.serializers.geopackage_serializer.Indicators.get_scenario_indicators"
    )
    @patch("mobility.serializers.geopackage_serializer.IdealScenarioComputer")
    def test_construct_employees_with_other_scenario_data(
        self,
        mock_IdealScenarioComputer: Any,
        mock_get_scenario_indicators: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
        consolidated_study_factory: Any,
        scenario_indicators: Any,
        scenario_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employees = {
            geo_employee_factory(coordinates=GeoCoordinates(10, 20)): {
                (
                    scenario_data_factory(is_present=False, nickname="Scenario00"),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                )
            },
            geo_employee_factory(coordinates=GeoCoordinates(30, 40)): {
                (
                    scenario_data_factory(
                        id=1, is_present=False, nickname="Scenario00"
                    ),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                )
            },
        }
        mock_get_scenario_indicators.return_value = scenario_indicators()
        scenario_indicators = mock_get_scenario_indicators.return_value
        scenario_indicators._bike_shift_scenarios = []
        scenario_indicators._public_transport_shift_scenarios = []
        scenario_indicators._remote_scenarios = []

        result = gpkg_study._construct_employees(employees)

        assert len(result) == 2
        for feature, employee in zip(result, employees.keys()):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(employee.coordinates.longitude, employee.coordinates.latitude)
            )
            assert set(feature["properties"].keys()) == {
                "employee",
                "site",
                "mode",
                "mode_declared",
                "duration_walk_Scenario00",
                "duration_bicycle_Scenario00",
                "duration_car_Scenario00",
                "duration_public_transport_Scenario00",
                "duration_carpooling_Scenario00",
                "duration_electric_bicycle_Scenario00",
                "duration_electric_car_Scenario00",
                "duration_motorcycle_Scenario00",
                "duration_airplane_Scenario00",
                "duration_electric_motorcycle_Scenario00",
                "duration_fast_bicycle_Scenario00",
                "duration_car_public_transport_Scenario00",
                "duration_bicycle_public_transport_Scenario00",
                "distance_walk_Scenario00",
                "distance_bicycle_Scenario00",
                "distance_car_Scenario00",
                "distance_public_transport_Scenario00",
                "distance_carpooling_Scenario00",
                "distance_electric_bicycle_Scenario00",
                "distance_electric_car_Scenario00",
                "distance_motorcycle_Scenario00",
                "distance_airplane_Scenario00",
                "distance_electric_motorcycle_Scenario00",
                "distance_fast_bicycle_Scenario00",
                "distance_car_public_transport_Scenario00",
                "distance_bicycle_public_transport_Scenario00",
                "emission_walk_Scenario00",
                "emission_bicycle_Scenario00",
                "emission_car_Scenario00",
                "emission_public_transport_Scenario00",
                "emission_carpooling_Scenario00",
                "emission_electric_bicycle_Scenario00",
                "emission_electric_car_Scenario00",
                "emission_motorcycle_Scenario00",
                "emission_airplane_Scenario00",
                "emission_electric_motorcycle_Scenario00",
                "emission_fast_bicycle_Scenario00",
                "emission_car_public_transport_Scenario00",
                "emission_bicycle_public_transport_Scenario00",
                "duration_current_mode",
                "distance_current_mode",
                "emission_current_mode",
                "carpool_group_id_Scenario00",
                "is_carpool_driver_Scenario00",
                "ideal_mode_Scenario00",
            }

    @patch(
        "mobility.serializers.geopackage_serializer.Indicators.get_scenario_indicators"
    )
    @patch("mobility.serializers.geopackage_serializer.IdealScenarioComputer")
    def test_construct_employees_with_multiple_scenario_data(
        self,
        mock_IdealScenarioComputer: Any,
        mock_get_scenario_indicators: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
        consolidated_study_factory: Any,
        scenario_indicators: Any,
        scenario_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employees = {
            geo_employee_factory(coordinates=GeoCoordinates(10, 20)): {
                (
                    scenario_data_factory(is_present=False, nickname="Scenario00"),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                ),
                (
                    scenario_data_factory(is_present=False, nickname="Scenario11"),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                ),
            },
            geo_employee_factory(coordinates=GeoCoordinates(30, 40)): {
                (
                    scenario_data_factory(
                        id=1, is_present=False, nickname="Scenario00"
                    ),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                ),
                (
                    scenario_data_factory(
                        id=2, is_present=False, nickname="Scenario11"
                    ),
                    geo_site_factory(),
                    modal_commute_data_factory(),
                ),
            },
        }
        mock_get_scenario_indicators.return_value = scenario_indicators()
        scenario_indicators = mock_get_scenario_indicators.return_value
        scenario_indicators._bike_shift_scenarios = []
        scenario_indicators._public_transport_shift_scenarios = []
        scenario_indicators._remote_scenarios = []

        result = gpkg_study._construct_employees(employees)

        assert len(result) == 2
        for feature, employee in zip(result, employees.keys()):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(employee.coordinates.longitude, employee.coordinates.latitude)
            )
            assert set(feature["properties"].keys()) == {
                "employee",
                "site",
                "mode",
                "mode_declared",
                "duration_walk_Scenario00",
                "duration_bicycle_Scenario00",
                "duration_car_Scenario00",
                "duration_public_transport_Scenario00",
                "duration_carpooling_Scenario00",
                "duration_electric_bicycle_Scenario00",
                "duration_electric_car_Scenario00",
                "duration_motorcycle_Scenario00",
                "duration_airplane_Scenario00",
                "duration_electric_motorcycle_Scenario00",
                "duration_fast_bicycle_Scenario00",
                "duration_car_public_transport_Scenario00",
                "duration_bicycle_public_transport_Scenario00",
                "distance_walk_Scenario00",
                "distance_bicycle_Scenario00",
                "distance_car_Scenario00",
                "distance_public_transport_Scenario00",
                "distance_carpooling_Scenario00",
                "distance_electric_bicycle_Scenario00",
                "distance_electric_car_Scenario00",
                "distance_motorcycle_Scenario00",
                "distance_airplane_Scenario00",
                "distance_electric_motorcycle_Scenario00",
                "distance_fast_bicycle_Scenario00",
                "distance_car_public_transport_Scenario00",
                "distance_bicycle_public_transport_Scenario00",
                "emission_walk_Scenario00",
                "emission_bicycle_Scenario00",
                "emission_car_Scenario00",
                "emission_public_transport_Scenario00",
                "emission_carpooling_Scenario00",
                "emission_electric_bicycle_Scenario00",
                "emission_electric_car_Scenario00",
                "emission_motorcycle_Scenario00",
                "emission_airplane_Scenario00",
                "emission_electric_motorcycle_Scenario00",
                "emission_fast_bicycle_Scenario00",
                "emission_car_public_transport_Scenario00",
                "emission_bicycle_public_transport_Scenario00",
                "duration_current_mode",
                "distance_current_mode",
                "emission_current_mode",
                "carpool_group_id_Scenario00",
                "is_carpool_driver_Scenario00",
                "duration_walk_Scenario11",
                "duration_bicycle_Scenario11",
                "duration_car_Scenario11",
                "duration_public_transport_Scenario11",
                "duration_carpooling_Scenario11",
                "duration_electric_bicycle_Scenario11",
                "duration_electric_car_Scenario11",
                "duration_motorcycle_Scenario11",
                "duration_airplane_Scenario11",
                "duration_electric_motorcycle_Scenario11",
                "duration_fast_bicycle_Scenario11",
                "duration_car_public_transport_Scenario11",
                "duration_bicycle_public_transport_Scenario11",
                "distance_walk_Scenario11",
                "distance_bicycle_Scenario11",
                "distance_car_Scenario11",
                "distance_public_transport_Scenario11",
                "distance_carpooling_Scenario11",
                "distance_electric_bicycle_Scenario11",
                "distance_electric_car_Scenario11",
                "distance_motorcycle_Scenario11",
                "distance_airplane_Scenario11",
                "distance_electric_motorcycle_Scenario11",
                "distance_fast_bicycle_Scenario11",
                "distance_car_public_transport_Scenario11",
                "distance_bicycle_public_transport_Scenario11",
                "emission_walk_Scenario11",
                "emission_bicycle_Scenario11",
                "emission_car_Scenario11",
                "emission_public_transport_Scenario11",
                "emission_carpooling_Scenario11",
                "emission_electric_bicycle_Scenario11",
                "emission_electric_car_Scenario11",
                "emission_motorcycle_Scenario11",
                "emission_airplane_Scenario11",
                "emission_electric_motorcycle_Scenario11",
                "emission_fast_bicycle_Scenario11",
                "emission_car_public_transport_Scenario11",
                "emission_bicycle_public_transport_Scenario11",
                "carpool_group_id_Scenario11",
                "is_carpool_driver_Scenario11",
                "ideal_mode_Scenario00",
                "ideal_mode_Scenario11",
            }

    @patch(
        "mobility.serializers.geopackage_serializer.Indicators.get_scenario_indicators"
    )
    @patch("mobility.serializers.geopackage_serializer.IdealScenarioComputer")
    def test_construct_employees_properties(
        self,
        mock_IdealScenarioComputer: Any,
        mock_get_scenario_indicators: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
        consolidated_study_factory: Any,
        scenario_indicators: Any,
        scenario_data_factory: Any,
        temp_dir: str,
    ) -> None:
        employee = geo_employee_factory(
            coordinates=GeoCoordinates(10, 20),
        )
        site = geo_site_factory()
        commute_data = modal_commute_data_factory()

        study = consolidated_study_factory()
        scenario_name = "Scenario00"
        scenario_data = scenario_data_factory(nickname=scenario_name, is_present=False)
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        mock_get_scenario_indicators.return_value = scenario_indicators()
        scenario_indicators = mock_get_scenario_indicators.return_value
        scenario_indicators._bike_shift_scenarios = []
        scenario_indicators._public_transport_shift_scenarios = []
        scenario_indicators._remote_scenarios = []
        employees_plan = ImmutableDict({employee: Plan.NO_CHANGE})

        result = gpkg_study._construct_employees_properties(
            employee,
            site,
            commute_data,
            scenario_data,
            scenario_indicators,
            employees_plan,
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "employee": "employee_01",
            "site": "site_01",
            "mode": "WALK",
            "mode_declared": "WALK",
            "duration_walk_Scenario00": 3200,
            "duration_bicycle_Scenario00": 1600,
            "duration_car_Scenario00": 800,
            "duration_public_transport_Scenario00": 1200,
            "duration_carpooling_Scenario00": 900,
            "duration_electric_bicycle_Scenario00": 1500,
            "duration_electric_car_Scenario00": 850,
            "duration_motorcycle_Scenario00": 700,
            "duration_airplane_Scenario00": 3600,
            "duration_electric_motorcycle_Scenario00": 750,
            "duration_fast_bicycle_Scenario00": 1400,
            "duration_car_public_transport_Scenario00": 1000,
            "duration_bicycle_public_transport_Scenario00": 1100,
            "distance_walk_Scenario00": 1800,
            "distance_bicycle_Scenario00": 1800,
            "distance_car_Scenario00": 2000,
            "distance_public_transport_Scenario00": 2100,
            "distance_carpooling_Scenario00": 2100,
            "distance_electric_bicycle_Scenario00": 1800,
            "distance_electric_car_Scenario00": 2000,
            "distance_motorcycle_Scenario00": 2000,
            "distance_airplane_Scenario00": 20000,
            "distance_electric_motorcycle_Scenario00": 2000,
            "distance_fast_bicycle_Scenario00": 2000,
            "distance_car_public_transport_Scenario00": 2200,
            "distance_bicycle_public_transport_Scenario00": 2100,
            "emission_walk_Scenario00": 0,
            "emission_bicycle_Scenario00": 0,
            "emission_car_Scenario00": 4800,
            "emission_public_transport_Scenario00": 1000,
            "emission_carpooling_Scenario00": 2400,
            "emission_electric_bicycle_Scenario00": 10,
            "emission_electric_car_Scenario00": 1200,
            "emission_motorcycle_Scenario00": 4800,
            "emission_airplane_Scenario00": 10000,
            "emission_electric_motorcycle_Scenario00": 15,
            "emission_fast_bicycle_Scenario00": 10,
            "emission_car_public_transport_Scenario00": 1500,
            "emission_bicycle_public_transport_Scenario00": 1000,
            "duration_current_mode": 3200,
            "distance_current_mode": 1800,
            "emission_current_mode": 0,
            "carpool_group_id_Scenario00": None,
            "is_carpool_driver_Scenario00": 0,
            "ideal_mode_Scenario00": Plan.NO_CHANGE,
        }

    @patch(
        "mobility.serializers.geopackage_serializer.Indicators.get_scenario_indicators"
    )
    @patch("mobility.serializers.geopackage_serializer.IdealScenarioComputer")
    def test_construct_employees_properties_with_none(
        self,
        mock_IdealScenarioComputer: Any,
        mock_get_scenario_indicators: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        modal_commute_data_factory: Any,
        consolidated_study_factory: Any,
        scenario_indicators: Any,
        scenario_data_factory: Any,
        temp_dir: str,
    ) -> None:
        employee = geo_employee_factory(
            coordinates=GeoCoordinates(10, 20),
        )
        site = geo_site_factory()
        commute_data = modal_commute_data_factory(
            duration=ImmutableDict(
                {
                    TransportMode.CAR: 800,
                }
            ),
            distance=ImmutableDict(
                {
                    TransportMode.CAR: 2000,
                }
            ),
            emission=ImmutableDict(
                {
                    TransportMode.CAR: 4800,
                }
            ),
            best_mode=TransportMode.CAR,
        )

        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        scenario_name = "Scenario00"
        scenario_data = scenario_data_factory(nickname=scenario_name, is_present=False)
        mock_get_scenario_indicators.return_value = scenario_indicators()
        scenario_indicators = mock_get_scenario_indicators.return_value
        scenario_indicators._bike_shift_scenarios = []
        scenario_indicators._public_transport_shift_scenarios = []
        scenario_indicators._remote_scenarios = []
        employees_plan = ImmutableDict({employee: Plan.NO_CHANGE})
        result = gpkg_study._construct_employees_properties(
            employee,
            site,
            commute_data,
            scenario_data,
            scenario_indicators,
            employees_plan,
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "employee": "employee_01",
            "site": "site_01",
            "mode": "CAR",
            "mode_declared": "WALK",
            "duration_walk_Scenario00": None,
            "duration_bicycle_Scenario00": None,
            "duration_car_Scenario00": 800,
            "duration_public_transport_Scenario00": None,
            "duration_carpooling_Scenario00": None,
            "duration_electric_bicycle_Scenario00": None,
            "duration_electric_car_Scenario00": None,
            "duration_motorcycle_Scenario00": None,
            "duration_airplane_Scenario00": None,
            "duration_electric_motorcycle_Scenario00": None,
            "duration_fast_bicycle_Scenario00": None,
            "duration_car_public_transport_Scenario00": None,
            "duration_bicycle_public_transport_Scenario00": None,
            "distance_walk_Scenario00": None,
            "distance_bicycle_Scenario00": None,
            "distance_car_Scenario00": 2000,
            "distance_public_transport_Scenario00": None,
            "distance_carpooling_Scenario00": None,
            "distance_electric_bicycle_Scenario00": None,
            "distance_electric_car_Scenario00": None,
            "distance_motorcycle_Scenario00": None,
            "distance_airplane_Scenario00": None,
            "distance_electric_motorcycle_Scenario00": None,
            "distance_fast_bicycle_Scenario00": None,
            "distance_car_public_transport_Scenario00": None,
            "distance_bicycle_public_transport_Scenario00": None,
            "emission_walk_Scenario00": None,
            "emission_bicycle_Scenario00": None,
            "emission_car_Scenario00": 4800,
            "emission_public_transport_Scenario00": None,
            "emission_carpooling_Scenario00": None,
            "emission_electric_bicycle_Scenario00": None,
            "emission_electric_car_Scenario00": None,
            "emission_motorcycle_Scenario00": None,
            "emission_airplane_Scenario00": None,
            "emission_electric_motorcycle_Scenario00": None,
            "emission_fast_bicycle_Scenario00": None,
            "emission_car_public_transport_Scenario00": None,
            "emission_bicycle_public_transport_Scenario00": None,
            "duration_current_mode": 800,
            "distance_current_mode": 2000,
            "emission_current_mode": 4800,
            "carpool_group_id_Scenario00": None,
            "is_carpool_driver_Scenario00": 0,
            "ideal_mode_Scenario00": Plan.NO_CHANGE,
        }

    def test_get_employee_carpooling_indicators(
        self,
        consolidated_study_factory: Any,
        geo_employee_factory: Any,
        scenario_data_factory: Any,
        new_carpool_indicators: Any,
        scenario_indicators: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employee = geo_employee_factory(
            coordinates=GeoCoordinates(10, 20),
        )
        scenario_name = "Scenario00"
        scenario_data = scenario_data_factory(nickname=scenario_name, is_present=False)
        carpool_indicators = new_carpool_indicators(
            carpool_group_id_by_employee=ImmutableDict(
                {
                    employee: 999,
                }
            ),
            drivers=[employee],
        )
        scenario_indicators = scenario_indicators()
        scenario_indicators._carpool_indicators = carpool_indicators

        result = gpkg_study._get_employee_carpooling_indicators(
            employee, scenario_data, scenario_indicators
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "carpool_group_id_Scenario00": 999,
            "is_carpool_driver_Scenario00": 1,
        }

    def test_get_modal_shift_indicators_returns_true_when_employee_has_shifted(
        self,
        consolidated_study_factory: Any,
        geo_employee_factory: Any,
        scenario_data_factory: Any,
        mode_shift_scenario_indicators: Any,
        mode_shift_specification: Any,
        new_remote_scenario_indicators: Any,
        scenario_indicators: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employee = geo_employee_factory(
            coordinates=GeoCoordinates(10, 20),
        )
        scenario_name = "Scenario00"
        scenario_data = scenario_data_factory(nickname=scenario_name, is_present=False)
        bike_shift_spec = mode_shift_specification(
            nickname="scenario_actif",
            mode_to_shift_to=TransportMode.BICYCLE,
        )
        bike_shift_indicators = mode_shift_scenario_indicators(
            spec=bike_shift_spec,
            shifter_employees=[employee],
        )
        pt_shift_spec = mode_shift_specification(
            nickname="scenario_gagnant",
            mode_to_shift_to=TransportMode.PUBLIC_TRANSPORT,
        )
        pt_shift_indicators = mode_shift_scenario_indicators(
            spec=pt_shift_spec,
            shifter_employees=[employee],
        )
        remote_shift_indicators = new_remote_scenario_indicators(
            nickname="scenario_ambitieux",
            remote_workers=[employee],
        )
        scenario_indicators = scenario_indicators()
        scenario_indicators._bike_shift_scenarios = [bike_shift_indicators]
        scenario_indicators._public_transport_shift_scenarios = [pt_shift_indicators]
        scenario_indicators._remote_scenarios = [remote_shift_indicators]

        result = gpkg_study._get_modal_shift_indicators(
            employee, scenario_data, scenario_indicators
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "bike_shift_scenario_actif_Scenario00": True,
            "pt_shift_scenario_gagnant_Scenario00": True,
            "remote_shift_scenario_ambitieux_Scenario00": True,
        }

    def test_get_modal_shift_indicators_returns_false_when_employee_has_not_shifted(
        self,
        consolidated_study_factory: Any,
        geo_employee_factory: Any,
        scenario_data_factory: Any,
        mode_shift_scenario_indicators: Any,
        mode_shift_specification: Any,
        new_remote_scenario_indicators: Any,
        scenario_indicators: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employee = geo_employee_factory(
            coordinates=GeoCoordinates(10, 20),
        )
        scenario_name = "Scenario00"
        scenario_data = scenario_data_factory(nickname=scenario_name, is_present=False)
        bike_shift_spec = mode_shift_specification(
            nickname="scenario_actif",
            mode_to_shift_to=TransportMode.BICYCLE,
        )
        bike_shift_indicators = mode_shift_scenario_indicators(
            spec=bike_shift_spec,
            shifter_employees=[],
        )
        pt_shift_spec = mode_shift_specification(
            nickname="scenario_gagnant",
            mode_to_shift_to=TransportMode.PUBLIC_TRANSPORT,
        )
        pt_shift_indicators = mode_shift_scenario_indicators(
            spec=pt_shift_spec,
            shifter_employees=[],
        )
        remote_shift_indicators = new_remote_scenario_indicators(
            nickname="scenario_ambitieux",
            remote_workers=[],
        )
        scenario_indicators = scenario_indicators()
        scenario_indicators._bike_shift_scenarios = [bike_shift_indicators]
        scenario_indicators._public_transport_shift_scenarios = [pt_shift_indicators]
        scenario_indicators._remote_scenarios = [remote_shift_indicators]

        result = gpkg_study._get_modal_shift_indicators(
            employee, scenario_data, scenario_indicators
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "bike_shift_scenario_actif_Scenario00": False,
            "pt_shift_scenario_gagnant_Scenario00": False,
            "remote_shift_scenario_ambitieux_Scenario00": False,
        }

    def test_construct_public_transport_stations_scenario(
        self,
        public_transport_stations_scenario_factory: Any,
        timed_transport_stop_commute_factory: Any,
        modal_commute_data_factory: Any,
        public_transport_stop: Any,
        consolidated_study_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        pt_stations_scenario = public_transport_stations_scenario_factory(
            frozenset(
                [
                    timed_transport_stop_commute_factory(
                        pt_stop=lambda: public_transport_stop(
                            name="PTStop1",
                            coordinates=GeoCoordinates(10, 20),
                            lines_connected=frozenset(["Line1"]),
                            lines_kinds=frozenset([TransportType.BUS]),
                        ),
                        data=lambda: modal_commute_data_factory(
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 3200,
                                }
                            ),
                        ),
                    ),
                    timed_transport_stop_commute_factory(
                        pt_stop=lambda: public_transport_stop(
                            name="PTStop2",
                            coordinates=GeoCoordinates(30, 40),
                            lines_connected=frozenset(["Line2"]),
                            lines_kinds=frozenset([TransportType.SUBWAY]),
                        ),
                        data=lambda: modal_commute_data_factory(
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 1200,
                                }
                            ),
                        ),
                    ),
                ]
            )
        )

        result = gpkg_study._construct_public_transport_stations_scenario(
            pt_stations_scenario
        )

        assert len(result) == 2
        for feature, pt_stop_commute in zip(result, pt_stations_scenario.commutes):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(
                    pt_stop_commute.destination.coordinates.longitude,
                    pt_stop_commute.destination.coordinates.latitude,
                )
            )
            if pt_stop_commute.destination.name == "PTStop1":
                assert feature["properties"] == {
                    "site": "site_01",
                    "pt_station": "PTStop1",
                    "duration_walk": 3200,
                    "distance_walk": 1800,
                    "emission_walk": 0,
                    "lines": "Line1",
                    "lines_kind": "bus",
                }
            elif pt_stop_commute.destination.name == "PTStop2":
                assert feature["properties"] == {
                    "site": "site_01",
                    "pt_station": "PTStop2",
                    "duration_walk": 1200,
                    "distance_walk": 1800,
                    "emission_walk": 0,
                    "lines": "Line2",
                    "lines_kind": "métro",
                }

    def test_construct_public_transport_stops(
        self, public_transport_stop: Any, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        pt_stops = frozenset(
            [
                public_transport_stop(
                    name="PTStop1",
                    coordinates=GeoCoordinates(10, 20),
                    lines_connected=frozenset(["Line1"]),
                    lines_kinds=frozenset([TransportType.BUS]),
                ),
                public_transport_stop(
                    name="PTStop2",
                    coordinates=GeoCoordinates(30, 40),
                    lines_connected=frozenset(["Line2"]),
                    lines_kinds=frozenset([TransportType.SUBWAY]),
                ),
            ]
        )

        result = gpkg_study._construct_public_transport_stops(pt_stops)

        assert len(result) == 2
        for feature, pt_stop in zip(result, pt_stops):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(pt_stop.coordinates.longitude, pt_stop.coordinates.latitude)
            )
            if pt_stop.name == "PTStop1":
                assert feature["properties"] == {
                    "name": "PTStop1",
                    "lines": "Line1",
                    "lines_kind": "bus",
                }
            elif pt_stop.name == "PTStop2":
                assert feature["properties"] == {
                    "name": "PTStop2",
                    "lines": "Line2",
                    "lines_kind": "métro",
                }

    def test_construct_isochrones(
        self, geo_site_factory: Any, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        isochrones = {
            geo_site_factory(nickname="Site1"): {
                TransportMode.PUBLIC_TRANSPORT: {
                    10
                    * minutes: MultiPolygon(
                        [
                            shape(
                                {
                                    "type": "Polygon",
                                    "coordinates": [
                                        [[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]
                                    ],
                                }
                            )
                        ]
                    )
                }
            }
        }

        result = gpkg_study._construct_isochrones(isochrones)

        assert result == [
            {
                "geometry": mapping(
                    MultiPolygon(
                        [
                            shape(
                                {
                                    "type": "Polygon",
                                    "coordinates": [
                                        [[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]
                                    ],
                                }
                            )
                        ]
                    )
                ),
                "properties": {
                    "site_id": "Site1",
                    "mode": "PUBLIC_TRANSPORT",
                    "duration": 10,
                },
            }
        ]

    def test_convert_polygons_to_multipolygon_if_needed(
        self, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        isochrone_features = [
            {
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]],
                },
                "properties": {
                    "site_id": "Site1",
                    "mode": "PUBLIC_TRANSPORT",
                    "duration": 10,
                },
            },
        ]

        result = gpkg_study._convert_polygons_to_multipolygon_if_needed(
            isochrone_features
        )

        assert result == [
            {
                "geometry": mapping(
                    MultiPolygon(
                        [
                            shape(
                                {
                                    "type": "Polygon",
                                    "coordinates": [
                                        [[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]
                                    ],
                                }
                            )
                        ]
                    )
                ),
                "properties": isochrone_features[0]["properties"],
            }
        ]

    def test_convert_polygons_to_multipolygon_if_needed_with_multipolygon(
        self, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        isochrone_features = [
            {
                "geometry": {
                    "type": "MultiPolygon",
                    "coordinates": [
                        [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]],
                    ],
                },
                "properties": {
                    "site_id": "Site1",
                    "mode": "PUBLIC_TRANSPORT",
                    "duration": 10,
                },
            },
        ]

        result = gpkg_study._convert_polygons_to_multipolygon_if_needed(
            isochrone_features
        )

        assert result == isochrone_features

    def test_construct_pois(
        self,
        geo_site_factory: Any,
        consolidated_study_factory: Any,
        poi_scenario: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        poi_scenarios = Scenarios(frozenset([poi_scenario]))

        result = gpkg_study._construct_pois(poi_scenarios)

        assert len(result.poi_features) == 1
        feature = result.poi_features[0]
        poi = list(poi_scenarios.compute_destinations())[0]
        site = list(poi_scenarios.compute_origins())[0]
        assert feature.coordinates == poi.coordinates
        assert feature.poi == poi.name
        assert len(feature.site_commutes) == 1
        assert feature.site_commutes[0].site == site.nickname
        assert feature.site_commutes[0].duration == {
            TransportMode.BICYCLE: 1600,
            TransportMode.CAR: 800,
            TransportMode.CARPOOLING: 900,
            TransportMode.ELECTRIC_BICYCLE: 1500,
            TransportMode.ELECTRIC_CAR: 850,
            TransportMode.MOTORCYCLE: 700,
            TransportMode.PUBLIC_TRANSPORT: 1200,
            TransportMode.WALK: 3200,
            TransportMode.AIRPLANE: 3600,
            TransportMode.ELECTRIC_MOTORCYCLE: 750,
            TransportMode.FAST_BICYCLE: 1400,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
        }
        assert feature.site_commutes[0].distance == {
            TransportMode.BICYCLE: 1800,
            TransportMode.CAR: 2000,
            TransportMode.CARPOOLING: 2100,
            TransportMode.ELECTRIC_BICYCLE: 1800,
            TransportMode.ELECTRIC_CAR: 2000,
            TransportMode.MOTORCYCLE: 2000,
            TransportMode.PUBLIC_TRANSPORT: 2100,
            TransportMode.WALK: 1800,
            TransportMode.AIRPLANE: 20000,
            TransportMode.ELECTRIC_MOTORCYCLE: 2000,
            TransportMode.FAST_BICYCLE: 2000,
            TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
        }
        assert feature.site_commutes[0].emission == {
            TransportMode.BICYCLE: 0,
            TransportMode.CAR: 4800,
            TransportMode.CARPOOLING: 2400,
            TransportMode.ELECTRIC_BICYCLE: 10,
            TransportMode.ELECTRIC_CAR: 1200,
            TransportMode.MOTORCYCLE: 4800,
            TransportMode.PUBLIC_TRANSPORT: 1000,
            TransportMode.WALK: 0,
            TransportMode.AIRPLANE: 10000,
            TransportMode.ELECTRIC_MOTORCYCLE: 15,
            TransportMode.FAST_BICYCLE: 10,
            TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
        }

    def test_construct_commute_properties_with_scenario_name(
        self,
        consolidated_study_factory: Any,
        modal_commute_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        commute_data = modal_commute_data_factory()
        scenario_name = "Scenario00"

        result = gpkg_study._construct_commute_properties(commute_data, scenario_name)

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "duration_walk_Scenario00": 3200,
            "duration_bicycle_Scenario00": 1600,
            "duration_car_Scenario00": 800,
            "duration_public_transport_Scenario00": 1200,
            "duration_carpooling_Scenario00": 900,
            "duration_electric_bicycle_Scenario00": 1500,
            "duration_electric_car_Scenario00": 850,
            "duration_motorcycle_Scenario00": 700,
            "duration_airplane_Scenario00": 3600,
            "duration_electric_motorcycle_Scenario00": 750,
            "duration_fast_bicycle_Scenario00": 1400,
            "duration_car_public_transport_Scenario00": 1000,
            "duration_bicycle_public_transport_Scenario00": 1100,
            "distance_walk_Scenario00": 1800,
            "distance_bicycle_Scenario00": 1800,
            "distance_car_Scenario00": 2000,
            "distance_public_transport_Scenario00": 2100,
            "distance_carpooling_Scenario00": 2100,
            "distance_electric_bicycle_Scenario00": 1800,
            "distance_electric_car_Scenario00": 2000,
            "distance_motorcycle_Scenario00": 2000,
            "distance_airplane_Scenario00": 20000,
            "distance_electric_motorcycle_Scenario00": 2000,
            "distance_fast_bicycle_Scenario00": 2000,
            "distance_car_public_transport_Scenario00": 2200,
            "distance_bicycle_public_transport_Scenario00": 2100,
            "emission_walk_Scenario00": 0,
            "emission_bicycle_Scenario00": 0,
            "emission_car_Scenario00": 4800,
            "emission_public_transport_Scenario00": 1000,
            "emission_carpooling_Scenario00": 2400,
            "emission_electric_bicycle_Scenario00": 10,
            "emission_electric_car_Scenario00": 1200,
            "emission_motorcycle_Scenario00": 4800,
            "emission_airplane_Scenario00": 10000,
            "emission_electric_motorcycle_Scenario00": 15,
            "emission_fast_bicycle_Scenario00": 10,
            "emission_car_public_transport_Scenario00": 1500,
            "emission_bicycle_public_transport_Scenario00": 1000,
        }

    def test_construct_commute_properties_without_scenario_name(
        self,
        consolidated_study_factory: Any,
        modal_commute_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        commute_data = modal_commute_data_factory()
        scenario_name = None

        result = gpkg_study._construct_commute_properties(commute_data, scenario_name)

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "duration_walk": 3200,
            "duration_bicycle": 1600,
            "duration_car": 800,
            "duration_public_transport": 1200,
            "duration_carpooling": 900,
            "duration_electric_bicycle": 1500,
            "duration_electric_car": 850,
            "duration_motorcycle": 700,
            "duration_airplane": 3600,
            "duration_electric_motorcycle": 750,
            "duration_fast_bicycle": 1400,
            "duration_car_public_transport": 1000,
            "duration_bicycle_public_transport": 1100,
            "distance_walk": 1800,
            "distance_bicycle": 1800,
            "distance_car": 2000,
            "distance_public_transport": 2100,
            "distance_carpooling": 2100,
            "distance_electric_bicycle": 1800,
            "distance_electric_car": 2000,
            "distance_motorcycle": 2000,
            "distance_airplane": 20000,
            "distance_electric_motorcycle": 2000,
            "distance_fast_bicycle": 2000,
            "distance_car_public_transport": 2200,
            "distance_bicycle_public_transport": 2100,
            "emission_walk": 0,
            "emission_bicycle": 0,
            "emission_car": 4800,
            "emission_public_transport": 1000,
            "emission_carpooling": 2400,
            "emission_electric_bicycle": 10,
            "emission_electric_car": 1200,
            "emission_motorcycle": 4800,
            "emission_airplane": 10000,
            "emission_electric_motorcycle": 15,
            "emission_fast_bicycle": 10,
            "emission_car_public_transport": 1500,
            "emission_bicycle_public_transport": 1000,
        }

    def test_construct_commute_properties_with_specified_mode(
        self,
        consolidated_study_factory: Any,
        modal_commute_data_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        commute_data = modal_commute_data_factory()
        scenario_name = "Scenario00"
        modes = [TransportMode.CAR]

        result = gpkg_study._construct_commute_properties(
            commute_data, scenario_name, modes
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "duration_car_Scenario00": 800,
            "distance_car_Scenario00": 2000,
            "emission_car_Scenario00": 4800,
        }

    def test_construct_bicycle_amenity_scenario(
        self,
        consolidated_study_factory: Any,
        timed_commute_data: Any,
        geo_site_factory: Any,
        bicycle_amenity: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        site = geo_site_factory(
            nickname="Site1",
            coordinates=GeoCoordinates(0, 1),
        )
        amenities_scenario = Scenario(
            data=None,
            commutes=frozenset(
                {
                    Commute(
                        origin=site,
                        destination=bicycle_amenity(name="bicycle_amenity_01"),
                        data=timed_commute_data,
                    )
                }
            ),
        )
        result = gpkg_study._construct_bicycle_amenity_scenario(amenities_scenario)

        assert len(result) == 1
        for feature, amenity in zip(
            result, list(amenities_scenario.get_destinations())
        ):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(amenity.coordinates.longitude, amenity.coordinates.latitude)
            )

    def test_construct_bicycle_amenities_properties(
        self,
        consolidated_study_factory: Any,
        bicycle_amenity: Any,
        geo_site_factory: Any,
        timed_commute_data: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        amenity = bicycle_amenity(
            name="bicycle_amenity_01", amenity_types=frozenset(["station"])
        )
        site = geo_site_factory(
            nickname="Site1",
            coordinates=GeoCoordinates(0, 1),
        )

        result = gpkg_study._construct_bicycle_amenity_properties(
            amenity, site, timed_commute_data
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "bicycle_amenity": "bicycle_amenity_01",
            "type": "station",
            "site": "Site1",
            "duration_walk": 3200,
            "distance_walk": 1800,
            "emission_walk": 0,
        }

    def test_construct_car_amenity_scenario(
        self,
        consolidated_study_factory: Any,
        timed_commute_data: Any,
        geo_site_factory: Any,
        car_amenity: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        site = geo_site_factory(
            nickname="Site1",
            coordinates=GeoCoordinates(0, 1),
        )
        amenities_scenario = Scenario(
            data=None,
            commutes=frozenset(
                {
                    Commute(
                        origin=site,
                        destination=car_amenity(name="car_amenity_01"),
                        data=timed_commute_data,
                    )
                }
            ),
        )
        result = gpkg_study._construct_car_amenity_scenario(amenities_scenario)

        assert len(result) == 1
        for feature, amenity in zip(
            result, list(amenities_scenario.get_destinations())
        ):
            assert "geometry" in feature
            assert "properties" in feature
            assert isinstance(feature["geometry"], dict)
            assert isinstance(feature["properties"], dict)
            assert feature["geometry"] == mapping(
                Point(amenity.coordinates.longitude, amenity.coordinates.latitude)
            )

    def test_construct_car_amenities_properties(
        self,
        consolidated_study_factory: Any,
        car_amenity: Any,
        geo_site_factory: Any,
        timed_commute_data: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        amenity = car_amenity(
            name="car_amenity_01", amenity_types=frozenset(["parking"])
        )
        site = geo_site_factory(
            nickname="Site1",
            coordinates=GeoCoordinates(0, 1),
        )

        result = gpkg_study._construct_car_amenity_properties(
            amenity, site, timed_commute_data
        )

        assert result is not None
        assert isinstance(result, dict)
        assert result == {
            "car_amenity": "car_amenity_01",
            "type": "parking",
            "site": "Site1",
            "duration_walk": 3200,
            "distance_walk": 1800,
            "emission_walk": 0,
        }

    @patch(
        "mobility.serializers.geopackage_serializer.GeopackageStudy._construct_bicycle_amenity_scenario"  # noqa E501
    )
    @patch(
        "mobility.serializers.geopackage_serializer.GeopackageStudy._construct_car_amenity_scenario"  # noqa E501
    )
    @patch(
        "mobility.serializers.geopackage_serializer.GeopackageStudy._construct_public_transport_stations_scenario"  # noqa E501
    )
    def test_construct_point_amenities(
        self,
        pt_mock: Any,
        car_mock: Any,
        bike_mock: Any,
        consolidated_study_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        bicycle_amenities_features = [
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [0, 0],
                },
                "properties": {
                    "bicycle_amenity": "velib",
                    "site": "LEssor",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                },
            }
        ]
        car_amenities_features = [
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [1, 0],
                },
                "properties": {
                    "car_amenity": "parking",
                    "site": "LEssor",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                },
            }
        ]
        public_transport_stations_features = [
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [2, 0],
                },
                "properties": {
                    "site": "LEssor",
                    "pt_station": "Gare Matabiau",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                    "lines": "Line1",
                    "lines_kind": "bus",
                },
            }
        ]
        bike_mock.return_value = bicycle_amenities_features
        car_mock.return_value = car_amenities_features
        pt_mock.return_value = public_transport_stations_features

        result = gpkg_study._construct_point_amenities()
        expected_result = [
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [0, 0],
                },
                "properties": {
                    "bicycle_amenity": "velib",
                    "site": "LEssor",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                    "car_amenity": None,
                    "pt_station": None,
                    "lines": None,
                    "lines_kind": None,
                },
            },
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [1, 0],
                },
                "properties": {
                    "bicycle_amenity": None,
                    "site": "LEssor",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                    "car_amenity": "parking",
                    "pt_station": None,
                    "lines": None,
                    "lines_kind": None,
                },
            },
            {
                "geometry": {
                    "type": "Point",
                    "coordinates": [2, 0],
                },
                "properties": {
                    "bicycle_amenity": None,
                    "site": "LEssor",
                    "duration_walk": 1149,
                    "distance_walk": 1245,
                    "emission_walk": 0,
                    "car_amenity": None,
                    "pt_station": "Gare Matabiau",
                    "lines": "Line1",
                    "lines_kind": "bus",
                },
            },
        ]

        assert result == expected_result

    def test_construct_bicycle_lines(
        self,
        consolidated_study_factory: Any,
        bicycle_line: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        lines = frozenset(
            {
                bicycle_line(name="bicycle_line_01"),
            }
        )

        result = gpkg_study._construct_bicycle_lines(lines)

        assert result is not None
        assert result == [
            {
                "geometry": mapping(
                    shape(
                        {
                            "type": "MultiLineString",
                            "coordinates": [[(1.0, 1.0), (3.0, 2.0)]],
                        }
                    )
                ),
                "properties": {
                    "bicycle_line": "bicycle_line_01",
                    "is_lane": False,
                },
            }
        ]

    def test_construct_public_transport_lines(
        self,
        consolidated_study_factory: Any,
        public_transport_line: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        lines = frozenset(
            {
                public_transport_line(name="pt_line_01"),
            }
        )

        result = gpkg_study._construct_public_transport_lines(lines)

        assert result is not None
        assert result == [
            {
                "geometry": mapping(
                    shape(
                        {
                            "type": "MultiLineString",
                            "coordinates": [[(2.0, 1.0), (3.0, 2.0)]],
                        }
                    )
                ),
                "properties": {
                    "pt_line": "pt_line_01",
                    "short_name": "ligne cars",
                    "operator": "Sytralou",
                    "kind": "bus",
                    "colour": "#132456",
                },
            }
        ]

    def test_construct_line_amenities(
        self,
        consolidated_study_factory: Any,
        bicycle_line: Any,
        public_transport_line: Any,
        timed_infrastructure: Any,
        temp_dir: str,
    ) -> None:
        bicycle_lines = frozenset(
            {
                bicycle_line(name="bicycle_line_01"),
            }
        )
        public_transport_lines = frozenset(
            {
                public_transport_line(name="pt_line_01"),
            }
        )
        infrastructure = timed_infrastructure(
            bicycle_lines=bicycle_lines,
            public_transport_lines=public_transport_lines,
        )
        study = consolidated_study_factory(infrastructure=infrastructure)
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)

        result = gpkg_study._construct_line_amenities()

        assert result is not None
        assert result == [
            {
                "geometry": mapping(
                    shape(
                        {
                            "type": "MultiLineString",
                            "coordinates": [[(1.0, 1.0), (3.0, 2.0)]],
                        }
                    )
                ),
                "properties": {
                    "bicycle_line": "bicycle_line_01",
                    "pt_line": None,
                    "is_lane": False,
                    "short_name": None,
                    "operator": None,
                    "kind": None,
                    "colour": None,
                },
            },
            {
                "geometry": mapping(
                    shape(
                        {
                            "type": "MultiLineString",
                            "coordinates": [[(2.0, 1.0), (3.0, 2.0)]],
                        }
                    )
                ),
                "properties": {
                    "pt_line": "pt_line_01",
                    "bicycle_line": None,
                    "is_lane": None,
                    "short_name": "ligne cars",
                    "operator": "Sytralou",
                    "kind": "bus",
                    "colour": "#132456",
                },
            },
        ]

    @patch(
        "mobility.serializers.geopackage_serializer.TerritoryDatabase.get_postcode_coordinates"  # noqa E501
    )
    def test_construct_postcode_modal_share_features(
        self,
        mock_postcodes_coordinates: Any,
        consolidated_study_factory: Any,
        geo_employee_factory: Any,
        address_factory: Any,
        temp_dir: str,
        postcode_modal_share_feature: Any,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        present_scenario = Mock()
        present_scenario.compute_employees_by_postcode_and_mode.return_value = {
            "31000": {
                TransportMode.BICYCLE: [
                    geo_employee_factory(nickname=i) for i in range(5)
                ],
                TransportMode.PUBLIC_TRANSPORT: [
                    geo_employee_factory(nickname=i + 10) for i in range(5)
                ],
                TransportMode.WALK: [
                    geo_employee_factory(nickname=i + 20) for i in range(10)
                ],
            }
        }
        mock_postcodes_coordinates.return_value = GeoCoordinates(1, 0)

        result = gpkg_study._construct_postcode_modal_share_features(
            present_scenario, []
        )

        expected_features = [
            postcode_modal_share_feature(
                postcode="31000",
                coordinates=GeoCoordinates(1, 0),
                current_modal_share={
                    TransportMode.BICYCLE: 5,
                    TransportMode.PUBLIC_TRANSPORT: 5,
                    TransportMode.WALK: 10,
                },
            )
        ]
        assert result == PostCodeModalShareFeatureLayer(
            features=tuple(expected_features)
        )

    @patch(
        "mobility.serializers.geopackage_serializer.TerritoryDatabase.get_postcode_coordinates"  # noqa E501
    )
    def test_construct_postcode_modal_share_features_no_postal_code(
        self,
        mock_postcodes_coordinates: Any,
        consolidated_study_factory: Any,
        geo_employee_factory: Any,
        geo_site_factory: Any,
        address_factory: Any,
        modal_commute_data_factory: Any,
        scenario_data_factory: Any,
        temp_dir: str,
        postcode_modal_share_feature: Any,
        caplog: Any,
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        mock_postcodes_coordinates.return_value = None
        present_scenario = Mock()
        present_scenario.compute_employees_by_postcode_and_mode.return_value = {
            "31000": {
                TransportMode.BICYCLE: [
                    geo_employee_factory(
                        coordinates=GeoCoordinates(1.0, 1.0), nickname=i
                    )
                    for i in range(5)
                ],
                TransportMode.PUBLIC_TRANSPORT: [
                    geo_employee_factory(
                        coordinates=GeoCoordinates(-1.0, -1.0), nickname=i + 10
                    )
                    for i in range(5)
                ],
                TransportMode.WALK: [
                    geo_employee_factory(
                        coordinates=GeoCoordinates(2.0, 1.0), nickname=i + 20
                    )
                    for i in range(10)
                ],
            }
        }

        result = gpkg_study._construct_postcode_modal_share_features(
            present_scenario, []
        )

        expected_features = [
            postcode_modal_share_feature(
                postcode="31000",
                coordinates=GeoCoordinates(1.0, 0.5),
                current_modal_share={
                    TransportMode.BICYCLE: 5,
                    TransportMode.PUBLIC_TRANSPORT: 5,
                    TransportMode.WALK: 10,
                },
            )
        ]
        assert result == PostCodeModalShareFeatureLayer(
            features=tuple(expected_features)
        )
        assert "Could not find coordinates for postal code 31000" in caplog.text

    def test_compute_employee_centroid(
        self, consolidated_study_factory: Any, geo_employee_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        gpkg_study = GeopackageStudy(study, Indicators(study), temp_dir)
        employees = [
            geo_employee_factory(
                coordinates=GeoCoordinates(0, 1),
            ),
            geo_employee_factory(
                coordinates=GeoCoordinates(1, 2),
            ),
        ]

        result = gpkg_study._compute_employee_centroid(employees)

        assert result == GeoCoordinates(0.5, 1.5)


class TestStudyGeopackageSerializer:
    @patch("mobility.ir.study.ConsolidatedStudy.get_prefix")
    def test_init(
        self, mock_get_prefix: Any, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        mock_get_prefix.return_value = "Diagnostic_mobilité"

        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        expected_dst = Path("dir") / "Diagnostic_mobilité_Modelity_time.gpkg"

        assert str(serializer.dst) == str(expected_dst)

    @patch("mobility.serializers.geopackage_serializer.make_dir_for")
    @patch(
        "mobility.serializers.geopackage_serializer.StudyGeopackageSerializer._write_geopackage"  # noqa E501
    )
    def test_serialize(
        self,
        mock_write: Any,
        mock_make_dir: Any,
        consolidated_study_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        serializer.gpkg_study = Mock()
        serializer.gpkg_study.sites = ["site1", "site2"]
        serializer.gpkg_study.employees = ["commute1", "commute2"]
        serializer.gpkg_study.isochrones = ["isochrone1", "isochrone2"]
        serializer.gpkg_study.pt_stops = ["pt_stop1", "pt_stop2"]
        serializer.gpkg_study.pt_stations_scenario = ["pt_station1", "pt_station2"]
        serializer.gpkg_study.poi_scenarios = ["poi_scenario1", "poi_scenario2"]

        serializer.serialize()

        calls = [
            call("2_sites", ["site1", "site2"]),
            call("1_employees", ["commute1", "commute2"]),
            call("9_isochrones", ["isochrone1", "isochrone2"]),
            call("4_pt_stops", ["pt_stop1", "pt_stop2"]),
            call("5_pt_stations_scenario", ["pt_station1", "pt_station2"]),
            call("3_poi_scenarios", ["poi_scenario1", "poi_scenario2"]),
        ]
        mock_write.assert_has_calls(calls, any_order=True)

    @patch("mobility.serializers.geopackage_serializer.fiona.open")
    @patch("mobility.serializers.geopackage_serializer.logging")
    @patch("mobility.serializers.geopackage_serializer.make_dir_for")
    @patch(
        "mobility.serializers.geopackage_serializer.StudyGeopackageSerializer._write_geopackage"  # noqa E501
    )
    def test_serialize_no_features(
        self,
        mock_write: Any,
        mock_make_dir: Any,
        mock_logging: Any,
        mock_fiona_open: Any,
        consolidated_study_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        serializer.gpkg_study = Mock()
        serializer.gpkg_study.sites = []
        serializer.gpkg_study.employees = []
        serializer.gpkg_study.isochrones = []
        serializer.gpkg_study.pt_stops = []
        serializer.gpkg_study.pt_stations_scenario = []
        serializer.gpkg_study.poi_scenarios = []
        mock_write.side_effect = _side_effect

        serializer.serialize()

        mock_make_dir.assert_called()
        calls = [
            call("2_sites", []),
            call("1_employees", []),
            call("9_isochrones", []),
            call("4_pt_stops", []),
            call("5_pt_stations_scenario", []),
            call("3_poi_scenarios", []),
        ]
        mock_write.assert_has_calls(calls, any_order=True)
        warnings = [
            call("Could not write layer 2_sites: No features found for layer 2_sites."),
            call(
                (
                    "Could not write layer 1_employees: "
                    "No features found for layer 1_employees."
                )
            ),
            call(
                (
                    "Could not write layer 9_isochrones: "
                    "No features found for layer 9_isochrones."
                )
            ),
            call(
                "Could not write layer 4_pt_stops: No features found for layer 4_pt_stops."
            ),
            call(
                (
                    "Could not write layer 5_pt_stations_scenario: "
                    "No features found for layer 5_pt_stations_scenario."
                )
            ),
            call(
                (
                    "Could not write layer 3_poi_scenarios: "
                    "No features found for layer 3_poi_scenarios."
                )
            ),
        ]
        mock_logging.warning.assert_has_calls(warnings, any_order=True)
        mock_fiona_open.assert_not_called()

    @patch("mobility.serializers.geopackage_serializer.fiona.open")
    @patch("mobility.serializers.geopackage_serializer.logging")
    @patch("mobility.serializers.geopackage_serializer.make_dir_for")
    @patch(
        "mobility.serializers.geopackage_serializer"
        ".StudyGeopackageSerializer._write_geopackage"
    )
    def test_serialize_with_and_without_features(
        self,
        mock_write: Any,
        mock_make_dir: Any,
        mock_logging: Any,
        mock_fiona_open: Any,
        consolidated_study_factory: Any,
        temp_dir: str,
    ) -> None:
        study = consolidated_study_factory()
        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        serializer.gpkg_study = Mock()
        serializer.gpkg_study.sites = ["site1", "site2"]
        serializer.gpkg_study.commutes = []
        serializer.gpkg_study.isochrones = ["isochrone1", "isochrone2"]
        serializer.gpkg_study.pt_stops = []
        serializer.gpkg_study.pt_stations_scenario = []
        serializer.gpkg_study.poi_scenarios = []
        mock_write.side_effect = _side_effect

        serializer.serialize()

        mock_make_dir.assert_called()
        calls = [
            call("2_sites", ["site1", "site2"]),
            call("9_isochrones", ["isochrone1", "isochrone2"]),
        ]
        mock_write.assert_has_calls(calls, any_order=True)
        warnings = [
            call(
                (
                    "Could not write layer 1_employees: "
                    "No features found for layer 1_employees."
                )
            ),
            call(
                (
                    "Could not write layer 4_pt_stops: "
                    "No features found for layer 4_pt_stops."
                )
            ),
            call(
                (
                    "Could not write layer 5_pt_stations_scenario: "
                    "No features found for layer 5_pt_stations_scenario."
                )
            ),
            call(
                (
                    "Could not write layer 3_poi_scenarios: "
                    "No features found for layer 3_poi_scenarios."
                )
            ),
        ]
        mock_logging.warning.assert_has_calls(warnings, any_order=True)

    @patch("mobility.serializers.geopackage_serializer.fiona.open")
    def test_write_geopackage(
        self, mock_open: Any, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        features = [
            {
                "geometry": {"type": "Point", "coordinates": (10, 20)},
                "properties": {"prop": "value"},
            }
        ]
        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        serializer._write_geopackage("layer", features)
        mock_open.assert_called_with(
            serializer.dst,
            "w",
            layer="layer",
            driver="GPKG",
            crs=CRS.from_epsg(4326),
            schema={
                "geometry": "Point",
                "properties": {"prop": "str"},
            },
        )

    def test_write_geopackage_no_features(
        self, consolidated_study_factory: Any, temp_dir: str
    ) -> None:
        study = consolidated_study_factory()
        serializer = StudyGeopackageSerializer("dir", study, "time", temp_dir, None)
        with pytest.raises(ValueError):
            serializer._write_geopackage("layer", [])

    def test_copies_template_before_running(
        self, consolidated_study_factory: Any, tmp_path: Any
    ) -> None:
        study = consolidated_study_factory()
        template = tmp_path / "template.gpkg"
        template.write_text("coucou")

        StudyGeopackageSerializer(
            str(tmp_path / "dir"), study, "time", str(tmp_path), template
        )

        assert os.path.exists(
            tmp_path / "dir" / "Diagnostic_mobilité_Modelity_time.gpkg"
        )

    def test_starts_fresh_if_none_template(
        self, consolidated_study_factory: Any, tmp_path: Any
    ) -> None:
        study = consolidated_study_factory()

        StudyGeopackageSerializer(
            str(tmp_path / "dir"), study, "time", str(tmp_path), None
        )

        assert not os.path.exists(
            tmp_path / "dir" / "Diagnostic_mobilité_Modelity_time.gpkg"
        )

    def test_should_warn_if_template_file_does_not_exist(
        self, consolidated_study_factory: Any, tmp_path: Any, caplog: Any
    ) -> None:
        study = consolidated_study_factory()
        not_a_file = str(tmp_path / "non_existing_file.txt")

        StudyGeopackageSerializer(
            str(tmp_path / "dir"), study, "time", str(tmp_path), not_a_file
        )

        assert not os.path.exists(
            tmp_path / "dir" / "Diagnostic_mobilité_Modelity_time.gpkg"
        )
        expected_warning = (
            "root",
            logging.WARNING,
            f"Could not find the geopackage template file {not_a_file}.\n"
            "The produced geopackage will only contain study data.",
        )
        assert expected_warning in caplog.record_tuples


class TestPOIFeatureLayer:
    def test_should_make_schema_based_on_encapsulated_features(self) -> None:
        layer = POIFeatureLayer(tuple())

        schema = layer.get_schema()

        assert schema == {
            "geometry": "Point",
            "properties": {
                "distance_BICYCLE": "int",
                "distance_CAR": "int",
                "distance_CARPOOLING": "int",
                "distance_ELECTRIC_BICYCLE": "int",
                "distance_ELECTRIC_CAR": "int",
                "distance_MOTORCYCLE": "int",
                "distance_AIRPLANE": "int",
                "distance_PUBLIC_TRANSPORT": "int",
                "distance_WALK": "int",
                "distance_ELECTRIC_MOTORCYCLE": "int",
                "distance_FAST_BICYCLE": "int",
                "distance_CAR_PUBLIC_TRANSPORT": "int",
                "distance_BICYCLE_PUBLIC_TRANSPORT": "int",
                "duration_BICYCLE": "int",
                "duration_CAR": "int",
                "duration_CARPOOLING": "int",
                "duration_ELECTRIC_BICYCLE": "int",
                "duration_ELECTRIC_CAR": "int",
                "duration_MOTORCYCLE": "int",
                "duration_AIRPLANE": "int",
                "duration_PUBLIC_TRANSPORT": "int",
                "duration_WALK": "int",
                "duration_ELECTRIC_MOTORCYCLE": "int",
                "duration_FAST_BICYCLE": "int",
                "duration_CAR_PUBLIC_TRANSPORT": "int",
                "duration_BICYCLE_PUBLIC_TRANSPORT": "int",
                "emission_BICYCLE": "int",
                "emission_CAR": "int",
                "emission_CARPOOLING": "int",
                "emission_ELECTRIC_BICYCLE": "int",
                "emission_ELECTRIC_CAR": "int",
                "emission_MOTORCYCLE": "int",
                "emission_AIRPLANE": "int",
                "emission_PUBLIC_TRANSPORT": "int",
                "emission_WALK": "int",
                "emission_ELECTRIC_MOTORCYCLE": "int",
                "emission_FAST_BICYCLE": "int",
                "emission_CAR_PUBLIC_TRANSPORT": "int",
                "emission_BICYCLE_PUBLIC_TRANSPORT": "int",
                "name": "str",
                "site": "str",
            },
        }

    def test_should_make_empty_list(self) -> None:
        layer = POIFeatureLayer(tuple())

        features = layer.to_dict()

        assert features == []

    def test_should_make_list_of_one_feature_as_dict(self, poi_feature: Any) -> None:
        features = (poi_feature(),)
        layer = POIFeatureLayer(features)

        dictfeatures = layer.to_dict()

        assert dictfeatures == [
            {
                "geometry": {
                    "coordinates": (
                        4.5,
                        45.2,
                    ),
                    "type": "Point",
                },
                "properties": {
                    "name": "fake poi",
                    "site": "fake_site",
                    "distance_BICYCLE": 1100,
                    "distance_CAR": 1200,
                    "distance_CARPOOLING": -1,
                    "distance_ELECTRIC_BICYCLE": -1,
                    "distance_ELECTRIC_CAR": -1,
                    "distance_MOTORCYCLE": -1,
                    "distance_AIRPLANE": -1,
                    "distance_PUBLIC_TRANSPORT": 2000,
                    "distance_WALK": 1000,
                    "distance_ELECTRIC_MOTORCYCLE": -1,
                    "distance_FAST_BICYCLE": -1,
                    "distance_CAR_PUBLIC_TRANSPORT": -1,
                    "distance_BICYCLE_PUBLIC_TRANSPORT": -1,
                    "duration_BICYCLE": 2200,
                    "duration_CAR": 2400,
                    "duration_CARPOOLING": -1,
                    "duration_ELECTRIC_BICYCLE": -1,
                    "duration_ELECTRIC_CAR": -1,
                    "duration_MOTORCYCLE": -1,
                    "duration_AIRPLANE": -1,
                    "duration_PUBLIC_TRANSPORT": 4000,
                    "duration_WALK": 2000,
                    "duration_ELECTRIC_MOTORCYCLE": -1,
                    "duration_FAST_BICYCLE": -1,
                    "duration_CAR_PUBLIC_TRANSPORT": -1,
                    "duration_BICYCLE_PUBLIC_TRANSPORT": -1,
                    "emission_BICYCLE": 0,
                    "emission_CAR": 3200,
                    "emission_CARPOOLING": -1,
                    "emission_ELECTRIC_BICYCLE": -1,
                    "emission_ELECTRIC_CAR": -1,
                    "emission_MOTORCYCLE": -1,
                    "emission_AIRPLANE": -1,
                    "emission_PUBLIC_TRANSPORT": 1500,
                    "emission_WALK": 0,
                    "emission_ELECTRIC_MOTORCYCLE": -1,
                    "emission_FAST_BICYCLE": -1,
                    "emission_CAR_PUBLIC_TRANSPORT": -1,
                    "emission_BICYCLE_PUBLIC_TRANSPORT": -1,
                },
            },
        ]


class TestPostCodeModalShareFeatureLayer:
    def test_should_return_schema_from_empty_layer(self) -> None:
        layer = PostCodeModalShareFeatureLayer(tuple())

        schema = layer.get_schema()

        assert schema == {
            "geometry": "Point",
            "properties": {
                "postcode": "str",
                "modal_share_BICYCLE": "int",
                "modal_share_CAR": "int",
                "modal_share_CARPOOLING": "int",
                "modal_share_ELECTRIC_BICYCLE": "int",
                "modal_share_ELECTRIC_CAR": "int",
                "modal_share_MOTORCYCLE": "int",
                "modal_share_AIRPLANE": "int",
                "modal_share_PUBLIC_TRANSPORT": "int",
                "modal_share_WALK": "int",
                "modal_share_ELECTRIC_MOTORCYCLE": "int",
                "modal_share_FAST_BICYCLE": "int",
                "modal_share_CAR_PUBLIC_TRANSPORT": "int",
                "modal_share_BICYCLE_PUBLIC_TRANSPORT": "int",
            },
        }

    def test_should_make_empty_list(self) -> None:
        layer = PostCodeModalShareFeatureLayer(tuple())

        features = layer.to_dict()

        assert features == []

    def test_should_make_list_of_one_feature_as_dict(
        self, postcode_modal_share_feature: Any
    ) -> None:
        features = (postcode_modal_share_feature(),)
        layer = PostCodeModalShareFeatureLayer(features)

        dictfeatures = layer.to_dict()

        assert dictfeatures == [
            {
                "geometry": {
                    "coordinates": (
                        4.5,
                        45.2,
                    ),
                    "type": "Point",
                },
                "properties": {
                    "modal_share_BICYCLE": 15,
                    "modal_share_CAR": 120,
                    "modal_share_CARPOOLING": 0,
                    "modal_share_ELECTRIC_BICYCLE": 0,
                    "modal_share_ELECTRIC_CAR": 0,
                    "modal_share_MOTORCYCLE": 0,
                    "modal_share_AIRPLANE": 0,
                    "modal_share_PUBLIC_TRANSPORT": 40,
                    "modal_share_WALK": 3,
                    "modal_share_ELECTRIC_MOTORCYCLE": 0,
                    "modal_share_FAST_BICYCLE": 0,
                    "modal_share_CAR_PUBLIC_TRANSPORT": 0,
                    "modal_share_BICYCLE_PUBLIC_TRANSPORT": 0,
                    "postcode": "00000",
                },
            },
        ]

    def test_should_make_list_of_one_feature_with_many_scenarios(
        self, postcode_modal_share_feature: Any, modal_share_full: Any
    ) -> None:
        feature = postcode_modal_share_feature(
            scenario_modal_share={
                "Scenario 1": modal_share_full,
                "Other scenario": modal_share_full,
            }
        )
        layer = PostCodeModalShareFeatureLayer((feature,))

        dictfeatures = layer.to_dict()

        assert dictfeatures == [
            {
                "geometry": {
                    "coordinates": (
                        4.5,
                        45.2,
                    ),
                    "type": "Point",
                },
                "properties": {
                    "postcode": "00000",
                    "modal_share_BICYCLE": 15,
                    "modal_share_BICYCLE_Other scenario": 15,
                    "modal_share_BICYCLE_Scenario 1": 15,
                    "modal_share_CAR": 120,
                    "modal_share_CAR_Other scenario": 120,
                    "modal_share_CAR_Scenario 1": 120,
                    "modal_share_CARPOOLING": 0,
                    "modal_share_CARPOOLING_Other scenario": 0,
                    "modal_share_CARPOOLING_Scenario 1": 0,
                    "modal_share_ELECTRIC_BICYCLE": 0,
                    "modal_share_ELECTRIC_BICYCLE_Other scenario": 0,
                    "modal_share_ELECTRIC_BICYCLE_Scenario 1": 0,
                    "modal_share_ELECTRIC_CAR": 0,
                    "modal_share_ELECTRIC_CAR_Other scenario": 0,
                    "modal_share_ELECTRIC_CAR_Scenario 1": 0,
                    "modal_share_MOTORCYCLE": 0,
                    "modal_share_MOTORCYCLE_Other scenario": 0,
                    "modal_share_MOTORCYCLE_Scenario 1": 0,
                    "modal_share_AIRPLANE": 0,
                    "modal_share_AIRPLANE_Other scenario": 0,
                    "modal_share_AIRPLANE_Scenario 1": 0,
                    "modal_share_PUBLIC_TRANSPORT": 40,
                    "modal_share_PUBLIC_TRANSPORT_Other scenario": 40,
                    "modal_share_PUBLIC_TRANSPORT_Scenario 1": 40,
                    "modal_share_WALK": 3,
                    "modal_share_WALK_Other scenario": 3,
                    "modal_share_WALK_Scenario 1": 3,
                    "modal_share_ELECTRIC_MOTORCYCLE": 0,
                    "modal_share_ELECTRIC_MOTORCYCLE_Other scenario": 0,
                    "modal_share_ELECTRIC_MOTORCYCLE_Scenario 1": 0,
                    "modal_share_FAST_BICYCLE": 0,
                    "modal_share_FAST_BICYCLE_Other scenario": 0,
                    "modal_share_FAST_BICYCLE_Scenario 1": 0,
                    "modal_share_CAR_PUBLIC_TRANSPORT": 0,
                    "modal_share_CAR_PUBLIC_TRANSPORT_Other scenario": 0,
                    "modal_share_CAR_PUBLIC_TRANSPORT_Scenario 1": 0,
                    "modal_share_BICYCLE_PUBLIC_TRANSPORT": 0,
                    "modal_share_BICYCLE_PUBLIC_TRANSPORT_Other scenario": 0,
                    "modal_share_BICYCLE_PUBLIC_TRANSPORT_Scenario 1": 0,
                },
            },
        ]

    def test_should_make_schema_with_many_scenarios(
        self, postcode_modal_share_feature: Any, modal_share_full: Any
    ) -> None:
        feature = postcode_modal_share_feature(
            scenario_modal_share={
                "Scenario 1": modal_share_full,
                "Other scenario": modal_share_full,
            }
        )
        layer = PostCodeModalShareFeatureLayer((feature,))

        schema = layer.get_schema()

        expected_schema_properties = {
            "postcode": "str",
        }
        for mode in TransportMode:
            for scenario in ["", "_Scenario 1", "_Other scenario"]:
                expected_schema_properties[f"modal_share_{mode}{scenario}"] = "int"
        assert schema == {
            "geometry": "Point",
            "properties": expected_schema_properties,
        }

{"data_mtime": 1752154445, "dep_lines": [8, 9, 11, 13, 14, 15, 16, 17, 18, 12, 1, 2, 3, 4, 5, 6, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "mobility.builders.json_builder", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.study_types", "mobility.serializers.json_serializer", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "mobility.funky", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "json", "logging", "datetime", "typing", "mobility", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "configparser", "enum", "functools", "io", "json.decoder", "mobility.builders", "mobility.ir", "mobility.ir.crit_air", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.transport", "mobility.quantity", "mobility.serializers", "mobility.workers", "os", "types", "typing_extensions"], "hash": "d98282b7ae4e26075899a764c1ed904ad4e37de7", "id": "scripts.update_employee_address", "ignore_all": false, "interface_hash": "26bd7fb463102d58b76104b3bd60b6d4c6037fdc", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\update_employee_address.py", "plugin_data": null, "size": 5164, "suppressed": [], "version_id": "1.16.1"}
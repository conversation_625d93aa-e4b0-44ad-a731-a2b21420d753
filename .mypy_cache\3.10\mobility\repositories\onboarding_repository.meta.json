{"data_mtime": 1751444476, "dep_lines": [12, 6, 7, 8, 11, 13, 14, 10, 1, 2, 4, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.geocode_api", "mobility.ir.company_potential", "mobility.ir.onboarding_form_step", "mobility.repositories.abstract_repositories", "mobility.funky", "json", "typing", "flask", "mobility", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "configparser", "enum", "io", "json.decoder", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.zfe", "os", "typing_extensions"], "hash": "f502c314271e9ea762e8894961ab76a40177cfb3", "id": "mobility.repositories.onboarding_repository", "ignore_all": false, "interface_hash": "13ee91029b6d5164a40948f64fabc4ff986373be", "mtime": 1722327417, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\onboarding_repository.py", "plugin_data": null, "size": 2739, "suppressed": [], "version_id": "1.16.1"}
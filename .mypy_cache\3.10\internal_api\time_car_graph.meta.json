{"data_mtime": 1751444474, "dep_lines": [9, 10, 11, 13, 14, 16, 15, 1, 2, 3, 4, 5, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.google.api", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "datetime", "json", "random", "typing", "mobility", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.google", "configparser", "enum", "functools", "io", "json.decoder", "json.encoder", "mobility.ir", "mobility.workers", "os", "typing_extensions"], "hash": "c14d53d1be3cc1d3636202cbd92675a55171bb02", "id": "internal_api.time_car_graph", "ignore_all": false, "interface_hash": "7e44d0d9e5726cacfd9896e9e4eba4fe8121b250", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\time_car_graph.py", "plugin_data": null, "size": 5976, "suppressed": ["networkx"], "version_id": "1.16.1"}
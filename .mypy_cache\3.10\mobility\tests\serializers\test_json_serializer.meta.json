{"data_mtime": 1752154427, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 16, 17, 19, 4, 18, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.serializers.json_serializer", "mobility.funky", "mobility.quantity", "pathlib", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "os", "typing_extensions"], "hash": "9ea02a98da0f03ca6726751e119202cc60a7a422", "id": "mobility.tests.serializers.test_json_serializer", "ignore_all": false, "interface_hash": "68eb79fe1de13ea3d1e64d47d3d930944263b2ac", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\serializers\\test_json_serializer.py", "plugin_data": null, "size": 21062, "suppressed": [], "version_id": "1.16.1"}
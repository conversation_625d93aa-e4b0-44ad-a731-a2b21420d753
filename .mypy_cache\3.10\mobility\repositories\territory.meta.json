{"data_mtime": 1751444409, "dep_lines": [6, 8, 9, 7, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.territory_builder", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.funky", "os", "subprocess", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "io", "mobility.builders", "mobility.builders.exceptions", "mobility.ir", "mobility.ir.bounding_box", "typing_extensions"], "hash": "5ac7d9e330341cccd029c5a8b23e9988f1b6a4ab", "id": "mobility.repositories.territory", "ignore_all": false, "interface_hash": "9d1ccef28d004c9c171fac3e590f4b7a4369fa49", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\territory.py", "plugin_data": null, "size": 4562, "suppressed": [], "version_id": "1.16.1"}
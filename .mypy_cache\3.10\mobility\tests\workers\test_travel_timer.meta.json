{"data_mtime": 1751444425, "dep_lines": [5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.trivial.trivial_travel_time", "mobility.ir.commute_data", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.poi", "mobility.ir.study_types", "mobility.ir.transport", "mobility.workers.travel_timer", "mobility.funky", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.trivial", "enum", "mobility.ir", "mobility.ir.employee", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.workers", "typing_extensions"], "hash": "7b72d7ae1373a9a540bbf1bcc97258f7e6adffc2", "id": "mobility.tests.workers.test_travel_timer", "ignore_all": false, "interface_hash": "b50bf9154ca11f1cbbc4aa5a54cfbbbbfe7cad38", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_travel_timer.py", "plugin_data": null, "size": 73988, "suppressed": [], "version_id": "1.16.1"}
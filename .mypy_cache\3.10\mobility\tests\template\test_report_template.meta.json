{"data_mtime": 1751444429, "dep_lines": [7, 8, 9, 2, 6, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.scenario_data", "mobility.ir.study_types", "mobility.ir.transport", "unittest.mock", "mobility.funky", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "enum", "mobility.ir", "typing_extensions"], "hash": "34670a5af91a956cdebbd29a7a6ed68aa0a9f44e", "id": "mobility.tests.template.test_report_template", "ignore_all": false, "interface_hash": "b670301f0247d3f4df16ee0af261d1ae0df82e5b", "mtime": 1747931728, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\template\\test_report_template.py", "plugin_data": null, "size": 4607, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1752154445, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.otp.api", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.serializers.chart_writer", "mobility.serializers.plotly_map_maker", "mobility.serializers.plotly_theme", "mobility.workers.color_picker", "<PERSON><PERSON><PERSON><PERSON>", "csv", "datetime", "json", "typing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins", "_collections_abc", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "api_abstraction.otp", "enum", "io", "json.decoder", "mobility", "mobility.ir", "mobility.ir.country", "mobility.ir.map_elements", "mobility.serializers", "mobility.serializers.base_map_maker", "mobility.serializers.charters", "mobility.serializers.charters.svg_map_addons", "mobility.workers", "os", "pyproj._geod", "pyproj.geod", "types", "typing_extensions"], "hash": "ea062e684db6699ecb437802de4804cdc9bc468a", "id": "scripts.draw_isochrone_tc_vs_iris_population", "ignore_all": false, "interface_hash": "bbe66a6bdbb76446519481c121e05b0bd5fbbb89", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\draw_isochrone_tc_vs_iris_population.py", "plugin_data": null, "size": 5826, "suppressed": ["shapely.geometry", "fiona"], "version_id": "1.16.1"}
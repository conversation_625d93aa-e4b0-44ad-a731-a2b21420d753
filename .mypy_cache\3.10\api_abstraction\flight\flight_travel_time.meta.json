{"data_mtime": 1753176776, "dep_lines": [3, 4, 5, 6, 7, 8, 10, 11, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.trivial.trivial_travel_time", "mobility.builders.airport_database", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.workers.emission_computer", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.trivial", "enum", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.airport", "mobility.workers", "typing_extensions"], "hash": "1cc312c723d4faad12e63d09b337e74fe83f2499", "id": "api_abstraction.flight.flight_travel_time", "ignore_all": false, "interface_hash": "f1abf899cdfed801452144288f73e13359f82090", "mtime": 1751895952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\flight\\flight_travel_time.py", "plugin_data": null, "size": 5444, "suppressed": [], "version_id": "1.16.1"}
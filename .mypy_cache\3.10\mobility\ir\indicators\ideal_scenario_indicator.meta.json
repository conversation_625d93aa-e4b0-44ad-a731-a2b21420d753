{"data_mtime": 1751444404, "dep_lines": [6, 7, 8, 9, 10, 5, 11, 1, 2, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.employee", "mobility.ir.plan", "mobility.ir.transport", "mobility.ir.work_mode", "mobility.funky", "mobility.quantity", "dataclasses", "enum", "typing", "builtins", "_frozen_importlib", "abc", "mobility.ir.geo_study", "typing_extensions"], "hash": "8e8c6452d8eb06fecee47a8e7a384708673e858f", "id": "mobility.ir.indicators.ideal_scenario_indicator", "ignore_all": false, "interface_hash": "69cbc9753009a7169f51f568f26b4e85b6aef1a2", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\ideal_scenario_indicator.py", "plugin_data": null, "size": 1640, "suppressed": [], "version_id": "1.16.1"}
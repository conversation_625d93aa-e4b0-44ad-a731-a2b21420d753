import json
from typing import Any, <PERSON><PERSON>
from unittest import mock

import pytest

from mobility.builders.exceptions import WrongJsonFormatError
from mobility.builders.json_builder import (
    ConsolidatedJsonBuilder,
    ConsolidatedStudyConverter,
    JsonPromoter,
    JsonStudyPromoter,
    JSONVersion,
    StudyJsonBuilder,
    validate_latest_json_schema,
)
from mobility.funky import ImmutableDict
from mobility.ir.commute_data import TimedCommuteData
from mobility.ir.crit_air import CritAirCategory
from mobility.ir.geo_study import Address, GeoCoordinates
from mobility.ir.infrastructure import PublicTransportLine, PublicTransportStop
from mobility.ir.study import (
    PoiScenarios,
    PublicTransportStationsScenario,
    TimedTransportStopCommute,
)
from mobility.ir.study_types import Scenario, Scenarios
from mobility.ir.transport import TransportMode, TransportType
from mobility.quantity import seconds
from mobility.serializers.json_serializer import StudyJsonSerializer


class AnyOf:
    def __init__(self, choices: Tuple) -> None:
        self.choices = choices

    def __str__(self) -> str:
        return f"Any of {', '.join(str(c) for c in self.choices)}"

    def __repr__(self) -> str:
        return str(self)

    def __eq__(self, other: Any) -> bool:
        return other in self.choices

    def __req__(self, other: Any) -> bool:
        return self.__eq__(other)

    def __hash__(self) -> Any:
        return hash(self.choices)


class TestStudyJsonBuilder:
    @pytest.mark.parametrize(
        "input_json_version,expected_json_version",
        [
            ("v16", "v16"),
            ("14_0", "v16"),
            ("1_0", "v1"),
            ("10_2", "v12"),
            ("13_0", "v15"),
        ],
    )
    def test_normalize_json_study_version_format_should_use_proper_version_in_json_study(
        self,
        input_json_version: str,
        expected_json_version: str,
        tmp_path: Any,
    ) -> None:
        json_file_name = str(tmp_path / f"study_{input_json_version}.json")
        with open(json_file_name, "w+") as f:
            f.write(
                f'{{"version": "{input_json_version}", "company": "foo", "scenarios": []}}'
            )

        study = StudyJsonBuilder(json_file_name)

        assert study.json_study["version"] == expected_json_version

    def test_should_build_consolidated_study_from_dump(
        self, consolidated_study, tmp_path
    ) -> None:
        my_super_path = tmp_path / "dossier_quexiste_po"
        json_file_name = str(my_super_path / "Diagnostic_mobilité_Modelity_.json")
        StudyJsonSerializer(str(my_super_path), consolidated_study, "").serialize()

        study = StudyJsonBuilder(json_file_name).build_consolidated_study()

        assert study == consolidated_study

    def test_should_raise_an_error_when_json_has_not_the_right_format(
        self, tmp_path
    ) -> None:
        json_file_name = str(tmp_path / "study.json")
        with open(json_file_name, "w+") as f:
            f.write('{"version": "v2", "company": "foo", "scenarios": []}')

        with pytest.raises(WrongJsonFormatError):
            StudyJsonBuilder(json_file_name).build_consolidated_study()

    def test_should_raise_an_error_when_version_is_invalid(self, tmp_path) -> None:
        json_file_name = str(tmp_path / "study.json")
        with open(json_file_name, "w+") as f:
            f.write('{"version": "FOO", "company": "foo", "scenarios": []}')

        with pytest.raises(WrongJsonFormatError):
            StudyJsonBuilder(json_file_name).build_consolidated_study()

    def test_should_raise_an_error_when_no_version(self, tmp_path) -> None:
        json_file_name = str(tmp_path / "study.json")
        with open(json_file_name, "w+") as f:
            f.write('{"company": "foo", "scenarios": []}')

        with pytest.raises(WrongJsonFormatError):
            StudyJsonBuilder(json_file_name)

    @mock.patch("mobility.builders.json_builder.CritAirComputer")
    def test_should_parse_version_v1_json_into_timed_study(
        self,
        mock_critair_computer,
        scenario_data,
        geo_site_factory,
        timed_study_factory,
        tmp_path,
        geo_employee_factory,
        timed_commute_data_factory,
        timed_infrastructure,
    ) -> None:
        mock_critair_computer().get_random_crit_air_for_citycode.return_value = (
            CritAirCategory.CRITAIR_NON_CLASSE
        )
        json_file_name = str(tmp_path / "study.json")
        with open(json_file_name, "w+") as f:
            f.write(
                '{"version": "v1", "company": "Modelity", "scenarios": [{"data": "1", "commutes": [{"employee": "1", "site": "1", "commute_data": {"best_mode": "PUBLIC_TRANSPORT", "duration": {"BICYCLE": 1600, "CAR": 800, "PUBLIC_TRANSPORT": 1200, "WALK": 3200}}}]}], "time_failed": [], "geocode_failed_sites": [], "geocode_failed_employees": [], "geocode_failed_both": [], "employees": {"1": {"id": 1, "nickname": "employee_01", "address": "emlpoyee_address_01", "transport_mode": "WALK", "coordinates": {"latitude": 2.0, "longitude": 1.0}}}, "sites": {"1": {"id": 1, "nickname": "site_01", "address": "site_address_01", "coordinates": {"latitude": 46.0, "longitude": 5.0}}}, "scenarios_data": {"1": {"id": 1, "nickname": "trivial", "is_present": true}}}'
            )

        study = StudyJsonBuilder(json_file_name).build_timed_study()

        site_address_details = Address(
            full="site_address_01",
            normalized="",
            city="",
            postcode="",
            citycode="",
            coordinates=GeoCoordinates(latitude=46.0, longitude=5.0),
        )
        geo_site = geo_site_factory(address_details=site_address_details)
        data = timed_commute_data_factory(
            duration=ImmutableDict(
                {
                    TransportMode.BICYCLE: 1600,
                    TransportMode.CAR: 800,
                    TransportMode.PUBLIC_TRANSPORT: 1200,
                    TransportMode.WALK: 3200,
                }
            ),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
        )
        employee_address_details = Address(
            full="emlpoyee_address_01",
            normalized="",
            city="",
            postcode="",
            citycode="",
            coordinates=GeoCoordinates(latitude=2.0, longitude=1.0),
        )
        commutes = {
            (
                geo_employee_factory(
                    remote=False,
                    address_details=employee_address_details,
                    crit_air=CritAirCategory.CRITAIR_NON_CLASSE,
                ),
                geo_site,
                data,
            )
        }
        scenario = Scenario.from_commutes(scenario_data, commutes)
        assert study == timed_study_factory(
            scenarios=Scenarios(frozenset({scenario})),
            poi_scenarios=Scenarios.empty(),
            infrastructure=timed_infrastructure(),
        )

    @mock.patch("mobility.builders.json_builder.CritAirComputer")
    def test_should_parse_json_into_timed_study(
        self,
        mock_critair_computer,
        timed_infrastructure,
        poi_scenario,
        timed_study_factory,
        tmp_path,
    ) -> None:
        mock_critair_computer().get_random_crit_air_for_citycode.return_value = (
            CritAirCategory.CRITAIR_NON_CLASSE
        )
        json_file_name = str(tmp_path / "study.json")
        with open(json_file_name, "w+") as f:
            f.write(
                json.dumps(
                    {
                        "version": "v16",
                        "data": {
                            "company": "Modelity",
                            "territory": "LYON",
                            "mission_id": "YYMXXX.V",
                            "arrival_time": "(8, 30)",
                            "agency": "",
                        },
                        "scenarios": [
                            {
                                "data": "1",
                                "commutes": [
                                    {
                                        "origin": "1",
                                        "destination": "1",
                                        "commute_data": {
                                            "best_mode": "WALK",
                                            "duration": {
                                                "BICYCLE": 1600,
                                                "CAR": 800,
                                                "PUBLIC_TRANSPORT": 1200,
                                                "WALK": 3200,
                                                "CARPOOLING": 900,
                                                "ELECTRIC_BICYCLE": 1500,
                                                "ELECTRIC_CAR": 850,
                                                "MOTORCYCLE": 700,
                                                "AIRPLANE": 3600,
                                                "ELECTRIC_MOTORCYCLE": 750,
                                                "FAST_BICYCLE": 1400,
                                                "CAR_PUBLIC_TRANSPORT": 1000,
                                                "BICYCLE_PUBLIC_TRANSPORT": 1100,
                                            },
                                            "distance": {
                                                "BICYCLE": 1800,
                                                "CAR": 2000,
                                                "PUBLIC_TRANSPORT": 2100,
                                                "WALK": 1800,
                                                "CARPOOLING": 2100,
                                                "ELECTRIC_BICYCLE": 1800,
                                                "ELECTRIC_CAR": 2000,
                                                "MOTORCYCLE": 2000,
                                                "AIRPLANE": 20000,
                                                "ELECTRIC_MOTORCYCLE": 2000,
                                                "FAST_BICYCLE": 2000,
                                                "CAR_PUBLIC_TRANSPORT": 2200,
                                                "BICYCLE_PUBLIC_TRANSPORT": 2100,
                                            },
                                            "emission": {
                                                "BICYCLE": 0,
                                                "CAR": 4800,
                                                "PUBLIC_TRANSPORT": 1000,
                                                "WALK": 0,
                                                "CARPOOLING": 2400,
                                                "ELECTRIC_BICYCLE": 10,
                                                "ELECTRIC_CAR": 1200,
                                                "MOTORCYCLE": 4800,
                                                "AIRPLANE": 10000,
                                                "ELECTRIC_MOTORCYCLE": 15,
                                                "FAST_BICYCLE": 10,
                                                "CAR_PUBLIC_TRANSPORT": 1500,
                                                "BICYCLE_PUBLIC_TRANSPORT": 1000,
                                            },
                                            "alternative_arrival_time": {},
                                        },
                                    }
                                ],
                            }
                        ],
                        "poi_scenarios": [
                            {
                                "data": "1",
                                "commutes": [
                                    {
                                        "origin": "1",
                                        "destination": {
                                            "name": "Gare Part-Dieu",
                                            "coordinates": {
                                                "latitude": 45.760749,
                                                "longitude": 4.861171,
                                            },
                                        },
                                        "commute_data": {
                                            "duration": {
                                                "BICYCLE": 1600,
                                                "CAR": 800,
                                                "PUBLIC_TRANSPORT": 1200,
                                                "WALK": 3200,
                                                "CARPOOLING": 900,
                                                "ELECTRIC_BICYCLE": 1500,
                                                "ELECTRIC_CAR": 850,
                                                "MOTORCYCLE": 700,
                                                "AIRPLANE": 3600,
                                                "ELECTRIC_MOTORCYCLE": 750,
                                                "FAST_BICYCLE": 1400,
                                                "CAR_PUBLIC_TRANSPORT": 1000,
                                                "BICYCLE_PUBLIC_TRANSPORT": 1100,
                                            },
                                            "distance": {
                                                "BICYCLE": 1800,
                                                "CAR": 2000,
                                                "PUBLIC_TRANSPORT": 2100,
                                                "WALK": 1800,
                                                "CARPOOLING": 2100,
                                                "ELECTRIC_BICYCLE": 1800,
                                                "ELECTRIC_CAR": 2000,
                                                "MOTORCYCLE": 2000,
                                                "AIRPLANE": 20000,
                                                "ELECTRIC_MOTORCYCLE": 2000,
                                                "FAST_BICYCLE": 2000,
                                                "CAR_PUBLIC_TRANSPORT": 2200,
                                                "BICYCLE_PUBLIC_TRANSPORT": 2100,
                                            },
                                            "emission": {
                                                "BICYCLE": 0,
                                                "CAR": 4800,
                                                "PUBLIC_TRANSPORT": 1000,
                                                "WALK": 0,
                                                "CARPOOLING": 2400,
                                                "ELECTRIC_BICYCLE": 10,
                                                "ELECTRIC_CAR": 1200,
                                                "MOTORCYCLE": 4800,
                                                "AIRPLANE": 10000,
                                                "ELECTRIC_MOTORCYCLE": 15,
                                                "FAST_BICYCLE": 10,
                                                "CAR_PUBLIC_TRANSPORT": 1500,
                                                "BICYCLE_PUBLIC_TRANSPORT": 1000,
                                            },
                                            "alternative_arrival_time": {},
                                        },
                                    }
                                ],
                            }
                        ],
                        "public_transport_stations_scenario": {
                            "data": None,
                            "commutes": [],
                        },
                        "public_transport_lines": [],
                        "public_transport_stops": [],
                        "bicycle_amenity_scenario": {
                            "data": None,
                            "commutes": [],
                        },
                        "bicycle_amenities": [],
                        "bicycle_lines": [],
                        "car_amenity_scenario": {
                            "data": None,
                            "commutes": [],
                        },
                        "car_amenities": [],
                        "car_ways": [],
                        "time_failed": [],
                        "geocode_failed_sites": [],
                        "geocode_failed_employees": [],
                        "geocode_failed_both": [],
                        "employees": {
                            "1": {
                                "id": 1,
                                "nickname": "employee_01",
                                "address": "emlpoyee_address_01",
                                "address_details": {
                                    "full": "fake address",
                                    "normalized": "normalized fake address",
                                    "city": "fake city name",
                                    "postcode": "12123",
                                    "citycode": "12345",
                                    "coordinates": {"latitude": 2.0, "longitude": 1.0},
                                },
                                "transport_mode": "WALK",
                                "remote": True,
                                "coordinates": {"latitude": 2.0, "longitude": 1.0},
                                "crit_air": "CRITAIR_NON_CLASSE",
                            }
                        },
                        "sites": {
                            "1": {
                                "id": 1,
                                "nickname": "site_01",
                                "address": "site_address_01",
                                "address_details": {
                                    "full": "fake address",
                                    "normalized": "normalized fake address",
                                    "city": "fake city name",
                                    "postcode": "12123",
                                    "citycode": "12345",
                                    "coordinates": {"latitude": 2.0, "longitude": 1.0},
                                },
                                "coordinates": {"latitude": 46.0, "longitude": 5.0},
                            }
                        },
                        "scenarios_data": {
                            "1": {"id": 1, "nickname": "trivial", "is_present": True}
                        },
                        "isochrones": {},
                        "coworking_scenario": {
                            "data": None,
                            "commutes": [],
                        },
                        "coworking_failed_scenario": {
                            "data": None,
                            "commutes": [],
                        },
                    }
                )
            )

        study = StudyJsonBuilder(json_file_name).build_timed_study()

        timed_study = timed_study_factory(
            poi_scenarios=Scenarios(frozenset({poi_scenario})),
            infrastructure=timed_infrastructure(),
        )
        assert study == timed_study

    @mock.patch("mobility.builders.json_builder.compute_poi_scenario")
    def test_add_poi_scenarios(
        self,
        mock_compute_poi: Any,
        consolidated_study_factory: Any,
        timed_poi_commute_factory: Any,
        point_of_interest_factory: Any,
        poi_scenario_factory: Any,
        bicycle_line: Any,
        public_transport_line: Any,
        timed_infrastructure: Any,
    ) -> None:
        bicycle_lines = frozenset(
            {
                bicycle_line(name="bicycle_line_01"),
            }
        )
        public_transport_lines = frozenset(
            {
                public_transport_line(name="pt_line_01"),
            }
        )
        infrastructure = timed_infrastructure(
            bicycle_lines=bicycle_lines,
            public_transport_lines=public_transport_lines,
        )
        study = consolidated_study_factory(infrastructure=infrastructure)
        poi_commute = timed_poi_commute_factory(
            poi=point_of_interest_factory(
                name="my_poi",
                coordinates=GeoCoordinates(42.23, 4.52),
            )
        )
        pois_scenarios = Scenarios(
            frozenset(
                {
                    poi_scenario_factory(
                        nickname="not_trivial", commutes=frozenset({poi_commute})
                    )
                }
            )
        )
        mock_compute_poi.return_value = pois_scenarios
        poi_scenarios: PoiScenarios = mock_compute_poi.return_value.update_commutes(
            lambda x: TimedCommuteData(
                duration=ImmutableDict(),
                distance=ImmutableDict(),
                emission=ImmutableDict(),
                alternative_arrival_time=ImmutableDict(),
            )
        )

        updated_study = StudyJsonBuilder.add_poi_scenarios(study)

        assert updated_study.poi_scenarios == poi_scenarios
        assert updated_study.infrastructure.bicycle_lines == bicycle_lines
        assert (
            updated_study.infrastructure.public_transport_lines
            == public_transport_lines
        )


class TestConsolidatedStudyJsonBuilder:
    def test_should_parse_json_into_consolidated_study(
        self,
        consolidated_study_factory,
        timed_infrastructure,
        poi_scenario,
        tmp_path,
    ) -> None:
        json_study = ImmutableDict(
            {
                "data": ImmutableDict(
                    {
                        "company": "Modelity",
                        "territory": "LYON",
                        "mission_id": "YYMXXX.V",
                        "arrival_time": "(8, 30)",
                        "agency": "",
                    }
                ),
                "scenarios": [
                    ImmutableDict(
                        {
                            "data": "1",
                            "commutes": [
                                ImmutableDict(
                                    {
                                        "origin": "1",
                                        "destination": "1",
                                        "commute_data": ImmutableDict(
                                            {
                                                "best_mode": "WALK",
                                                "duration": ImmutableDict(
                                                    {
                                                        "BICYCLE": 1600,
                                                        "CAR": 800,
                                                        "PUBLIC_TRANSPORT": 1200,
                                                        "WALK": 3200,
                                                        "CARPOOLING": 900,
                                                        "ELECTRIC_BICYCLE": 1500,
                                                        "ELECTRIC_CAR": 850,
                                                        "MOTORCYCLE": 700,
                                                        "AIRPLANE": 3600,
                                                        "ELECTRIC_MOTORCYCLE": 750,
                                                        "FAST_BICYCLE": 1400,
                                                        "CAR_PUBLIC_TRANSPORT": 1000,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 1100,
                                                    }
                                                ),
                                                "distance": ImmutableDict(
                                                    {
                                                        "BICYCLE": 1800,
                                                        "CAR": 2000,
                                                        "PUBLIC_TRANSPORT": 2100,
                                                        "WALK": 1800,
                                                        "CARPOOLING": 2100,
                                                        "ELECTRIC_BICYCLE": 1800,
                                                        "ELECTRIC_CAR": 2000,
                                                        "MOTORCYCLE": 2000,
                                                        "AIRPLANE": 20000,
                                                        "ELECTRIC_MOTORCYCLE": 2000,
                                                        "FAST_BICYCLE": 2000,
                                                        "CAR_PUBLIC_TRANSPORT": 2200,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 2100,
                                                    }
                                                ),
                                                "emission": ImmutableDict(
                                                    {
                                                        "BICYCLE": 0,
                                                        "CAR": 4800,
                                                        "PUBLIC_TRANSPORT": 1000,
                                                        "WALK": 0,
                                                        "CARPOOLING": 2400,
                                                        "ELECTRIC_BICYCLE": 10,
                                                        "ELECTRIC_CAR": 1200,
                                                        "MOTORCYCLE": 4800,
                                                        "AIRPLANE": 10000,
                                                        "ELECTRIC_MOTORCYCLE": 15,
                                                        "FAST_BICYCLE": 10,
                                                        "CAR_PUBLIC_TRANSPORT": 1500,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 1000,
                                                    }
                                                ),
                                                "alternative_arrival_time": ImmutableDict(),
                                            }
                                        ),
                                    }
                                )
                            ],
                        }
                    )
                ],
                "poi_scenarios": [
                    ImmutableDict(
                        {
                            "data": "1",
                            "commutes": [
                                ImmutableDict(
                                    {
                                        "origin": "1",
                                        "destination": ImmutableDict(
                                            {
                                                "name": "Gare Part-Dieu",
                                                "coordinates": ImmutableDict(
                                                    {
                                                        "latitude": 45.760749,
                                                        "longitude": 4.861171,
                                                    }
                                                ),
                                            }
                                        ),
                                        "commute_data": ImmutableDict(
                                            {
                                                "best_mode": TransportMode.PUBLIC_TRANSPORT,
                                                "duration": ImmutableDict(
                                                    {
                                                        "BICYCLE": 1600,
                                                        "CAR": 800,
                                                        "PUBLIC_TRANSPORT": 1200,
                                                        "WALK": 3200,
                                                        "CARPOOLING": 900,
                                                        "ELECTRIC_BICYCLE": 1500,
                                                        "ELECTRIC_CAR": 850,
                                                        "MOTORCYCLE": 700,
                                                        "AIRPLANE": 3600,
                                                        "ELECTRIC_MOTORCYCLE": 750,
                                                        "FAST_BICYCLE": 1400,
                                                        "CAR_PUBLIC_TRANSPORT": 1000,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 1100,
                                                    }
                                                ),
                                                "distance": ImmutableDict(
                                                    {
                                                        "BICYCLE": 1800,
                                                        "CAR": 2000,
                                                        "PUBLIC_TRANSPORT": 2100,
                                                        "WALK": 1800,
                                                        "CARPOOLING": 2100,
                                                        "ELECTRIC_BICYCLE": 1800,
                                                        "ELECTRIC_CAR": 2000,
                                                        "MOTORCYCLE": 2000,
                                                        "AIRPLANE": 20000,
                                                        "ELECTRIC_MOTORCYCLE": 2000,
                                                        "FAST_BICYCLE": 2000,
                                                        "CAR_PUBLIC_TRANSPORT": 2200,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 2100,
                                                    }
                                                ),
                                                "emission": ImmutableDict(
                                                    {
                                                        "BICYCLE": 0,
                                                        "CAR": 4800,
                                                        "PUBLIC_TRANSPORT": 1000,
                                                        "WALK": 0,
                                                        "CARPOOLING": 2400,
                                                        "ELECTRIC_BICYCLE": 10,
                                                        "ELECTRIC_CAR": 1200,
                                                        "MOTORCYCLE": 4800,
                                                        "AIRPLANE": 10000,
                                                        "ELECTRIC_MOTORCYCLE": 15,
                                                        "FAST_BICYCLE": 10,
                                                        "CAR_PUBLIC_TRANSPORT": 1500,
                                                        "BICYCLE_PUBLIC_TRANSPORT": 1000,
                                                    }
                                                ),
                                                "alternative_arrival_time": ImmutableDict(),
                                            }
                                        ),
                                    }
                                )
                            ],
                        }
                    )
                ],
                "time_failed": [],
                "geocode_failed_sites": [],
                "geocode_failed_employees": [],
                "geocode_failed_both": [],
                "public_transport_stations_scenario": {
                    "data": None,
                    "commutes": [],
                },
                "bicycle_amenity_scenario": {
                    "data": None,
                    "commutes": [],
                },
                "car_amenity_scenario": {
                    "data": None,
                    "commutes": [],
                },
                "employees": ImmutableDict(
                    {
                        "1": ImmutableDict(
                            {
                                "id": 1,
                                "nickname": "employee_01",
                                "address": "emlpoyee_address_01",
                                "address_details": ImmutableDict(
                                    {
                                        "full": "fake address",
                                        "normalized": "normalized fake address",
                                        "city": "fake city name",
                                        "postcode": "12123",
                                        "citycode": "12345",
                                        "coordinates": ImmutableDict(
                                            {"latitude": 2.0, "longitude": 1.0}
                                        ),
                                    }
                                ),
                                "transport_mode": "WALK",
                                "coordinates": ImmutableDict(
                                    {"latitude": 2.0, "longitude": 1.0}
                                ),
                                "remote": True,
                                "crit_air": "CRITAIR_NON_CLASSE",
                            }
                        )
                    }
                ),
                "sites": ImmutableDict(
                    {
                        "1": ImmutableDict(
                            {
                                "id": 1,
                                "nickname": "site_01",
                                "address": "site_address_01",
                                "address_details": ImmutableDict(
                                    {
                                        "full": "fake address",
                                        "normalized": "normalized fake address",
                                        "city": "fake city name",
                                        "postcode": "12123",
                                        "citycode": "12345",
                                        "coordinates": ImmutableDict(
                                            {"latitude": 2.0, "longitude": 1.0}
                                        ),
                                    }
                                ),
                                "coordinates": ImmutableDict(
                                    {"latitude": 46.0, "longitude": 5.0}
                                ),
                            }
                        )
                    }
                ),
                "scenarios_data": ImmutableDict(
                    {
                        "1": ImmutableDict(
                            {"id": 1, "nickname": "trivial", "is_present": True}
                        )
                    }
                ),
                "public_transport_stops": [],
                "public_transport_lines": [],
                "bicycle_amenities": [],
                "bicycle_lines": [],
                "car_amenities": [],
                "car_ways": [],
                "isochrones": {},
                "coworking_scenario": {"data": None, "commutes": []},
                "coworking_failed_scenario": {"data": None, "commutes": []},
            }
        )

        study = ConsolidatedJsonBuilder(json_study).build()

        consolidated_study = consolidated_study_factory(
            poi_scenarios=Scenarios(frozenset({poi_scenario})),
            infrastructure=timed_infrastructure(),
        )
        assert study == consolidated_study

    def test_should_parse_json_isochrones(self, geo_site_factory) -> None:
        json_study = ImmutableDict(
            {
                "data": ImmutableDict({"company": "Modelity", "territory": "LYON"}),
                "scenarios": [],
                "poi_scenarios": [],
                "time_failed": [],
                "geocode_failed_sites": [],
                "geocode_failed_employees": [],
                "geocode_failed_both": [],
                "employees": ImmutableDict(),
                "sites": ImmutableDict(
                    {
                        "1": ImmutableDict(
                            {
                                "id": 1,
                                "nickname": "actuel",
                                "address": "the address",
                                "address_details": ImmutableDict(
                                    {
                                        "full": "the address",
                                        "normalized": "",
                                        "city": "fake city name",
                                        "postcode": "12123",
                                        "citycode": "12345",
                                        "coordinates": ImmutableDict(
                                            {"latitude": 45.1, "longitude": 4.21}
                                        ),
                                    }
                                ),
                                "coordinates": ImmutableDict(
                                    {"latitude": 45.1, "longitude": 4.21}
                                ),
                            }
                        )
                    }
                ),
                "scenarios_data": ImmutableDict(),
                "isochrones": {
                    "1": {
                        "WALK": {
                            600: {
                                "type": "MultiPolygon",
                                "coordinates": [[[[5.0, 45.0]]]],
                            }
                        }
                    }
                },
            }
        )

        iso_json = ConsolidatedJsonBuilder(json_study).construct_isochrones()

        assert iso_json == {
            geo_site_factory(
                id=1,
                nickname="actuel",
                address="the address",
                address_details=Address(
                    full="the address",
                    normalized="",
                    city="fake city name",
                    postcode="12123",
                    citycode="12345",
                    coordinates=GeoCoordinates(45.1, 4.21),
                ),
                coordinates=GeoCoordinates(45.1, 4.21),
            ): {
                TransportMode.WALK: {
                    600
                    * seconds: {
                        "type": "MultiPolygon",
                        "coordinates": [[[[5.0, 45.0]]]],
                    }
                }
            }
        }

    def test_should_parse_json_public_transport_stops(self) -> None:
        json_study = ImmutableDict(
            {
                "data": ImmutableDict({"company": "Modelity", "territory": "LYON"}),
                "scenarios": [],
                "poi_scenarios": [],
                "time_failed": [],
                "geocode_failed_sites": [],
                "geocode_failed_employees": [],
                "geocode_failed_both": [],
                "employees": ImmutableDict(),
                "sites": ImmutableDict(),
                "scenarios_data": ImmutableDict(),
                "isochrones": ImmutableDict(),
                "public_transport_stops": [
                    ImmutableDict(
                        {
                            "name": "part-dieu",
                            "coordinates": ImmutableDict(
                                {"latitude": 1, "longitude": 2}
                            ),
                            "lines_connected": ("A",),
                            "lines_kinds": (TransportType.TRAIN,),
                        }
                    ),
                    ImmutableDict(
                        {
                            "name": "gerland",
                            "coordinates": ImmutableDict(
                                {"latitude": 3, "longitude": 4}
                            ),
                            "lines_connected": ("B",),
                            "lines_kinds": (TransportType.SUBWAY,),
                        }
                    ),
                ],
            }
        )

        stops = ConsolidatedJsonBuilder(json_study).construct_public_transport_stops()

        assert stops == frozenset(
            {
                PublicTransportStop(
                    name="part-dieu",
                    coordinates=GeoCoordinates(latitude=1, longitude=2),
                    lines_connected=frozenset({"A"}),
                    lines_kinds=frozenset({TransportType.TRAIN}),
                ),
                PublicTransportStop(
                    name="gerland",
                    coordinates=GeoCoordinates(latitude=3, longitude=4),
                    lines_connected=frozenset({"B"}),
                    lines_kinds=frozenset({TransportType.SUBWAY}),
                ),
            }
        )

    def test_should_parse_json_public_transport_stations_scenario(
        self, geo_site_factory
    ) -> None:
        json_study = ImmutableDict(
            {
                "data": ImmutableDict({"company": "Modelity", "territory": "LYON"}),
                "scenarios": [],
                "poi_scenarios": [],
                "time_failed": [],
                "geocode_failed_sites": [],
                "geocode_failed_employees": [],
                "geocode_failed_both": [],
                "employees": ImmutableDict(),
                "sites": {
                    "1": {
                        "address": "site_address_01",
                        "address_details": ImmutableDict(
                            {
                                "full": "site_address_01",
                                "normalized": "site_address_01",
                                "city": "fake city name",
                                "postcode": "12123",
                                "citycode": "12345",
                                "coordinates": ImmutableDict(
                                    {"latitude": 46.0, "longitude": 5.0}
                                ),
                            }
                        ),
                        "coordinates": {"latitude": 46.0, "longitude": 5.0},
                        "id": 1,
                        "nickname": "site_01",
                    }
                },
                "scenarios_data": ImmutableDict(),
                "isochrones": ImmutableDict(),
                "public_transport_stations_scenario": {
                    "commutes": [
                        {
                            "commute_data": {
                                "distance": {},
                                "duration": {"WALK": 200},
                                "emission": {},
                                "alternative_arrival_time": {},
                            },
                            "destination": {
                                "coordinates": {"latitude": 2, "longitude": 2},
                                "lines_connected": ("A",),
                                "lines_kinds": ("train",),
                                "name": "part-dieu",
                            },
                            "origin": "1",
                        }
                    ],
                    "data": None,
                },
            }
        )

        scenario = ConsolidatedJsonBuilder(
            json_study
        ).construct_public_transport_stations_scenario()

        site = geo_site_factory(
            id=1,
            nickname="site_01",
            address="site_address_01",
            address_details=Address(
                full="site_address_01",
                normalized="site_address_01",
                city="fake city name",
                postcode="12123",
                citycode="12345",
                coordinates=GeoCoordinates(46.0, 5.0),
            ),
            coordinates=GeoCoordinates(46.0, 5.0),
        )
        stop = PublicTransportStop(
            name="part-dieu",
            coordinates=GeoCoordinates(2, 2),
            lines_connected=frozenset({"A"}),
            lines_kinds=frozenset({TransportType.TRAIN}),
        )
        data = TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 200}),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = [TimedTransportStopCommute(site, stop, data)]
        expected_scenario = PublicTransportStationsScenario(None, frozenset(commutes))
        assert scenario == expected_scenario

    def test_should_parse_json_public_transport_lines(self) -> None:
        json_study = ImmutableDict(
            {
                "data": ImmutableDict({"company": "Modelity", "territory": "LYON"}),
                "scenarios": [],
                "poi_scenarios": [],
                "time_failed": [],
                "geocode_failed_sites": [],
                "geocode_failed_employees": [],
                "geocode_failed_both": [],
                "employees": ImmutableDict(),
                "sites": ImmutableDict(),
                "scenarios_data": ImmutableDict(),
                "isochrones": ImmutableDict(),
                "public_transport_lines": [
                    {
                        "name": "Bus A",
                        "points": (({"latitude": 1, "longitude": 2},),),
                        "operator": "keolis",
                        "short_name": "A",
                        "kind": TransportType.BUS,
                        "colour": "gold",
                    },
                    {
                        "name": "Bus B",
                        "points": (({"latitude": 3, "longitude": 4},),),
                        "operator": "koala",
                        "short_name": "B",
                        "kind": TransportType.SUBWAY,
                        "colour": "sand",
                    },
                ],
            }
        )

        stops = ConsolidatedJsonBuilder(json_study).construct_public_transport_lines()

        assert stops == frozenset(
            {
                PublicTransportLine(
                    name="Bus A",
                    points=((GeoCoordinates(latitude=1, longitude=2),),),
                    operator="keolis",
                    short_name="A",
                    kind=TransportType.BUS,
                    colour="gold",
                ),
                PublicTransportLine(
                    name="Bus B",
                    points=((GeoCoordinates(latitude=3, longitude=4),),),
                    operator="koala",
                    short_name="B",
                    kind=TransportType.SUBWAY,
                    colour="sand",
                ),
            }
        )


class TestConsolidatedStudyConverter:
    def test_should_do_nothing_if_no_time_error_commute(
        self,
        scenario_data: Any,
        geo_site: Any,
        timed_commute_data: Any,
        geo_employee: Any,
        modal_commute_data: Any,
        timed_infrastructure: Any,
        consolidated_study_factory: Any,
        timed_study_factory: Any,
        study_data: Any,
    ) -> None:
        empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})
        consolidated_scenarios = Scenarios.from_commutes(
            {scenario_data: {(geo_employee, geo_site, modal_commute_data)}}
        )
        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=consolidated_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=empty_scenarios,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        timed_scenarios = Scenarios.from_commutes(
            {scenario_data: {(geo_employee, geo_site, timed_commute_data)}}
        )
        expected_timed_study = timed_study_factory(
            scenarios=timed_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )
        assert study == expected_timed_study

    def test_should_move_time_failed_commute_to_scenarios(
        self,
        geo_employee_factory: Any,
        timed_commute_data_factory: Any,
        scenario_data: Any,
        geo_site: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        consolidated_study_factory: Any,
        study_data: Any,
    ) -> None:
        employee = geo_employee_factory(transport_mode=TransportMode.WALK)
        commute = timed_commute_data_factory(
            duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 25}),
        )
        scenarios = Scenarios.from_commutes(
            {scenario_data: {(employee, geo_site, commute)}}
        )
        empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})

        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=empty_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=scenarios,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        expected_timed_study = timed_study_factory(
            scenarios=scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )
        assert study == expected_timed_study

    def test_move_failed_time_commute_in_every_scenarios(
        self,
        modal_commute_data_factory: Any,
        timed_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_data_factory: Any,
        geo_site: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        consolidated_study_factory: Any,
        study_data: Any,
    ) -> None:
        new_commute_only_pt = timed_commute_data_factory(
            duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 25}),
        )
        walker = geo_employee_factory(transport_mode=TransportMode.WALK)
        walker_bis = geo_employee_factory(id=2, transport_mode=TransportMode.WALK)
        empty_scenarios: Scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): set(),
                scenario_data_factory(id=2, is_present=False): set(),
            }
        )
        scenarios_failed_time = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): {(walker, geo_site, new_commute_only_pt)},
                scenario_data_factory(id=2, is_present=False): {
                    (walker, geo_site, new_commute_only_pt)
                },
            }
        )
        consolidated_walker_bis = geo_employee_factory(
            id=2, transport_mode=TransportMode.WALK
        )
        cons_commute_only_pt = modal_commute_data_factory(
            best_mode=TransportMode.PUBLIC_TRANSPORT,
            duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 25}),
        )
        scenarios_ok: Scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): set(),
                scenario_data_factory(id=2, is_present=False): {
                    (consolidated_walker_bis, geo_site, cons_commute_only_pt),
                },
            }
        )
        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=scenarios_ok,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=scenarios_failed_time,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        timed_scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): {(walker, geo_site, new_commute_only_pt)},
                scenario_data_factory(id=2, is_present=False): {
                    (walker, geo_site, new_commute_only_pt),
                    (walker_bis, geo_site, new_commute_only_pt),
                },
            }
        )
        expected_timed_study = timed_study_factory(
            scenarios=timed_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )
        assert study == expected_timed_study

    def test_should_gather_geocode_failures_in_only_failed_geocode_study(
        self,
        modal_commute_data: Any,
        failed_geo_employee: Any,
        scenario_data: Any,
        geo_employee: Any,
        geo_site: Any,
        timed_commute_data: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        consolidated_study_factory: Any,
        study_data: Any,
    ) -> None:
        scenarios_failed_employee = Scenarios.from_commutes(
            {scenario_data: {(failed_geo_employee, geo_site, None)}}
        )
        empty_scenarios: Scenarios = Scenarios.from_commutes({scenario_data: set()})
        consolidated_scenarios = Scenarios.from_commutes(
            {scenario_data: {(geo_employee, geo_site, modal_commute_data)}}
        )
        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=consolidated_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=empty_scenarios,
            geocode_failed_employees=scenarios_failed_employee,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        timed_scenarios = Scenarios.from_commutes(
            {scenario_data: {(geo_employee, geo_site, timed_commute_data)}}
        )
        expected_timed_study = timed_study_factory(
            scenarios=timed_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_employees=scenarios_failed_employee,
            geocode_failed_sites=empty_scenarios,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )
        assert study == expected_timed_study

    def test_should_gather_geocode_failures_in_multiple_studies(
        self,
        failed_geo_site: Any,
        modal_commute_data: Any,
        scenario_data_factory: Any,
        geo_employee: Any,
        geo_site: Any,
        timed_commute_data: Any,
        timed_infrastructure: Any,
        timed_study_factory: Any,
        consolidated_study_factory: Any,
        study_data: Any,
    ) -> None:
        scenarios_failed_sites: Scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): set(),
                scenario_data_factory(id=2, is_present=False): {
                    (geo_employee, failed_geo_site, timed_commute_data)
                },
            }
        )
        empty_scenarios: Scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): set(),
                scenario_data_factory(id=2, is_present=False): set(),
            }
        )
        cons_scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): {
                    (geo_employee, geo_site, modal_commute_data)
                },
                scenario_data_factory(id=2, is_present=False): {
                    (geo_employee, geo_site, modal_commute_data)
                },
            }
        )
        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=cons_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=empty_scenarios,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_sites=scenarios_failed_sites,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        timed_scenarios = Scenarios.from_commutes(
            {
                scenario_data_factory(id=1): {
                    (geo_employee, geo_site, timed_commute_data)
                },
                scenario_data_factory(id=2, is_present=False): {
                    (geo_employee, geo_site, timed_commute_data)
                },
            }
        )
        expected_timed_study = timed_study_factory(
            scenarios=timed_scenarios,
            poi_scenarios=empty_scenarios,
            infrastructure=infrastructure,
            data=data,
            geocode_failed_employees=empty_scenarios,
            geocode_failed_sites=scenarios_failed_sites,
            geocode_failed_both=empty_scenarios,
            isochrones={},
        )
        assert study == expected_timed_study

    def test_should_copy_poi_scenario(
        self,
        timed_commute_data: Any,
        point_of_interest: Any,
        geo_site: Any,
        scenario_data: Any,
        timed_infrastructure: Any,
        consolidated_study_factory: Any,
        study_data: Any,
    ) -> None:
        poi_commutes = {(geo_site, point_of_interest, timed_commute_data)}
        poi_scenarios = Scenarios.from_commutes({scenario_data: poi_commutes})
        infrastructure = timed_infrastructure()
        data = study_data(mission_id="24M000.0")
        consolidated_study = consolidated_study_factory(
            scenarios=Scenarios.empty(),
            poi_scenarios=poi_scenarios,
            infrastructure=infrastructure,
            data=data,
            time_failed=Scenarios.empty(),
            geocode_failed_employees=Scenarios.empty(),
            geocode_failed_sites=Scenarios.empty(),
            geocode_failed_both=Scenarios.empty(),
            isochrones={},
        )

        study = ConsolidatedStudyConverter(consolidated_study).convert_to_timed_study()

        assert study.poi_scenarios == poi_scenarios


class TestRaiseIfJsonIsInvalid:
    def test_should_raise_an_error_when_json_is_invalid(self) -> None:
        study_json = ImmutableDict({"version": "v2", "company": "foo", "scenarios": []})

        with pytest.raises(WrongJsonFormatError):
            validate_latest_json_schema(study_json)


class TestJsonPromoter:
    def test_should_copy_dict_by_default(self) -> None:
        promoter = JsonPromoter()
        json_dict = ImmutableDict({"anything": ["really"], "0": 65})

        converted_json = promoter.convert(json_dict)

        assert converted_json == json_dict

    def test_should_modify_path(self) -> None:
        class Promoter(JsonPromoter):
            def convert_element(self, parents, element):
                if parents == []:
                    element["added"] = 1
                    return element
                elif parents == ["0"]:
                    return element - 12
                elif parents == ["sublist", "listitem"]:
                    return element + 3
                else:
                    return element

        promoter = Promoter()
        json_dict = ImmutableDict(
            {
                "anything": ["really"],
                "0": 65,
                "sublist": [ImmutableDict({"listitem": 5})],
            }
        )

        converted_json = promoter.convert(json_dict)

        expected_json_dict = ImmutableDict(
            {
                "anything": ["really"],
                "0": 53,
                "added": 1,
                "sublist": [ImmutableDict({"listitem": 8})],
            }
        )
        assert converted_json == expected_json_dict


class TestJsonStudyPromoter:
    def test_should_convert_values_and_version(self) -> None:
        class Promoter(JsonStudyPromoter):
            next_version = JSONVersion.V3

            def convert_study_element(self, parents, element):
                if parents == ["sub", "path", "thing"]:
                    return element + 17
                else:
                    return element

        promoter = Promoter()
        json_dict = ImmutableDict(
            {
                "version": "v1",
                "sub": ImmutableDict(
                    {
                        "path": ImmutableDict({"thing": 5, "stuff": 0}),
                        "other subpath": 4,
                    }
                ),
                "other": "a string ?",
            }
        )

        converted_json = promoter.convert(json_dict)

        expected_json = ImmutableDict(
            {
                "version": JSONVersion.V3.to_string(),
                "sub": ImmutableDict(
                    {
                        "path": ImmutableDict({"thing": 22, "stuff": 0}),
                        "other subpath": 4,
                    }
                ),
                "other": "a string ?",
            }
        )
        assert converted_json == expected_json


class TestJSONVersion:
    @pytest.mark.parametrize("invalid_format", ["1", "V1", " v1", "version1", "v-1"])
    def test_from_string_invalid_format_should_raise(self, invalid_format):
        with pytest.raises(ValueError):
            JSONVersion.from_string(invalid_format)

    @pytest.mark.parametrize("non_existent_version", ["v0", "v999"])
    def test_from_string_invalid_version_number_should_raise(
        self, non_existent_version
    ):
        with pytest.raises(ValueError):
            JSONVersion.from_string(non_existent_version)

    @pytest.mark.parametrize("non_integer_version", ["v1.1", "v1a", "va", "v.1"])
    def test_from_string_with_non_integer_version_number_should_raise(
        self, non_integer_version
    ):
        with pytest.raises(ValueError):
            JSONVersion.from_string(non_integer_version)

    @pytest.mark.parametrize("version_enum", [v for v in JSONVersion])
    def test_should_return_string_for_each_json_version(self, version_enum):
        result = version_enum.to_string()

        assert result == f"v{version_enum.value}"

{"data_mtime": 1752154451, "dep_lines": [15, 13, 14, 16, 23, 24, 26, 2, 7, 12, 25, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.error", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.serializers.excel_serializer", "unittest.mock", "mobility.constants", "mobility.funky", "mobility.serializers", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.ir", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "re", "typing_extensions"], "hash": "f99fb70416c4d059c8581e9505fb9b772f03ae7b", "id": "mobility.tests.serializers.test_excel_serializer", "ignore_all": false, "interface_hash": "c1f6b94b860e194222e86449ff47b4a618848410", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\serializers\\test_excel_serializer.py", "plugin_data": null, "size": 57199, "suppressed": ["pandas"], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "api_abstraction.api.modal_travel_time_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModalTravelTimeApi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "name": "ModalTravelTimeApi", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.api.modal_travel_time_api", "mro": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode_apis", "isochrone_apis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode_apis", "isochrone_apis"], "arg_types": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "api_abstraction.api.travel_time_api.TravelTimeApi"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "api_abstraction.api.travel_time_api.TravelTimeApi"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModalTravelTimeApi", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_detour_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.compute_detour_costs", "name": "compute_detour_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "arg_types": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", {".class": "Instance", "args": ["mobility.ir.geo_study.GeoCoordinates"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_detour_costs of ModalTravelTimeApi", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_detours": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.compute_detours", "name": "compute_detours", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "arg_types": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", {".class": "Instance", "args": ["mobility.ir.geo_study.GeoCoordinates"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_detours of ModalTravelTimeApi", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int", "api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.compute_isochrone", "name": "compute_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "arg_types": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "mobility.ir.territory.Territory", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isochrone of ModalTravelTimeApi", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isochrone_apis": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.isochrone_apis", "name": "isochrone_apis", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "api_abstraction.api.travel_time_api.TravelTimeApi"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "mode_apis": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.mode_apis", "name": "mode_apis", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "api_abstraction.api.travel_time_api.TravelTimeApi"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.time", "name": "time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "time of ModalTravelTimeApi", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.time", "name": "time", "setter_type": null, "type": {".class": "Instance", "args": ["api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.api.modal_travel_time_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cache": {".class": "SymbolTableNode", "cross_ref": "functools.cache", "kind": "Gdef"}}, "path": "api_abstraction\\api\\modal_travel_time_api.py"}
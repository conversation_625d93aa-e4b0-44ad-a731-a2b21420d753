{"data_mtime": 1752154451, "dep_lines": [17, 9, 10, 11, 12, 13, 14, 15, 18, 19, 21, 22, 23, 24, 25, 30, 16, 20, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.travel_time_api", "mobility.builders.address_expander", "mobility.builders.exceptions", "mobility.builders.territory_builder", "mobility.entry_points.run_study", "mobility.ir.map_elements", "mobility.ir.study", "mobility.workers.emission_computer", "mobility.workers.isochrone_computer", "mobility.workers.territory_computer", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "mobility.workers.travel_timer", "mobility.interface", "mobility.serializers", "<PERSON><PERSON><PERSON><PERSON>", "logging", "sys", "datetime", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "mobility.builders", "mobility.ir", "mobility.ir.indicators", "mobility.ir.new_indicators", "mobility.ir.territory", "mobility.serializers.report_serializer", "mobility.workers", "types", "typing_extensions"], "hash": "635e6b62c1283621eaa17e877132abc321ee986b", "id": "mobility.entry_points.generate_building_mobility_study", "ignore_all": false, "interface_hash": "12b0a557229b95205d6637fd66fcd7e613bdf16e", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\entry_points\\generate_building_mobility_study.py", "plugin_data": null, "size": 6885, "suppressed": [], "version_id": "1.16.1"}
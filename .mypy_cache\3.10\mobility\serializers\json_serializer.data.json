{".class": "MypyFile", "_fullname": "mobility.serializers.json_serializer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ConsolidatedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ConsolidatedStudy", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "StudyEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["json.encoder.JSONEncoder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.serializers.json_serializer.StudyEncoder", "name": "StudyEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.serializers.json_serializer.StudyEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.serializers.json_serializer", "mro": ["mobility.serializers.json_serializer.StudyEncoder", "json.encoder.JSONEncoder", "builtins.object"], "names": {".class": "SymbolTable", "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.serializers.json_serializer.StudyEncoder.default", "name": "default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["mobility.serializers.json_serializer.StudyEncoder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default of StudyEncoder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.serializers.json_serializer.StudyEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.serializers.json_serializer.StudyEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StudyJsonSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer", "name": "StudyJsonSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.serializers.json_serializer", "mro": ["mobility.serializers.json_serializer.StudyJsonSerializer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dir_name", "study", "report_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dir_name", "study", "report_time"], "arg_types": ["mobility.serializers.json_serializer.StudyJsonSerializer", "builtins.str", "mobility.ir.study.ConsolidatedStudy", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StudyJsonSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dst": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer.dst", "name": "dst", "setter_type": null, "type": "pathlib.Path"}}, "serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer.serialize", "name": "serialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.serializers.json_serializer.StudyJsonSerializer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize of StudyJsonSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "study": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer.study", "name": "study", "setter_type": null, "type": "mobility.ir.study.ConsolidatedStudy"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.serializers.json_serializer.StudyJsonSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.serializers.json_serializer.StudyJsonSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.serializers.json_serializer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "make_dir_for": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.make_dir_for", "kind": "Gdef"}}, "path": "mobility\\serializers\\json_serializer.py"}
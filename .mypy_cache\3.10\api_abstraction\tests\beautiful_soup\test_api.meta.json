{"data_mtime": 1751444413, "dep_lines": [6, 7, 12, 13, 2, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.beautiful_soup.api", "mobility.ir.geo_study", "mobility.ir.transport", "unittest.mock", "typing", "unittest", "bs4", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "api_abstraction.beautiful_soup", "api_abstraction.geopy", "api_abstraction.geopy.ban_france", "bs4.builder", "bs4.element", "enum", "functools", "mobility", "mobility.ir", "types", "typing_extensions"], "hash": "9648d982b169b50a9fa671953bded9e4c7355090", "id": "api_abstraction.tests.beautiful_soup.test_api", "ignore_all": false, "interface_hash": "8dd04b702c2e0a25ccc33a7d30777ae47f9d0dd0", "mtime": 1723449305, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\beautiful_soup\\test_api.py", "plugin_data": null, "size": 3130, "suppressed": [], "version_id": "1.16.1"}
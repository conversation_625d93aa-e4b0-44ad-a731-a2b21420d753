{".class": "MypyFile", "_fullname": "api_abstraction.tests.google.test_distance_matrix_api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiTimeout": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiTimeout", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DistanceMatrix": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.distance_matrix.DistanceMatrix", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "GoogleRequestParameters": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.base_google_api.GoogleRequestParameters", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "TestDistanceMatrix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "name": "TestDistanceMatrix", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_distance_matrix_api", "mro": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "builtins.object"], "names": {".class": "SymbolTable", "test_api_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_api_error_message_is_explicit", "name": "test_api_error_message_is_explicit", "type": null}}, "test_compute_route_fails_with_proper_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_compute_route_fails_with_proper_exceptions", "name": "test_compute_route_fails_with_proper_exceptions", "type": null}}, "test_compute_route_formats_request_and_returns_journey_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_googlemaps_client", "mock_distance_matrix_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_compute_route_formats_request_and_returns_journey_attributes", "name": "test_compute_route_formats_request_and_returns_journey_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mock_googlemaps_client", "mock_distance_matrix_response"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_compute_route_formats_request_and_returns_journey_attributes of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_formats_input_parameters_correctly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_formats_input_parameters_correctly", "name": "test_formats_input_parameters_correctly", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_formats_input_parameters_correctly of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_http_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_http_error_message_is_explicit", "name": "test_http_error_message_is_explicit", "type": null}}, "test_maps_transport_modes_correctly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_maps_transport_modes_correctly", "name": "test_maps_transport_modes_correctly", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "mobility.ir.transport.TransportMode", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_maps_transport_modes_correctly of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_maps_transport_modes_correctly", "name": "test_maps_transport_modes_correctly", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "expected_google_mode"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "mobility.ir.transport.TransportMode", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_maps_transport_modes_correctly of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_over_query_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_over_query_error_message_is_explicit", "name": "test_over_query_error_message_is_explicit", "type": null}}, "test_raises_error_for_unsupported_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_for_unsupported_mode", "name": "test_raises_error_for_unsupported_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_for_unsupported_mode of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_for_unsupported_mode", "name": "test_raises_error_for_unsupported_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unsupported_mode"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_for_unsupported_mode of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_raises_error_if_element_status_not_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_if_element_status_not_ok", "name": "test_raises_error_if_element_status_not_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_element_status_not_ok of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_raises_error_if_no_duration_in_traffic_for_car": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_if_no_duration_in_traffic_for_car", "name": "test_raises_error_if_no_duration_in_traffic_for_car", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_no_duration_in_traffic_for_car of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_raises_error_if_status_not_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_if_status_not_ok", "name": "test_raises_error_if_status_not_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_status_not_ok of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_raises_error_if_wrong_elements_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_if_wrong_elements_count", "name": "test_raises_error_if_wrong_elements_count", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_wrong_elements_count of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_raises_error_if_wrong_row_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_raises_error_if_wrong_row_count", "name": "test_raises_error_if_wrong_row_count", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_raises_error_if_wrong_row_count of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_retriable_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_retriable_error_message_is_explicit", "name": "test_retriable_error_message_is_explicit", "type": null}}, "test_timeout_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_timeout_message_is_explicit", "name": "test_timeout_message_is_explicit", "type": null}}, "test_transport_error_message_is_explicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fake_googlemaps_exception_raiser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_transport_error_message_is_explicit", "name": "test_transport_error_message_is_explicit", "type": null}}, "test_uses_normal_duration_for_non_car_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.test_uses_normal_duration_for_non_car_modes", "name": "test_uses_normal_duration_for_non_car_modes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mock_googlemaps_client"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_uses_normal_duration_for_non_car_modes of TestDistanceMatrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_distance_matrix_api.TestDistanceMatrix", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "failing_traffic_distance_matrix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.failing_traffic_distance_matrix", "name": "failing_traffic_distance_matrix", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "failing_traffic_distance_matrix", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.failing_traffic_distance_matrix", "name": "failing_traffic_distance_matrix", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "failing_traffic_distance_matrix", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fake_compute_route@96": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", "name": "fake_compute_route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_distance_matrix_api", "mro": ["api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", "builtins.Exception"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of fake_compute_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "distance_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96.distance_matrix", "name": "distance_matrix", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "distance_matrix of fake_compute_route", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "e": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96.e", "name": "e", "setter_type": null, "type": "builtins.Exception"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_distance_matrix_api.fake_compute_route@96", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_distance_matrix@18": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", "name": "fake_distance_matrix", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.google.test_distance_matrix_api", "mro": ["api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n_failing_calls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n_failing_calls"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of fake_distance_matrix", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "distance_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18.distance_matrix", "name": "distance_matrix", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "distance_matrix of fake_distance_matrix", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "n_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18.n_calls", "name": "n_calls", "setter_type": null, "type": "builtins.int"}}, "n_failing_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18.n_failing_calls", "name": "n_failing_calls", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.google.test_distance_matrix_api.fake_distance_matrix@18", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fake_googlemaps_exception_raiser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_googlemaps_exception_raiser", "name": "fake_googlemaps_exception_raiser", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fake_googlemaps_exception_raiser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.fake_googlemaps_exception_raiser", "name": "fake_googlemaps_exception_raiser", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fake_googlemaps_exception_raiser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "googlemaps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.googlemaps", "name": "googlemaps", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.tests.google.test_distance_matrix_api.googlemaps", "source_any": null, "type_of_any": 3}}}, "mock_distance_matrix_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.mock_distance_matrix_response", "name": "mock_distance_matrix_response", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_distance_matrix_response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.mock_distance_matrix_response", "name": "mock_distance_matrix_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_distance_matrix_response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_googlemaps_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.mock_googlemaps_client", "name": "mock_googlemaps_client", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_googlemaps_client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.google.test_distance_matrix_api.mock_googlemaps_client", "name": "mock_googlemaps_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mock_googlemaps_client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "api_abstraction\\tests\\google\\test_distance_matrix_api.py"}
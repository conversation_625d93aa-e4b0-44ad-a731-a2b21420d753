{"data_mtime": 1752154451, "dep_lines": [8, 11, 14, 15, 16, 19, 7, 20, 21, 23, 24, 6, 22, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.bicycle_infrastructure_indicators", "mobility.ir.indicators.car_infrastructure_indicators", "mobility.ir.indicators.indicators", "mobility.ir.indicators.poi_scenario_indicators", "mobility.ir.indicators.pt_infrastructure_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.cost", "mobility.ir.infrastructure", "mobility.ir.transport", "mobility.serializers.chart_tracer", "mobility.serializers.illustrator", "mobility.enumerations", "mobility.quantity", "os", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "abc", "enum", "mobility.converters", "mobility.converters.accessibility", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.study_indicators", "mobility.ir.map_elements", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.serializers", "typing_extensions"], "hash": "ba3c72cfbb8d4d8b50a98014d66cd4433921ce15", "id": "mobility.tests.template.content.test_content_generation", "ignore_all": false, "interface_hash": "196ed340b4bb53370d598ed4e231aac9b587b6be", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\template\\content\\test_content_generation.py", "plugin_data": null, "size": 5430, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1753176776, "dep_lines": [11, 7, 9, 10, 12, 8, 1, 2, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.carpool_indicators", "api_abstraction.api.travel_time_api", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.funky", "dataclasses", "collections", "math", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "enum", "mobility.ir", "mobility.ir.indicators"], "hash": "f7e255fb465dd537555f5b9f0fa12124a06d584c", "id": "mobility.workers.carpool_computer", "ignore_all": false, "interface_hash": "4b671bc08bbc4d503d94b4158490e5f4bac7dff4", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\carpool_computer.py", "plugin_data": null, "size": 11431, "suppressed": [], "version_id": "1.16.1"}
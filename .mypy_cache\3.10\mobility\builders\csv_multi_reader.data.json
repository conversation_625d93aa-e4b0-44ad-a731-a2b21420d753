{".class": "MypyFile", "_fullname": "mobility.builders.csv_multi_reader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Constraints": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_constraints.Constraints", "kind": "Gdef"}, "CsvEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.CsvEmployee", "kind": "Gdef"}, "CsvMultiReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader", "name": "CsvMultiReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.csv_multi_reader", "mro": ["mobility.builders.csv_multi_reader.CsvMultiReader", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "site_csv_file", "employee_csv_file", "parameters_csv_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "site_csv_file", "employee_csv_file", "parameters_csv_file"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CsvMultiReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "employee_id", "row"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_employee", "name": "_extract_employee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "employee_id", "row"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader", "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_employee of CsvMultiReader", "ret_type": "mobility.ir.employee.CsvEmployee", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["raw_remote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_remote", "name": "_extract_remote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["raw_remote"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_remote of CsvMultiReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_remote", "name": "_extract_remote", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["raw_remote"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_remote of CsvMultiReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_extract_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "site_id", "row"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_site", "name": "_extract_site", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "site_id", "row"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader", "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_site of CsvMultiReader", "ret_type": {".class": "UnionType", "items": ["mobility.ir.site.CsvSite", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_transport_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["raw_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_transport_mode", "name": "_extract_transport_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["raw_mode"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_transport_mode of CsvMultiReader", "ret_type": {".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader._extract_transport_mode", "name": "_extract_transport_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["raw_mode"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_transport_mode of CsvMultiReader", "ret_type": {".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build of CsvMultiReader", "ret_type": "mobility.ir.study.CsvStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_employees_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_employees_mapping", "name": "build_employees_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_employees_mapping of CsvMultiReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.ir.employee.CsvEmployee"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_pois": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_pois", "name": "build_pois", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_pois of CsvMultiReader", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_raw_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario_columns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_raw_scenarios", "name": "build_raw_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario_columns"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_raw_scenarios of CsvMultiReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str", "builtins.str", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_scenario_data_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_scenario_data_mapping", "name": "build_scenario_data_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_scenario_data_mapping of CsvMultiReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.ir.scenario_data.ScenarioData"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_sites_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_sites_mapping", "name": "build_sites_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_sites_mapping of CsvMultiReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.ir.site.CsvSite"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_study_data_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.build_study_data_parameters", "name": "build_study_data_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvMultiReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_study_data_parameters of CsvMultiReader", "ret_type": "mobility.ir.study.CsvStudyDataParameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "employee_address_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_address_column", "name": "employee_address_column", "setter_type": null, "type": "builtins.str"}}, "employee_current_site_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_current_site_column", "name": "employee_current_site_column", "setter_type": null, "type": "builtins.str"}}, "employee_id_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_id_column", "name": "employee_id_column", "setter_type": null, "type": "builtins.str"}}, "employee_minimal_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_minimal_header", "name": "employee_minimal_header", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "employee_mode_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_mode_column", "name": "employee_mode_column", "setter_type": null, "type": "builtins.str"}}, "employee_remote_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employee_remote_column", "name": "employee_remote_column", "setter_type": null, "type": "builtins.str"}}, "employees_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.employees_data", "name": "employees_data", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parameters_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.parameters_data", "name": "parameters_data", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "site_address_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.site_address_column", "name": "site_address_column", "setter_type": null, "type": "builtins.str"}}, "site_id_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.site_id_column", "name": "site_id_column", "setter_type": null, "type": "builtins.str"}}, "site_minimal_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.site_minimal_header", "name": "site_minimal_header", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "site_parking_cost_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.site_parking_cost_column", "name": "site_parking_cost_column", "setter_type": null, "type": "builtins.str"}}, "sites_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.sites_data", "name": "sites_data", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.csv_multi_reader.CsvMultiReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.csv_multi_reader.CsvMultiReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CsvParameterReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader", "name": "CsvParameterReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.csv_multi_reader", "mro": ["mobility.builders.csv_multi_reader.CsvParameterReader", "builtins.object"], "names": {".class": "SymbolTable", "NB_LINES_SKIP_FOR_POI": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.NB_LINES_SKIP_FOR_POI", "name": "NB_LINES_SKIP_FOR_POI", "setter_type": null, "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters_csv_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters_csv_file"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CsvParameterReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader._extract_constraints", "name": "_extract_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters_mapping"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_constraints of CsvParameterReader", "ret_type": "mobility.ir.mode_constraints.Constraints", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_max_durations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parameters_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader._extract_max_durations", "name": "_extract_max_durations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parameters_mapping"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_max_durations of CsvParameterReader", "ret_type": "mobility.ir.mode_constraints.DurationConstraints", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_study_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "row"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader._extract_study_parameter", "name": "_extract_study_parameter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "row"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_study_parameter of CsvParameterReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_arrival_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "time_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader._parse_arrival_time", "name": "_parse_arrival_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "time_str"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_arrival_time of CsvParameterReader", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_parameters_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.build_parameters_mapping", "name": "build_parameters_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_parameters_mapping of CsvParameterReader", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_point_of_interests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.build_point_of_interests", "name": "build_point_of_interests", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_point_of_interests of CsvParameterReader", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parameter_id_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.parameter_id_header", "name": "parameter_id_header", "setter_type": null, "type": "builtins.str"}}, "parameter_minimal_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.parameter_minimal_header", "name": "parameter_minimal_header", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "parameter_value_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.parameter_value_header", "name": "parameter_value_header", "setter_type": null, "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.parameters", "name": "parameters", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parse_study_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.parse_study_data", "name": "parse_study_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.csv_multi_reader.CsvParameterReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_study_data of CsvParameterReader", "ret_type": "mobility.ir.study.CsvStudyDataParameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poi_coordinates_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.poi_coordinates_header", "name": "poi_coordinates_header", "setter_type": null, "type": "builtins.str"}}, "poi_name_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.poi_name_header", "name": "poi_name_header", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.csv_multi_reader.CsvParameterReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.csv_multi_reader.CsvParameterReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CsvSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.CsvSite", "kind": "Gdef"}, "CsvStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.CsvStudy", "kind": "Gdef"}, "CsvStudyDataParameter": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.CsvStudyDataParameter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DurationConstraint": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_constraints.DurationConstraint", "kind": "Gdef"}, "DurationConstraints": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.mode_constraints.DurationConstraints", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PointOfInterest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.poi.PointOfInterest", "kind": "Gdef"}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "WrongCSVFormatError": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.exceptions.WrongCSVFormatError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.csv_multi_reader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_extract_site_parking_cost": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["costs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.csv_multi_reader._extract_site_parking_cost", "name": "_extract_site_parking_cost", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["costs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_site_parking_cost", "ret_type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_study_from_csv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["employee_csv_file", "site_csv_file", "parameters_csv_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.csv_multi_reader.build_study_from_csv", "name": "build_study_from_csv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["employee_csv_file", "site_csv_file", "parameters_csv_file"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_study_from_csv", "ret_type": "mobility.ir.study.CsvStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "raise_error_if_header_is_invalid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["header", "expected_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.csv_multi_reader.raise_error_if_header_is_invalid", "name": "raise_error_if_header_is_invalid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["header", "expected_header"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raise_error_if_header_is_invalid", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "mobility\\builders\\csv_multi_reader.py"}
{"data_mtime": 1751992727, "dep_lines": [7, 14, 15, 16, 17, 18, 19, 3, 13, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.csv_multi_reader", "mobility.ir.geo_study", "mobility.ir.mode_constraints", "mobility.ir.poi", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "unittest.mock", "mobility.funky", "csv", "typing", "pytest", "builtins", "_csv", "_frozen_importlib", "_io", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "io", "mobility.builders", "mobility.builders.exceptions", "mobility.ir", "mobility.ir.employee", "mobility.ir.scenario_data", "mobility.ir.site", "os", "re", "types", "typing_extensions", "unittest"], "hash": "0a8808345a19c00fae9ae5d615253a26ebf3e1ff", "id": "mobility.tests.builders.test_csv_multi_reader", "ignore_all": false, "interface_hash": "bc59fdeb1b9ef03a0b8b7c214e5d72143eb3b2db", "mtime": 1753175228, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\builders\\test_csv_multi_reader.py", "plugin_data": null, "size": 30975, "suppressed": [], "version_id": "1.16.1"}
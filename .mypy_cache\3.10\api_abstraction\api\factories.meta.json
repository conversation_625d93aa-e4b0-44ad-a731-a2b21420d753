{"data_mtime": 1752056505, "dep_lines": [3, 4, 5, 8, 9, 10, 11, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.fallback_travel_time_api", "api_abstraction.api.fastest_choice_travel_time_api", "api_abstraction.api.geocode_api", "api_abstraction.api.modal_travel_time_api", "api_abstraction.api.travel_time_api", "api_abstraction.beautiful_soup.api", "api_abstraction.flight.flight_travel_time", "api_abstraction.geopy.arcgis", "api_abstraction.geopy.ban_france", "api_abstraction.geopy.here", "api_abstraction.google.api", "api_abstraction.here.api", "api_abstraction.internal.travel_time", "api_abstraction.lux_geocode.lux_geocode", "api_abstraction.manual.manual_geocoder", "api_abstraction.navitia.api", "api_abstraction.otp.api", "api_abstraction.trivial.trivial_geocoder", "api_abstraction.trivial.trivial_travel_time", "mobility.ir.transport", "typing", "builtins", "_frozen_importlib", "abc", "api_abstraction.api.api", "api_abstraction.beautiful_soup", "api_abstraction.flight", "api_abstraction.geopy", "api_abstraction.google", "api_abstraction.here", "api_abstraction.internal", "api_abstraction.lux_geocode", "api_abstraction.manual", "api_abstraction.navitia", "api_abstraction.otp", "api_abstraction.trivial", "bs4", "bs4.element", "enum", "internal_api", "internal_api.geo_graph", "mobility", "mobility.ir", "mobility.ir.geo_study", "typing_extensions"], "hash": "a498fc4ad9952ef3c617f5ca74e10ac51866b4f7", "id": "api_abstraction.api.factories", "ignore_all": false, "interface_hash": "81ed7ab87f1b2f89560feaa537baf790328cf341", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\api\\factories.py", "plugin_data": null, "size": 11205, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "mobility.ir.indicators.carpool_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CarpoolIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators", "name": "CarpoolIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 114, "name": "average_travel_time", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 115, "name": "carbon_emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 116, "name": "nb_car_parking_saved", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 117, "name": "carpool_groups", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 118, "name": "clusters", "type": {".class": "Instance", "args": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 119, "name": "not_clustered", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 120, "name": "drivers", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 121, "name": "passengers", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 122, "name": "carpool_group_id_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 123, "name": "costs", "type": "mobility.ir.cost.IndividualCosts"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 124, "name": "zfe_impact_calendar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 125, "name": "employees_count_per_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 126, "name": "employees_count_per_profile", "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.carpool_indicators", "mro": ["mobility.ir.indicators.carpool_indicators.CarpoolIndicators", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "average_travel_time", "carbon_emission", "nb_car_parking_saved", "carpool_groups", "clusters", "not_clustered", "drivers", "passengers", "carpool_group_id_by_employee", "costs", "zfe_impact_calendar", "employees_count_per_mode", "employees_count_per_profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "average_travel_time", "carbon_emission", "nb_car_parking_saved", "carpool_groups", "clusters", "not_clustered", "drivers", "passengers", "carpool_group_id_by_employee", "costs", "zfe_impact_calendar", "employees_count_per_mode", "employees_count_per_profile"], "arg_types": ["mobility.ir.indicators.carpool_indicators.CarpoolIndicators", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CarpoolIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "average_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbon_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_car_parking_saved"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carpool_groups"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clusters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "not_clustered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "drivers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "passengers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carpool_group_id_by_employee"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "costs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zfe_impact_calendar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_profile"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["average_travel_time", "carbon_emission", "nb_car_parking_saved", "carpool_groups", "clusters", "not_clustered", "drivers", "passengers", "carpool_group_id_by_employee", "costs", "zfe_impact_calendar", "employees_count_per_mode", "employees_count_per_profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["average_travel_time", "carbon_emission", "nb_car_parking_saved", "carpool_groups", "clusters", "not_clustered", "drivers", "passengers", "carpool_group_id_by_employee", "costs", "zfe_impact_calendar", "employees_count_per_mode", "employees_count_per_profile"], "arg_types": ["mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CarpoolIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["average_travel_time", "carbon_emission", "nb_car_parking_saved", "carpool_groups", "clusters", "not_clustered", "drivers", "passengers", "carpool_group_id_by_employee", "costs", "zfe_impact_calendar", "employees_count_per_mode", "employees_count_per_profile"], "arg_types": ["mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CarpoolIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "average_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.average_travel_time", "name": "average_travel_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "carbon_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.carbon_emission", "name": "carbon_emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "carpool_group_id_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.carpool_group_id_by_employee", "name": "carpool_group_id_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "carpool_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.carpool_groups", "name": "carpool_groups", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.clusters", "name": "clusters", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "costs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.costs", "name": "costs", "setter_type": null, "type": "mobility.ir.cost.IndividualCosts"}}, "drivers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.drivers", "name": "drivers", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "employees_count_per_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.employees_count_per_mode", "name": "employees_count_per_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_count_per_profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.employees_count_per_profile", "name": "employees_count_per_profile", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "nb_car_parking_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.nb_car_parking_saved", "name": "nb_car_parking_saved", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "not_clustered": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.not_clustered", "name": "not_clustered", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "passengers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.passengers", "name": "passengers", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "zfe_impact_calendar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.zfe_impact_calendar", "name": "zfe_impact_calendar", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.carpool_indicators.CarpoolIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CarpoolingCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster", "name": "CarpoolingCluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "members", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "site", "type": "mobility.ir.site.GeoSite"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "durations", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 37, "name": "emissions", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "distances", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "direct_durations", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "direct_emissions", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "direct_distances", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "duration_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "emission_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "distance_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "direct_duration_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "direct_emission_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "direct_distance_by_employee", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.carpool_indicators", "mro": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "members", "site", "durations", "emissions", "distances", "direct_durations", "direct_emissions", "direct_distances"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "members", "site", "durations", "emissions", "distances", "direct_durations", "direct_emissions", "direct_distances"], "arg_types": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.site.GeoSite", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CarpoolingCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "members"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "site"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "durations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emissions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distances"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "direct_durations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "direct_emissions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "direct_distances"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of CarpoolingCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["members", "site", "durations", "emissions", "distances", "direct_durations", "direct_emissions", "direct_distances", "duration_by_employee", "emission_by_employee", "distance_by_employee", "direct_duration_by_employee", "direct_emission_by_employee", "direct_distance_by_employee"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["members", "site", "durations", "emissions", "distances", "direct_durations", "direct_emissions", "direct_distances", "duration_by_employee", "emission_by_employee", "distance_by_employee", "direct_duration_by_employee", "direct_emission_by_employee", "direct_distance_by_employee"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.site.GeoSite", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CarpoolingCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["members", "site", "durations", "emissions", "distances", "direct_durations", "direct_emissions", "direct_distances", "duration_by_employee", "emission_by_employee", "distance_by_employee", "direct_duration_by_employee", "direct_emission_by_employee", "direct_distance_by_employee"], "arg_types": [{".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.site.GeoSite", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CarpoolingCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.carpool_indicators.CarpoolingCluster"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__post_init__ of CarpoolingCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "direct_distance_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_distance_by_employee", "name": "direct_distance_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "direct_distances": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_distances", "name": "direct_distances", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "direct_duration_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_duration_by_employee", "name": "direct_duration_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "direct_durations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_durations", "name": "direct_durations", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "direct_emission_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_emission_by_employee", "name": "direct_emission_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "direct_emissions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.direct_emissions", "name": "direct_emissions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "distance_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.distance_by_employee", "name": "distance_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "distances": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.distances", "name": "distances", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "duration_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.duration_by_employee", "name": "duration_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "durations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.durations", "name": "durations", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "emission_by_employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.emission_by_employee", "name": "emission_by_employee", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "emissions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.emissions", "name": "emissions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "members": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.members", "name": "members", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "site": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.site", "name": "site", "setter_type": null, "type": "mobility.ir.site.GeoSite"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.carpool_indicators.CarpoolingCluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommuterProfile": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.CommuterProfile", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IndividualCosts": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.IndividualCosts", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "ZFEImpactCalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.carpool_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}}, "path": "mobility\\ir\\indicators\\carpool_indicators.py"}
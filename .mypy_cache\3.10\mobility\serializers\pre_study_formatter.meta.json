{"data_mtime": 1752154445, "dep_lines": [3, 4, 8, 11, 15, 12, 14, 16, 17, 18, 19, 13, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.carpool", "mobility.converters.indicators.mode_shift_scenario", "mobility.converters.indicators.remote_scenario", "mobility.converters.indicators.scenario", "mobility.ir.indicators.coworking_indicators", "mobility.converters.scenario", "mobility.ir.employee", "mobility.ir.site", "mobility.ir.study", "mobility.ir.transport", "mobility.ir.webstudy", "mobility.funky", "mobility.quantity", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.new_indicators", "mobility.ir.scenario_data", "mobility.ir.study_types", "typing_extensions"], "hash": "7dc31fc4ff84c28be30627ef8a34b5dcd82fa858", "id": "mobility.serializers.pre_study_formatter", "ignore_all": false, "interface_hash": "59725762d515597581abb00f56bbb412651b109a", "mtime": 1722327418, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\pre_study_formatter.py", "plugin_data": null, "size": 3257, "suppressed": [], "version_id": "1.16.1"}
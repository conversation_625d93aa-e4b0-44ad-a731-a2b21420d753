{"data_mtime": 1751444411, "dep_lines": [5, 4, 6, 11, 13, 14, 3, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.company_potential", "mobility.ir.onboarding_form_step", "mobility.ir.plan", "mobility.repositories.abstract_repositories", "mobility.use_cases.base_use_case", "mobility.funky", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.zfe", "mobility.repositories", "typing_extensions"], "hash": "de55e1100f9596f7541758ffcc5b4f1ef02be39f", "id": "mobility.use_cases.compute_onboarding_results", "ignore_all": false, "interface_hash": "d72a64f4a05fb2353adf8193d063654990c0551e", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\use_cases\\compute_onboarding_results.py", "plugin_data": null, "size": 22559, "suppressed": [], "version_id": "1.16.1"}
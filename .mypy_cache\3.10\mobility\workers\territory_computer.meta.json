{"data_mtime": 1753176774, "dep_lines": [5, 6, 15, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.mode_constraints", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.territory", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.country", "mobility.ir.employee", "mobility.ir.poi", "mobility.ir.scenario_data", "typing_extensions"], "hash": "43453b4b45e9337e3dca839c61df25db426fc067", "id": "mobility.workers.territory_computer", "ignore_all": false, "interface_hash": "a7785983aa2a91ffc6238e9e20e57c1f01d10cf6", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\territory_computer.py", "plugin_data": null, "size": 8469, "suppressed": ["geopy.distance"], "version_id": "1.16.1"}
{"data_mtime": 1751992444, "dep_lines": [5, 6, 4, 1, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mobility.ir.error", "mobility.ir.geo_study", "mobility.funky", "dataclasses", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "be801dde14f5ce51890a7a0a227bc4e801a7268a", "id": "mobility.ir.site", "ignore_all": false, "interface_hash": "aa0dce730bdef57b33b0467d264b41eeda736fa4", "mtime": 1753175227, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\site.py", "plugin_data": null, "size": 4086, "suppressed": [], "version_id": "1.16.1"}
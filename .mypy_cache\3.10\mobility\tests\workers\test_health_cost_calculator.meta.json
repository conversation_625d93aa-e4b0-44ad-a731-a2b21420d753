{"data_mtime": **********, "dep_lines": [5, 6, 8, 7, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.transport", "mobility.workers.health_cost_calculator", "mobility.quantity", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_typeshed", "abc", "enum", "mobility.ir", "mobility.workers", "typing_extensions"], "hash": "a6f051d3c8bf02785bb8a59f36d8e38f55e6e5ba", "id": "mobility.tests.workers.test_health_cost_calculator", "ignore_all": false, "interface_hash": "a008fa51c9cc321d0c64d8afe6e7782dfcb86a45", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_health_cost_calculator.py", "plugin_data": null, "size": 4367, "suppressed": [], "version_id": "1.16.1"}
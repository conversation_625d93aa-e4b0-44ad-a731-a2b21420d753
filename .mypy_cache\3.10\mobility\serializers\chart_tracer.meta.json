{"data_mtime": 1752154451, "dep_lines": [30, 33, 36, 37, 38, 39, 42, 64, 65, 68, 69, 72, 75, 76, 79, 80, 12, 21, 22, 23, 29, 43, 44, 45, 46, 56, 57, 58, 83, 84, 85, 86, 87, 88, 89, 13, 47, 1, 2, 3, 4, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 9, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["mobility.ir.indicators.bicycle_infrastructure_indicators", "mobility.ir.indicators.car_infrastructure_indicators", "mobility.ir.indicators.indicators", "mobility.ir.indicators.inter_scenario_indicators", "mobility.ir.indicators.poi_scenario_indicators", "mobility.ir.indicators.pt_infrastructure_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.serializers.charters.cost_grapher", "mobility.serializers.charters.executive_summary_table_grapher", "mobility.serializers.charters.flow_chart_grapher", "mobility.serializers.charters.multi_bar_graph_with_threshold_grapher", "mobility.serializers.charters.multiple_quantity_delta_grapher", "mobility.serializers.charters.pie_grapher", "mobility.serializers.charters.quantity_delta_card_grapher", "mobility.serializers.charters.quantity_delta_grapher", "mobility.serializers.charters.workforce_by_modes_grapher", "mobility.converters.accessibility", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.ir.map_elements", "mobility.ir.study", "mobility.ir.transport", "mobility.ir.work_mode", "mobility.serializers.arcgis_map_maker", "mobility.serializers.base_map_maker", "mobility.serializers.chart_writer", "mobility.serializers.illustrator", "mobility.serializers.plotly_map_maker", "mobility.serializers.plotly_theme", "mobility.workers.color_picker", "mobility.workers.executive_summary_computer", "mobility.workers.ideal_scenario", "mobility.workers.zfe_impact_computer", "mobility.funky", "mobility.quantity", "logging", "math", "collections", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.converters", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.country", "mobility.ir.executive_summary", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.clustering_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.critical_cases", "mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.staggered_hours_indicators", "mobility.ir.indicators.study_indicators", "mobility.ir.infrastructure", "mobility.ir.plan", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.territory", "mobility.serializers.charters", "mobility.serializers.charters.svg_interface", "mobility.serializers.charters.svg_map_addons", "mobility.workers", "types", "typing_extensions"], "hash": "59d9a8c427734cbece91d50a5d3829248ff184b2", "id": "mobility.serializers.chart_tracer", "ignore_all": false, "interface_hash": "5681485ab38ba532a8cd92f7686795f464f00c1a", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\chart_tracer.py", "plugin_data": null, "size": 154686, "suppressed": ["plotly.subplots", "shapely.geometry", "plotly"], "version_id": "1.16.1"}
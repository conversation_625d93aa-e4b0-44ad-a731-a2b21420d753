{".class": "MypyFile", "_fullname": "mobility.workers.cost_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Address": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Address", "kind": "Gdef"}, "Commute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Commute", "kind": "Gdef"}, "ConsolidatedCommute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ConsolidatedCommute", "kind": "Gdef"}, "Cost": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.Cost", "kind": "Gdef"}, "CostComputer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.workers.cost_computer.CostComputer", "name": "CostComputer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.workers.cost_computer.CostComputer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.workers.cost_computer", "mro": ["mobility.workers.cost_computer.CostComputer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.cost_computer.CostComputer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CostComputer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_bicycle_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_bicycle_costs", "name": "_compute_bicycle_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_bicycle_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_bicycle_like_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_bicycle_like_costs", "name": "_compute_bicycle_like_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}, "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_bicycle_like_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_bicycle_parking_commute_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "site"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_bicycle_parking_commute_costs", "name": "_compute_bicycle_parking_commute_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "site"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_bicycle_parking_commute_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_bicycle_ride_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_bicycle_ride_costs", "name": "_compute_bicycle_ride_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_bicycle_ride_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_car_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "emission", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_car_costs", "name": "_compute_car_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "emission", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_car_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_car_like_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_car_like_costs", "name": "_compute_car_like_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}, "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_car_like_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_car_parking_commute_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "site"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_car_parking_commute_costs", "name": "_compute_car_parking_commute_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "site"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_car_parking_commute_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_car_ride_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "distance", "emission", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_car_ride_costs", "name": "_compute_car_ride_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "distance", "emission", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_car_ride_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_common_employee_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_common_employee_costs", "name": "_compute_common_employee_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "duration"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_common_employee_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_pt_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "emission", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_pt_costs", "name": "_compute_pt_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "duration", "distance", "emission", "origin", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_pt_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_pt_like_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_pt_like_costs", "name": "_compute_pt_like_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}, "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_pt_like_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_pt_ride_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "distance", "emission", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_pt_ride_costs", "name": "_compute_pt_ride_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "distance", "emission", "origin", "destination"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.geo_study.Address", "mobility.ir.geo_study.Address"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_pt_ride_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_remote_commutes_cost_on_single_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "site", "commutes_by_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_remote_commutes_cost_on_single_site", "name": "_compute_remote_commutes_cost_on_single_site", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "site", "commutes_by_mode"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.ir.site.GeoSite", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedCommute"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_remote_commutes_cost_on_single_site of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.cost.Costs"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_walk_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_walk_costs", "name": "_compute_walk_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commute"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}, "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_walk_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_walk_ride_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer._compute_walk_ride_costs", "name": "_compute_walk_ride_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "distance"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_walk_ride_costs of CostComputer", "ret_type": {".class": "Instance", "args": ["mobility.ir.cost.Cost"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accidents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.cost_computer.CostComputer.accidents", "name": "accidents", "setter_type": null, "type": "mobility.workers.work_accident_cost_calculator.WorkAccidentCostCalculator"}}, "compute_commute_cost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "commute", "has_mobility_benefits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_commute_cost", "name": "compute_commute_cost", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "commute", "has_mobility_benefits"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.CoworkingSite"], "uses_pep604_syntax": false}, "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Commute"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_commute_cost of CostComputer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.cost.Costs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_commutes_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "commutes", "has_mobility_benefits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_commutes_costs", "name": "compute_commutes_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "commutes", "has_mobility_benefits"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedCommute"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_commutes_costs of CostComputer", "ret_type": "mobility.ir.cost.IndividualCosts", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_cost_for_carpooling_employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_commute", "employee_commute", "ride_share_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_cost_for_carpooling_employee", "name": "compute_cost_for_carpooling_employee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_commute", "employee_commute", "ride_share_factor"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedCommute"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedCommute"}, "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_cost_for_carpooling_employee of CostComputer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.cost.Costs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_environment_damage_cost_for_car": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "emission"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_environment_damage_cost_for_car", "name": "compute_environment_damage_cost_for_car", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "emission"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_environment_damage_cost_for_car of CostComputer", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_environment_damage_cost_for_pt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "emission"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_environment_damage_cost_for_pt", "name": "compute_environment_damage_cost_for_pt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "distance", "emission"], "arg_types": ["mobility.workers.cost_computer.CostComputer", "mobility.quantity.Quantity", "mobility.quantity.Quantity"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_environment_damage_cost_for_pt of CostComputer", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_remote_commutes_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commutes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.cost_computer.CostComputer.compute_remote_commutes_costs", "name": "compute_remote_commutes_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commutes"], "arg_types": ["mobility.workers.cost_computer.CostComputer", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.ConsolidatedCommute"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_remote_commutes_costs of CostComputer", "ret_type": "mobility.ir.cost.IndividualCosts", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "employee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.cost_computer.CostComputer.employee", "name": "employee", "setter_type": null, "type": "mobility.workers.employee_cost_calculator.EmployeeCostCalculator"}}, "health": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.cost_computer.CostComputer.health", "name": "health", "setter_type": null, "type": "mobility.workers.health_cost_calculator.HealthCostCalculator"}}, "infrastructure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.cost_computer.CostComputer.infrastructure", "name": "infrastructure", "setter_type": null, "type": "mobility.workers.infrastructure_cost_calculator.InfrastructureCostCalculator"}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.workers.cost_computer.CostComputer.transport", "name": "transport", "setter_type": null, "type": "mobility.workers.transport_cost_calculator.TransportCostCalculator"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.cost_computer.CostComputer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.workers.cost_computer.CostComputer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CostKind": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostKind", "kind": "Gdef"}, "CostPayer": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostPayer", "kind": "Gdef"}, "Costs": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.Costs", "kind": "Gdef"}, "CoworkingSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.CoworkingSite", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EmployeeCostCalculator": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.employee_cost_calculator.EmployeeCostCalculator", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "HealthCostCalculator": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.health_cost_calculator.HealthCostCalculator", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IndividualCosts": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.IndividualCosts", "kind": "Gdef"}, "InfrastructureCostCalculator": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.infrastructure_cost_calculator.InfrastructureCostCalculator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModalCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.ModalCommuteData", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "TransportCostCalculator": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.transport_cost_calculator.TransportCostCalculator", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WorkAccidentCostCalculator": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.work_accident_cost_calculator.WorkAccidentCostCalculator", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.cost_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adimensional": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.adimensional", "kind": "Gdef"}, "compute_number_of_remote_workers_to_save_a_spot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.cost_computer.compute_number_of_remote_workers_to_save_a_spot", "name": "compute_number_of_remote_workers_to_save_a_spot", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_number_of_remote_workers_to_save_a_spot", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_remote_employee_trip_savings_factor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.cost_computer.compute_remote_employee_trip_savings_factor", "name": "compute_remote_employee_trip_savings_factor", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_remote_employee_trip_savings_factor", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_remote_savings_from_saved_spots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nb_remote_workers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.cost_computer.compute_remote_savings_from_saved_spots", "name": "compute_remote_savings_from_saved_spots", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["nb_remote_workers"], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_remote_savings_from_saved_spots", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "euros": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.euros", "kind": "Gdef"}, "gramEC": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.gramEC", "kind": "Gdef"}, "meters": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.meters", "kind": "Gdef"}, "remote_trip": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.remote_trip", "kind": "Gdef"}, "remote_trip_saved": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.remote_trip_saved", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}, "trip": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.trip", "kind": "Gdef"}, "yearly": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.yearly", "kind": "Gdef"}}, "path": "mobility\\workers\\cost_computer.py"}
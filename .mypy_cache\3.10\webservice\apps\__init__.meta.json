{"data_mtime": 1752154451, "dep_lines": [10, 10, 10, 10, 10, 17, 10, 19, 1, 2, 3, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["webservice.apps.routes.main", "webservice.apps.routes.mode_shift_readiness_classification", "webservice.apps.routes.onboarding", "webservice.apps.routes.queries", "webservice.apps.routes.simone", "webservice.apps.store.metadata_store", "webservice.apps.routes", "webservice.apps.db", "sys", "uuid", "typing", "flask", "jinja2", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "configparser", "flask.app", "flask.blueprints", "flask.config", "flask.ctx", "flask.globals", "flask.helpers", "jinja2.runtime", "typing_extensions", "webservice.apps.store"], "hash": "8f12d178134be558b677d947e91fd15569ca720d", "id": "webservice.apps", "ignore_all": false, "interface_hash": "fd27a80dd82ce7235f0b5cb5e0114a6963aaea0f", "mtime": 1726151661, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\__init__.py", "plugin_data": null, "size": 3229, "suppressed": ["flask_cors"], "version_id": "1.16.1"}
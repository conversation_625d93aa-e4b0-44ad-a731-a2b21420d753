{"data_mtime": 1752154445, "dep_lines": [5, 12, 11, 13, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.mode_shift_scenario", "mobility.ir.transport", "mobility.funky", "mobility.quantity", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "re", "typing_extensions"], "hash": "0c4299e4811e46d29a0d7c460d94a3340750f18f", "id": "mobility.tests.converters.indicators.test_mode_shift_scenario", "ignore_all": false, "interface_hash": "8ca129c5b335f71ed2779629815a3228e5c76e5b", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\converters\\indicators\\test_mode_shift_scenario.py", "plugin_data": null, "size": 18301, "suppressed": [], "version_id": "1.16.1"}
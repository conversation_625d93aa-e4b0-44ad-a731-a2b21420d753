{"data_mtime": 1751444475, "dep_lines": [5, 6, 7, 8, 10, 11, 12, 13, 14, 1, 2, 3, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "mobility.builders.territory_database", "mobility.builders.zfe_data", "mobility.ir.company_potential", "mobility.ir.geo_study", "mobility.ir.zfe", "json", "os", "typing", "mobility", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "configparser", "io", "json.decoder", "json.encoder", "mobility.builders", "mobility.ir", "ntpath", "typing_extensions"], "hash": "f44b9f2b4192bb441c78da592ae00a8cd0fad393", "id": "mobility.repositories.simulated_company", "ignore_all": false, "interface_hash": "7da4b5a1ee4617c57acb8e2a6b0c09f76370ce33", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\repositories\\simulated_company.py", "plugin_data": null, "size": 3989, "suppressed": [], "version_id": "1.16.1"}
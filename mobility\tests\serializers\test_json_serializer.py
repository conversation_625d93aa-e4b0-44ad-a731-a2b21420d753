from pathlib import Path
from typing import Any

from mobility.funky import ImmutableDict
from mobility.ir.commute_data import ModalCommuteData, TimedCommuteData
from mobility.ir.employee import FailedGeoEmployee, GeoEmployee
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.infrastructure import PublicTransportStop
from mobility.ir.scenario_data import ScenarioData
from mobility.ir.site import FailedGeoSite, GeoSite
from mobility.ir.study import (
    ConsolidatedStudy,
    PublicTransportStationsScenario,
    TimedTransportStopCommute,
)
from mobility.ir.study_types import Scenarios
from mobility.ir.transport import TransportMode, TransportType
from mobility.quantity import minutes
from mobility.serializers.json_serializer import StudyJsonSerializer


class TestStudyJsonSerializer:
    def test_should_create_file(
        self, tmp_path: Path, consolidated_study: ConsolidatedStudy
    ) -> None:
        my_super_path = tmp_path / "dossier_quexiste_po"
        serializer = StudyJsonSerializer(str(my_super_path), consolidated_study, "")

        serializer.serialize()

        file_path = my_super_path / "Diagnostic_mobilité_Modelity_.json"
        assert file_path.exists()


class TestStudyEncoder:
    def test_should_encode_consolidated_study(
        self,
        consolidated_study_factory: Any,
        timed_infrastructure: Any,
        poi_scenario: Any,
    ) -> None:
        consolidated_study = consolidated_study_factory(
            poi_scenarios=Scenarios(frozenset([poi_scenario])),
            infrastructure=timed_infrastructure(),
        )

        encoded = consolidated_study.to_json()

        assert encoded == {
            "version": "v18",
            "data": {
                "company": "Modelity",
                "territory": "LYON",
                "mission_id": "YYMXXX.V",
                "arrival_time": "(8,30)",
                "agency": "",
                "constraints": {"duration": {}},
            },
            "scenarios": [
                {
                    "data": "1",
                    "commutes": [
                        {
                            "origin": "1",
                            "destination": "1",
                            "commute_data": {
                                "best_mode": TransportMode.WALK,
                                "duration": {
                                    TransportMode.BICYCLE: 1600,
                                    TransportMode.CAR: 800,
                                    TransportMode.PUBLIC_TRANSPORT: 1200,
                                    TransportMode.WALK: 3200,
                                    TransportMode.CARPOOLING: 900,
                                    TransportMode.ELECTRIC_BICYCLE: 1500,
                                    TransportMode.ELECTRIC_CAR: 850,
                                    TransportMode.MOTORCYCLE: 700,
                                    TransportMode.AIRPLANE: 3600,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 750,
                                    TransportMode.FAST_BICYCLE: 1400,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
                                },
                                "distance": {
                                    TransportMode.BICYCLE: 1800,
                                    TransportMode.CAR: 2000,
                                    TransportMode.PUBLIC_TRANSPORT: 2100,
                                    TransportMode.WALK: 1800,
                                    TransportMode.CARPOOLING: 2100,
                                    TransportMode.ELECTRIC_BICYCLE: 1800,
                                    TransportMode.ELECTRIC_CAR: 2000,
                                    TransportMode.MOTORCYCLE: 2000,
                                    TransportMode.AIRPLANE: 20000,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 2000,
                                    TransportMode.FAST_BICYCLE: 2000,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
                                },
                                "emission": {
                                    TransportMode.BICYCLE: 0,
                                    TransportMode.CAR: 4800,
                                    TransportMode.PUBLIC_TRANSPORT: 1000,
                                    TransportMode.WALK: 0,
                                    TransportMode.CARPOOLING: 2400,
                                    TransportMode.ELECTRIC_BICYCLE: 10,
                                    TransportMode.ELECTRIC_CAR: 1200,
                                    TransportMode.MOTORCYCLE: 4800,
                                    TransportMode.AIRPLANE: 10000,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 15,
                                    TransportMode.FAST_BICYCLE: 10,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
                                },
                                "alternative_arrival_time": {},
                            },
                        }
                    ],
                }
            ],
            "poi_scenarios": [
                {
                    "data": "1",
                    "commutes": [
                        {
                            "origin": "1",
                            "destination": {
                                "name": "Gare Part-Dieu",
                                "coordinates": {
                                    "latitude": 45.760749,
                                    "longitude": 4.861171,
                                },
                            },
                            "commute_data": {
                                "duration": {
                                    TransportMode.BICYCLE: 1600,
                                    TransportMode.CAR: 800,
                                    TransportMode.PUBLIC_TRANSPORT: 1200,
                                    TransportMode.WALK: 3200,
                                    TransportMode.CARPOOLING: 900,
                                    TransportMode.ELECTRIC_BICYCLE: 1500,
                                    TransportMode.ELECTRIC_CAR: 850,
                                    TransportMode.MOTORCYCLE: 700,
                                    TransportMode.AIRPLANE: 3600,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 750,
                                    TransportMode.FAST_BICYCLE: 1400,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
                                },
                                "distance": {
                                    TransportMode.BICYCLE: 1800,
                                    TransportMode.CAR: 2000,
                                    TransportMode.PUBLIC_TRANSPORT: 2100,
                                    TransportMode.WALK: 1800,
                                    TransportMode.CARPOOLING: 2100,
                                    TransportMode.ELECTRIC_BICYCLE: 1800,
                                    TransportMode.ELECTRIC_CAR: 2000,
                                    TransportMode.MOTORCYCLE: 2000,
                                    TransportMode.AIRPLANE: 20000,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 2000,
                                    TransportMode.FAST_BICYCLE: 2000,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
                                },
                                "emission": {
                                    TransportMode.BICYCLE: 0,
                                    TransportMode.CAR: 4800,
                                    TransportMode.PUBLIC_TRANSPORT: 1000,
                                    TransportMode.WALK: 0,
                                    TransportMode.CARPOOLING: 2400,
                                    TransportMode.ELECTRIC_BICYCLE: 10,
                                    TransportMode.ELECTRIC_CAR: 1200,
                                    TransportMode.MOTORCYCLE: 4800,
                                    TransportMode.AIRPLANE: 10000,
                                    TransportMode.ELECTRIC_MOTORCYCLE: 15,
                                    TransportMode.FAST_BICYCLE: 10,
                                    TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
                                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
                                },
                                "alternative_arrival_time": {},
                            },
                        }
                    ],
                }
            ],
            "time_failed": [],
            "geocode_failed_both": [],
            "geocode_failed_employees": [],
            "geocode_failed_sites": [],
            "public_transport_stations_scenario": {"commutes": [], "data": None},
            "bicycle_amenity_scenario": {"commutes": [], "data": None},
            "car_amenity_scenario": {"commutes": [], "data": None},
            "employees": {
                "1": {
                    "id": 1,
                    "address": "emlpoyee_address_01",
                    "address_details": {
                        "full": "fake address",
                        "normalized": "normalized fake address",
                        "city": "fake city name",
                        "postcode": "12123",
                        "citycode": "12345",
                        "coordinates": {"latitude": 2.0, "longitude": 1.0},
                    },
                    "coordinates": {"latitude": 2.0, "longitude": 1.0},
                    "nickname": "employee_01",
                    "transport_mode": TransportMode.WALK,
                    "remote": True,
                    "crit_air": "CRITAIR_NON_CLASSE",
                }
            },
            "scenarios_data": {
                "1": {"id": 1, "is_present": True, "nickname": "trivial"}
            },
            "sites": {
                "1": {
                    "address": "site_address_01",
                    "address_details": {
                        "full": "fake address",
                        "normalized": "normalized fake address",
                        "city": "fake city name",
                        "postcode": "12123",
                        "citycode": "12345",
                        "coordinates": {"latitude": 2.0, "longitude": 1.0},
                    },
                    "coordinates": {"latitude": 46.0, "longitude": 5.0},
                    "id": 1,
                    "nickname": "site_01",
                }
            },
            "isochrones": {},
            "public_transport_stops": [],
            "public_transport_lines": [],
            "bicycle_amenities": [],
            "bicycle_lines": [],
            "car_amenities": [],
            "car_ways": [],
            "coworking_scenario": {"data": None, "commutes": []},
            "coworking_failed_scenario": {"data": None, "commutes": []},
        }

    def test_should_jsonify_isochrones(
        self, consolidated_study: ConsolidatedStudy, geo_site_factory: Any
    ) -> None:
        duration = 30 * minutes
        isochrones = {
            geo_site_factory(id=1): {
                TransportMode.PUBLIC_TRANSPORT: {
                    duration: {
                        "type": "MultiPolygon",
                        "coordinates": [[[[46.0, 5.0]]]],
                    }
                }
            },
            geo_site_factory(id=2): {
                TransportMode.PUBLIC_TRANSPORT: {
                    duration: {
                        "type": "MultiPolygon",
                        "coordinates": [[[[45.0, 4.0]]]],
                    }
                },
            },
        }

        isochrones_json = consolidated_study.jsonify_isochrone(isochrones)

        assert isochrones_json == {
            "1": {
                "PUBLIC_TRANSPORT": {
                    1800: {"type": "MultiPolygon", "coordinates": [[[[46.0, 5.0]]]]},
                }
            },
            "2": {
                "PUBLIC_TRANSPORT": {
                    1800: {"type": "MultiPolygon", "coordinates": [[[[45.0, 4.0]]]]},
                }
            },
        }

    def test_should_jsonify_public_transport_stations_scenario(
        self,
        consolidated_study,
        public_transport_stations_scenario_factory,
        geo_site_factory,
    ) -> None:
        site = geo_site_factory(
            id=1, nickname="mySite", address="unknown", coordinates=GeoCoordinates(1, 1)
        )
        stop = PublicTransportStop(
            name="part-dieu",
            coordinates=GeoCoordinates(2, 2),
            lines_connected=frozenset({"A"}),
            lines_kinds=frozenset({TransportType.TRAIN}),
        )
        data = TimedCommuteData(
            duration=ImmutableDict({TransportMode.WALK: 200}),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = [TimedTransportStopCommute(site, stop, data)]
        scenario = PublicTransportStationsScenario(None, frozenset(commutes))

        scenario_json = consolidated_study.jsonify_public_transport_stations_scenario(
            scenario
        ).to_json()

        assert scenario_json == {
            "commutes": [
                {
                    "commute_data": {
                        "distance": {},
                        "duration": {"WALK": 200},
                        "emission": {},
                        "alternative_arrival_time": {},
                    },
                    "destination": {
                        "coordinates": {"latitude": 2, "longitude": 2},
                        "lines_connected": ("A",),
                        "lines_kinds": ("train",),
                        "name": "part-dieu",
                    },
                    "origin": "1",
                }
            ],
            "data": None,
        }

    def test_commute_data_should_be_jsonable(
        self,
        modal_commute_data: ModalCommuteData,
    ) -> None:
        encoded = modal_commute_data.to_json()

        assert encoded == ImmutableDict(
            {
                "best_mode": modal_commute_data.best_mode,
                "duration": ImmutableDict(
                    {
                        TransportMode.BICYCLE: 1600,
                        TransportMode.CAR: 800,
                        TransportMode.PUBLIC_TRANSPORT: 1200,
                        TransportMode.WALK: 3200,
                        TransportMode.CARPOOLING: 900,
                        TransportMode.ELECTRIC_BICYCLE: 1500,
                        TransportMode.ELECTRIC_CAR: 850,
                        TransportMode.MOTORCYCLE: 700,
                        TransportMode.AIRPLANE: 3600,
                        TransportMode.ELECTRIC_MOTORCYCLE: 750,
                        TransportMode.FAST_BICYCLE: 1400,
                        TransportMode.CAR_PUBLIC_TRANSPORT: 1000,
                        TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1100,
                    }
                ),
                "distance": ImmutableDict(
                    {
                        TransportMode.BICYCLE: 1800,
                        TransportMode.CAR: 2000,
                        TransportMode.PUBLIC_TRANSPORT: 2100,
                        TransportMode.WALK: 1800,
                        TransportMode.CARPOOLING: 2100,
                        TransportMode.ELECTRIC_BICYCLE: 1800,
                        TransportMode.ELECTRIC_CAR: 2000,
                        TransportMode.MOTORCYCLE: 2000,
                        TransportMode.AIRPLANE: 20000,
                        TransportMode.ELECTRIC_MOTORCYCLE: 2000,
                        TransportMode.FAST_BICYCLE: 2000,
                        TransportMode.CAR_PUBLIC_TRANSPORT: 2200,
                        TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2100,
                    }
                ),
                "emission": ImmutableDict(
                    {
                        TransportMode.BICYCLE: 0,
                        TransportMode.CAR: 4800,
                        TransportMode.PUBLIC_TRANSPORT: 1000,
                        TransportMode.WALK: 0,
                        TransportMode.CARPOOLING: 2400,
                        TransportMode.ELECTRIC_BICYCLE: 10,
                        TransportMode.ELECTRIC_CAR: 1200,
                        TransportMode.MOTORCYCLE: 4800,
                        TransportMode.AIRPLANE: 10000,
                        TransportMode.ELECTRIC_MOTORCYCLE: 15,
                        TransportMode.FAST_BICYCLE: 10,
                        TransportMode.CAR_PUBLIC_TRANSPORT: 1500,
                        TransportMode.BICYCLE_PUBLIC_TRANSPORT: 1000,
                    }
                ),
                "alternative_arrival_time": ImmutableDict(),
            }
        )

    def test_scenario_data_should_be_jsonable(
        self, scenario_data: ScenarioData
    ) -> None:
        encoded = scenario_data.to_json()

        assert encoded == ImmutableDict(
            {"id": 1, "nickname": "trivial", "is_present": True}
        )

    def test_geocoodinates_should_be_jsonable(self) -> None:
        coords = GeoCoordinates(latitude=4.5, longitude=12.4)

        encoded = coords.to_json()

        assert encoded == ImmutableDict({"latitude": 4.5, "longitude": 12.4})

    def test_geoemployee_should_be_jsonable(self, geo_employee: GeoEmployee) -> None:
        encoded = geo_employee.to_json()

        assert encoded == ImmutableDict(
            {
                "id": 1,
                "nickname": "employee_01",
                "address": "emlpoyee_address_01",
                "address_details": ImmutableDict(
                    {
                        "full": "fake address",
                        "normalized": "normalized fake address",
                        "city": "fake city name",
                        "postcode": "12123",
                        "citycode": "12345",
                        "coordinates": ImmutableDict(
                            {"latitude": 2.0, "longitude": 1.0}
                        ),
                    }
                ),
                "transport_mode": TransportMode.WALK,
                "remote": True,
                "coordinates": ImmutableDict({"latitude": 2.0, "longitude": 1.0}),
                "crit_air": "CRITAIR_NON_CLASSE",
            }
        )

    def test_failedgeoemployee_should_be_jsonable(
        self, failed_geo_employee: FailedGeoEmployee
    ) -> None:
        encoded = failed_geo_employee.to_json()

        assert encoded == ImmutableDict(
            {
                "id": 2,
                "nickname": "employee_02",
                "address": "emlpoyee_address_02",
                "transport_mode": TransportMode.WALK,
                "remote": True,
                "failure": ImmutableDict(
                    {"kind": "geocode error", "description": "unfortunate hun"}
                ),
            }
        )

    def test_geosite_should_be_jsonable(self, geo_site: GeoSite) -> None:
        encoded = geo_site.to_json()

        assert encoded == ImmutableDict(
            {
                "id": 1,
                "nickname": "site_01",
                "address": "site_address_01",
                "address_details": ImmutableDict(
                    {
                        "full": "fake address",
                        "normalized": "normalized fake address",
                        "city": "fake city name",
                        "postcode": "12123",
                        "citycode": "12345",
                        "coordinates": ImmutableDict(
                            {"latitude": 2.0, "longitude": 1.0}
                        ),
                    }
                ),
                "coordinates": ImmutableDict({"latitude": 46.0, "longitude": 5.0}),
            }
        )

    def test_failedgeosite_should_be_jsonable(
        self, failed_geo_site: FailedGeoSite
    ) -> None:
        encoded = failed_geo_site.to_json()

        assert encoded == ImmutableDict(
            {
                "id": 2,
                "nickname": "site_02",
                "address": "site_address_02",
                "failure": ImmutableDict(
                    {"kind": "geocode error", "description": "unfortunate hun"}
                ),
            }
        )

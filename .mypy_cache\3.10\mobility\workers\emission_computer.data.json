{".class": "MypyFile", "_fullname": "mobility.workers.emission_computer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.BaseCommuteData", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "TimedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedStudy", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.emission_computer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_missing_emissions_from_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.emission_computer.compute_missing_emissions_from_distance", "name": "compute_missing_emissions_from_distance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["commute_data"], "arg_types": ["mobility.ir.commute_data.BaseCommuteData"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_missing_emissions_from_distance", "ret_type": "mobility.ir.commute_data.BaseCommuteData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_missing_emissions_from_distance_with_alternatives": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.emission_computer.compute_missing_emissions_from_distance_with_alternatives", "name": "compute_missing_emissions_from_distance_with_alternatives", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["commute_data"], "arg_types": ["mobility.ir.commute_data.TimedCommuteData"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_missing_emissions_from_distance_with_alternatives", "ret_type": "mobility.ir.commute_data.TimedCommuteData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_mode_emission_from_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.emission_computer.compute_mode_emission_from_distance", "name": "compute_mode_emission_from_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "distance"], "arg_types": ["mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_mode_emission_from_distance", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill_missing_emissions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.workers.emission_computer.fill_missing_emissions", "name": "fill_missing_emissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.TimedStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fill_missing_emissions", "ret_type": "mobility.ir.study.TimedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "mobility\\workers\\emission_computer.py"}
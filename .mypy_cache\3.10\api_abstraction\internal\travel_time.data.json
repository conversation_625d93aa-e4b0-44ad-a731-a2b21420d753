{".class": "MypyFile", "_fullname": "api_abstraction.internal.travel_time", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EdgeDataKind": {".class": "SymbolTableNode", "cross_ref": "internal_api.geo_graph.EdgeDataKind", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "GeoGraph": {".class": "SymbolTableNode", "cross_ref": "internal_api.geo_graph.GeoGraph", "kind": "Gdef"}, "InternalTravelTimer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer", "name": "InternalTravelTimer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.internal.travel_time", "mro": ["api_abstraction.internal.travel_time.InternalTravelTimer", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "AUTO_CONGESTION_PROPERTY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.AUTO_CONGESTION_PROPERTY", "name": "AUTO_CONGESTION_PROPERTY", "setter_type": null, "type": "builtins.str"}}, "BICYCLE_SPEED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.BICYCLE_SPEED", "name": "BICYCLE_SPEED", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "CARPOOLING_EMISSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.CARPOOLING_EMISSION", "name": "CARPOOLING_EMISSION", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "CAR_EMISSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.CAR_EMISSION", "name": "CAR_EMISSION", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "CONNECTOR_CAR_SPEED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.CONNECTOR_CAR_SPEED", "name": "CONNECTOR_CAR_SPEED", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "ELECTRIC_BICYCLE_EMISSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.ELECTRIC_BICYCLE_EMISSION", "name": "ELECTRIC_BICYCLE_EMISSION", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "ELECTRIC_BICYCLE_SPEED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.ELECTRIC_BICYCLE_SPEED", "name": "ELECTRIC_BICYCLE_SPEED", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "ELECTRIC_CAR_EMISSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.ELECTRIC_CAR_EMISSION", "name": "ELECTRIC_CAR_EMISSION", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "MOTORCYCLE_EMISSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.MOTORCYCLE_EMISSION", "name": "MOTORCYCLE_EMISSION", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "WALK_SPEED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.WALK_SPEED", "name": "WALK_SPEED", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "token", "reporter", "geograph"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "token", "reporter", "geograph"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.str", "api_abstraction.api.event_reporter.EventReporter", {".class": "UnionType", "items": ["internal_api.geo_graph.GeoGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of InternalTravelTimer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_closest_graph_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "position", "n_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._closest_graph_nodes", "name": "_closest_graph_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "position", "n_nodes"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.geo_study.GeoCoordinates", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_closest_graph_nodes of InternalTravelTimer", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "mobility.ir.geo_study.GeoCoordinates", "mobility.quantity.Quantity"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_graph_connectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "nb_connectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_graph_connectors", "name": "_compute_graph_connectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "nb_connectors"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_graph_connectors of InternalTravelTimer", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "mobility.quantity.Quantity", "builtins.int", "mobility.quantity.Quantity"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_congested_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_congested_journey", "name": "_compute_shortest_congested_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_congested_journey of InternalTravelTimer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["mobility.quantity.Quantity", "mobility.quantity.Quantity"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_congested_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_congested_path", "name": "_compute_shortest_congested_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_congested_path of InternalTravelTimer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_distance_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_distance_journey", "name": "_compute_shortest_distance_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_distance_journey of InternalTravelTimer", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_distance_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_distance_path", "name": "_compute_shortest_distance_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_distance_path of InternalTravelTimer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_pt_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_pt_journey", "name": "_compute_shortest_pt_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_pt_journey of InternalTravelTimer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_shortest_pt_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "force_pt_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._compute_shortest_pt_path", "name": "_compute_shortest_pt_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "force_pt_path"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_shortest_pt_path of InternalTravelTimer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_direct_path_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._direct_path_distance", "name": "_direct_path_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origin", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_direct_path_distance of InternalTravelTimer", "ret_type": "mobility.quantity.Quantity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_along_path_edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "path", "p", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._get_along_path_edges", "name": "_get_along_path_edges", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "path", "p", "default"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "internal_api.geo_graph.EdgeDataKind", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.T", "id": -1, "name": "T", "namespace": "api_abstraction.internal.travel_time.InternalTravelTimer._get_along_path_edges", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_along_path_edges of InternalTravelTimer", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.T", "id": -1, "name": "T", "namespace": "api_abstraction.internal.travel_time.InternalTravelTimer._get_along_path_edges", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.T", "id": -1, "name": "T", "namespace": "api_abstraction.internal.travel_time.InternalTravelTimer._get_along_path_edges", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._time", "name": "_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time of InternalTravelTimer", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time_in_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin_node", "destination_node", "origin_distance", "destination_distance", "direct_distance", "mode", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer._time_in_graph", "name": "_time_in_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin_node", "destination_node", "origin_distance", "destination_distance", "direct_distance", "mode", "arrival_time"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "builtins.int", "builtins.int", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.quantity.Quantity", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time_in_graph of InternalTravelTimer", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "car_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.car_graph", "name": "car_graph", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "compute_detour_costs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.compute_detour_costs", "name": "compute_detour_costs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", {".class": "Instance", "args": ["mobility.ir.geo_study.GeoCoordinates"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_detour_costs of InternalTravelTimer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_detours": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.compute_detours", "name": "compute_detours", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "origins", "destination"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", {".class": "Instance", "args": ["mobility.ir.geo_study.GeoCoordinates"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_detours of InternalTravelTimer", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int", "api_abstraction.api.travel_time_api.JourneyAttribute"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.compute_isochrone", "name": "compute_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.territory.Territory", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isochrone of InternalTravelTimer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_zob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.compute_zob", "name": "compute_zob", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "width"], "arg_types": ["api_abstraction.internal.travel_time.InternalTravelTimer", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_zob of InternalTravelTimer", "ret_type": "api_abstraction.internal.travel_time.Zob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "geograph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.geograph", "name": "geograph", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "max_call_per_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.max_call_per_period", "name": "max_call_per_period", "setter_type": null, "type": "builtins.int"}}, "period_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.period_in_seconds", "name": "period_in_seconds", "setter_type": null, "type": "builtins.float"}}, "retry_delay_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.retry_delay_in_seconds", "name": "retry_delay_in_seconds", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.InternalTravelTimer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.internal.travel_time.InternalTravelTimer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Zob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.internal.travel_time.Zob", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.internal.travel_time.Zob", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.internal.travel_time", "mro": ["api_abstraction.internal.travel_time.Zob", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "graph", "optim_param", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.internal.travel_time.Zob.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "graph", "optim_param", "width"], "arg_types": ["api_abstraction.internal.travel_time.Zob", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_origin_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.internal.travel_time.Zob.from_origin_mapping", "name": "from_origin_mapping", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "length_frequencies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "api_abstraction.internal.travel_time.Zob.length_frequencies", "name": "length_frequencies", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "nodes_adjacency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "api_abstraction.internal.travel_time.Zob.nodes_adjacency", "name": "nodes_adjacency", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "to_destination_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.internal.travel_time.Zob.to_destination_mapping", "name": "to_destination_mapping", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "zob_edges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.internal.travel_time.Zob.zob_edges", "name": "zob_edges", "setter_type": null, "type": {".class": "Instance", "args": ["api_abstraction.internal.travel_time.ZobEdge"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.Zob.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.internal.travel_time.Zob", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZobEdge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.internal.travel_time.ZobEdge", "name": "<PERSON>ob<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.internal.travel_time.ZobEdge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 411, "name": "origin_id", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 412, "name": "destination_id", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 413, "name": "origin", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 414, "name": "destination", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 415, "name": "length_before", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 416, "name": "length", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 417, "name": "length_after", "type": "builtins.float"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "api_abstraction.internal.travel_time", "mro": ["api_abstraction.internal.travel_time.ZobEdge", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin_id", "destination_id", "origin", "destination", "length_before", "length", "length_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "api_abstraction.internal.travel_time.ZobEdge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "origin_id", "destination_id", "origin", "destination", "length_before", "length", "length_after"], "arg_types": ["api_abstraction.internal.travel_time.ZobEdge", "builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ZobEdge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "origin_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "destination_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "origin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "destination"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length_before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length_after"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["origin_id", "destination_id", "origin", "destination", "length_before", "length", "length_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["origin_id", "destination_id", "origin", "destination", "length_before", "length", "length_after"], "arg_types": ["builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["origin_id", "destination_id", "origin", "destination", "length_before", "length", "length_after"], "arg_types": ["builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "destination": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.destination", "name": "destination", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "destination_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.destination_id", "name": "destination_id", "setter_type": null, "type": "builtins.int"}}, "length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.length", "name": "length", "setter_type": null, "type": "builtins.float"}}, "length_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.length_after", "name": "length_after", "setter_type": null, "type": "builtins.float"}}, "length_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.length_before", "name": "length_before", "setter_type": null, "type": "builtins.float"}}, "origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.origin", "name": "origin", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "origin_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "api_abstraction.internal.travel_time.ZobEdge.origin_id", "name": "origin_id", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.internal.travel_time.ZobEdge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.internal.travel_time.ZobEdge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.internal.travel_time.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.distance_computer.compute_distance", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "mobility.config", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "geopy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.internal.travel_time.geopy", "name": "geopy", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.geopy", "source_any": null, "type_of_any": 3}}}, "gramEC": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.gramEC", "kind": "Gdef"}, "hours": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.hours", "kind": "Gdef"}, "kilometers": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.kilometers", "kind": "Gdef"}, "meters": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.meters", "kind": "Gdef"}, "networkx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.internal.travel_time.networkx", "name": "networkx", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.internal.travel_time.networkx", "source_any": null, "type_of_any": 3}}}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "product": {".class": "SymbolTableNode", "cross_ref": "itertools.product", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}}, "path": "api_abstraction\\internal\\travel_time.py"}
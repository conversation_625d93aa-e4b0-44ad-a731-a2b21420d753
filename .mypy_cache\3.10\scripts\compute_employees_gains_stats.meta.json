{"data_mtime": 1752154448, "dep_lines": [5, 4, 6, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "mobility.builders.json_builder", "mobility.quantity", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "fractions", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.indicators.inter_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.study", "numbers"], "hash": "b41589fa92a228a939d4ee2ddcec7ab98687df3e", "id": "scripts.compute_employees_gains_stats", "ignore_all": false, "interface_hash": "5994f7d518ef5866cc31f520c13fde25189e8e50", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\compute_employees_gains_stats.py", "plugin_data": null, "size": 14029, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1753176776, "dep_lines": [8, 9, 12, 13, 14, 22, 23, 24, 25, 39, 40, 41, 43, 44, 10, 11, 42, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.builders.crit_air_computer", "mobility.builders.exceptions", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.infrastructure", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.compute_poi_scenario", "mobility.workers.territory_computer", "mobility.constants", "mobility.funky", "mobility.quantity", "json", "os", "enum", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "mobility.ir", "mobility.ir.crit_air", "mobility.ir.error", "mobility.ir.geo_study", "mobility.ir.mode_constraints", "mobility.workers", "types", "typing_extensions"], "hash": "3c2d0fce7a92f4ca10973121521b95f571be7cbc", "id": "mobility.builders.json_builder", "ignore_all": false, "interface_hash": "f915bf6b1163cb3617f1b1bd715456a7b6305f2f", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\builders\\json_builder.py", "plugin_data": null, "size": 31753, "suppressed": ["jsonschema"], "version_id": "1.16.1"}
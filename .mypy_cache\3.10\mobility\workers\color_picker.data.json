{".class": "MypyFile", "_fullname": "mobility.workers.color_picker", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ColorNamedStops": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.color_mode.ColorNamedStops", "kind": "Gdef"}, "ColorPicker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.workers.color_picker.ColorPicker", "name": "ColorPicker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.workers.color_picker.ColorPicker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.workers.color_picker", "mro": ["mobility.workers.color_picker.ColorPicker", "builtins.object"], "names": {".class": "SymbolTable", "T3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "name": "T3", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.color_picker.ColorPicker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ColorPicker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pick_random_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker._pick_random_color", "name": "_pick_random_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.color_picker.ColorPicker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_pick_random_color of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "barn_red": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.barn_red", "name": "barn_red", "setter_type": null, "type": "builtins.str"}}, "black": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.black", "name": "black", "setter_type": null, "type": "builtins.str"}}, "blue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.blue", "name": "blue", "setter_type": null, "type": "builtins.str"}}, "blue_greened": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.blue_greened", "name": "blue_greened", "setter_type": null, "type": "builtins.str"}}, "blue_reded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.blue_reded", "name": "blue_reded", "setter_type": null, "type": "builtins.str"}}, "bronze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.bronze", "name": "bronze", "setter_type": null, "type": "builtins.str"}}, "citec_aniseed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.citec_aniseed", "name": "citec_aniseed", "setter_type": null, "type": "builtins.str"}}, "citec_apple_green": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.citec_apple_green", "name": "citec_apple_green", "setter_type": null, "type": "builtins.str"}}, "citec_brick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.citec_brick", "name": "citec_brick", "setter_type": null, "type": "builtins.str"}}, "citec_cerulean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.citec_cerulean", "name": "citec_cerulean", "setter_type": null, "type": "builtins.str"}}, "colorize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["points", "nb_colors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.colorize", "name": "colorize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["points", "nb_colors"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "colorize of ColorPicker", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}, "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.colorize", "name": "colorize", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["points", "nb_colors"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "colorize of ColorPicker", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}, "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.colorize", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}]}}}}, "colors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.colors", "name": "colors", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "convert_hsl_to_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hsl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_hsl_to_rgb", "name": "convert_hsl_to_rgb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hsl"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.HSL"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_hsl_to_rgb of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_hsl_to_rgb", "name": "convert_hsl_to_rgb", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hsl"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.HSL"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_hsl_to_rgb of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_rgb_to_hsl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rgb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_rgb_to_hsl", "name": "convert_rgb_to_hsl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rgb"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_rgb_to_hsl of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.HSL"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_rgb_to_hsl", "name": "convert_rgb_to_hsl", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rgb"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_rgb_to_hsl of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.HSL"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_rgb_to_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rgb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_rgb_to_str", "name": "convert_rgb_to_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rgb"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_rgb_to_str of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_rgb_to_str", "name": "convert_rgb_to_str", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rgb"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_rgb_to_str of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_str_to_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["color"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_str_to_rgb", "name": "convert_str_to_rgb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["color"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_str_to_rgb of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_str_to_rgb", "name": "convert_str_to_rgb", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["color"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_str_to_rgb of ColorPicker", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.color_picker.RGB"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_str_to_rgba": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["color", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_str_to_rgba", "name": "convert_str_to_rgba", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["color", "alpha"], "arg_types": ["builtins.str", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_str_to_rgba of ColorPicker", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.convert_str_to_rgba", "name": "convert_str_to_rgba", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["color", "alpha"], "arg_types": ["builtins.str", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_str_to_rgba of ColorPicker", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dark_grey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.dark_grey", "name": "dark_grey", "setter_type": null, "type": "builtins.str"}}, "dark_red": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.dark_red", "name": "dark_red", "setter_type": null, "type": "builtins.str"}}, "dark_sky_blue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.dark_sky_blue", "name": "dark_sky_blue", "setter_type": null, "type": "builtins.str"}}, "darker_grey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.darker_grey", "name": "darker_grey", "setter_type": null, "type": "builtins.str"}}, "electric_green": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.electric_green", "name": "electric_green", "setter_type": null, "type": "builtins.str"}}, "electric_red": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.electric_red", "name": "electric_red", "setter_type": null, "type": "builtins.str"}}, "fast_electric_green": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.fast_electric_green", "name": "fast_electric_green", "setter_type": null, "type": "builtins.str"}}, "forest_green": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.forest_green", "name": "forest_green", "setter_type": null, "type": "builtins.str"}}, "gold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.gold", "name": "gold", "setter_type": null, "type": "builtins.str"}}, "green": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.green", "name": "green", "setter_type": null, "type": "builtins.str"}}, "green_blue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.green_blue", "name": "green_blue", "setter_type": null, "type": "builtins.str"}}, "grey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.grey", "name": "grey", "setter_type": null, "type": "builtins.str"}}, "is_hex_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["color"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.is_hex_color", "name": "is_hex_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["color"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_hex_color of ColorPicker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.is_hex_color", "name": "is_hex_color", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["color"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_hex_color of ColorPicker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "jonquil": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.jonquil", "name": "jon<PERSON>l", "setter_type": null, "type": "builtins.str"}}, "light_blue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.light_blue", "name": "light_blue", "setter_type": null, "type": "builtins.str"}}, "light_grey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.light_grey", "name": "light_grey", "setter_type": null, "type": "builtins.str"}}, "light_orange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.light_orange", "name": "light_orange", "setter_type": null, "type": "builtins.str"}}, "orange_brown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.orange_brown", "name": "orange_brown", "setter_type": null, "type": "builtins.str"}}, "orange_crayola": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.orange_crayola", "name": "orange_crayola", "setter_type": null, "type": "builtins.str"}}, "orange_engineering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.orange_engineering", "name": "orange_engineering", "setter_type": null, "type": "builtins.str"}}, "orange_soft": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.orange_soft", "name": "orange_soft", "setter_type": null, "type": "builtins.str"}}, "pacific_blue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.pacific_blue", "name": "pacific_blue", "setter_type": null, "type": "builtins.str"}}, "persian_plum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.persian_plum", "name": "persian_plum", "setter_type": null, "type": "builtins.str"}}, "pick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick", "name": "pick", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color_id"], "arg_types": ["mobility.workers.color_picker.ColorPicker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_on_bicolor_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "from_color_id", "to_color_id", "factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_on_bicolor_scale", "name": "pick_on_bicolor_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "from_color_id", "to_color_id", "factor"], "arg_types": ["mobility.workers.color_picker.ColorPicker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_on_bicolor_scale of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_on_color_named_stops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "color_named_stops", "colorable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_on_color_named_stops", "name": "pick_on_color_named_stops", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "color_named_stops", "colorable"], "arg_types": ["mobility.workers.color_picker.ColorPicker", "mobility.ir.color_mode.ColorNamedStops", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_on_color_named_stops of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_on_whitening_scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "color_id", "whiten"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_on_whitening_scale", "name": "pick_on_whitening_scale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "color_id", "whiten"], "arg_types": ["mobility.workers.color_picker.ColorPicker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_on_whitening_scale of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_random", "name": "pick_random", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.workers.color_picker.ColorPicker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_random of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_site_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "site_n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_site_color", "name": "pick_site_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "site_n"], "arg_types": ["mobility.workers.color_picker.ColorPicker", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_site_color of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pick_text_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "background_color"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.workers.color_picker.ColorPicker.pick_text_color", "name": "pick_text_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "background_color"], "arg_types": ["mobility.workers.color_picker.ColorPicker", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pick_text_color of ColorPicker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "purple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.purple", "name": "purple", "setter_type": null, "type": "builtins.str"}}, "red": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.red", "name": "red", "setter_type": null, "type": "builtins.str"}}, "red_orange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.red_orange", "name": "red_orange", "setter_type": null, "type": "builtins.str"}}, "rosso_corsa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.rosso_corsa", "name": "rosso_corsa", "setter_type": null, "type": "builtins.str"}}, "safety_orange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.safety_orange", "name": "safety_orange", "setter_type": null, "type": "builtins.str"}}, "silver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.silver", "name": "silver", "setter_type": null, "type": "builtins.str"}}, "simone_grey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.simone_grey", "name": "simone_grey", "setter_type": null, "type": "builtins.str"}}, "simone_orange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.simone_orange", "name": "simone_orange", "setter_type": null, "type": "builtins.str"}}, "simone_purple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.simone_purple", "name": "simone_purple", "setter_type": null, "type": "builtins.str"}}, "simone_yellow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.simone_yellow", "name": "simone_yellow", "setter_type": null, "type": "builtins.str"}}, "triangulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["points"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.workers.color_picker.ColorPicker.triangulate", "name": "triangulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["points"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.triangulate", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "triangulate of ColorPicker", "ret_type": {".class": "AnyType", "missing_import_name": "mobility.workers.color_picker.networkx", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.triangulate", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.ColorPicker.triangulate", "name": "triangulate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["points"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.triangulate", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "triangulate of ColorPicker", "ret_type": {".class": "AnyType", "missing_import_name": "mobility.workers.color_picker.networkx", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.T3", "id": -1, "name": "T3", "namespace": "mobility.workers.color_picker.ColorPicker.triangulate", "upper_bound": "mobility.ir.geo_study.Localised", "values": [], "variance": 0}]}}}}, "white": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.white", "name": "white", "setter_type": null, "type": "builtins.str"}}, "yellow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.workers.color_picker.ColorPicker.yellow", "name": "yellow", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.workers.color_picker.ColorPicker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.workers.color_picker.ColorPicker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CostCategory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostCategory", "kind": "Gdef"}, "CostPayer": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.CostPayer", "kind": "Gdef"}, "Delaunay": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.Delaunay", "name": "Delaunay", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.workers.color_picker.Delaunay", "source_any": null, "type_of_any": 3}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HSL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.workers.color_picker.HSL", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Localised": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Localised", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RGB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.workers.color_picker.RGB", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TransportType": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "WorkMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.work_mode.WorkMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.workers.color_picker.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "networkx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.workers.color_picker.networkx", "name": "networkx", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.workers.color_picker.networkx", "source_any": null, "type_of_any": 3}}}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}}, "path": "mobility\\workers\\color_picker.py"}
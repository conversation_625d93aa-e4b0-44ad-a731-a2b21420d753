{"data_mtime": 1751444410, "dep_lines": [4, 6, 12, 13, 5, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.api", "mobility.ir.onboarding_form_step", "mobility.repositories.abstract_repositories", "mobility.use_cases.base_use_case", "mobility.funky", "dataclasses", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.geocode_api", "enum", "functools", "mobility.ir", "mobility.ir.geo_study", "mobility.repositories", "types", "typing_extensions"], "hash": "59f81117236e84c9440c2c56242c435205941b7b", "id": "mobility.use_cases.run_onboarding_form", "ignore_all": false, "interface_hash": "0f28712a75a856a6aefe4c6a6143d81d47a21990", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\use_cases\\run_onboarding_form.py", "plugin_data": null, "size": 10777, "suppressed": [], "version_id": "1.16.1"}
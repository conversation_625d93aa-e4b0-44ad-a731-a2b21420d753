{"data_mtime": 1751444404, "dep_lines": [5, 6, 4, 7, 1, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mobility.ir.cost", "mobility.ir.transport", "mobility.funky", "mobility.quantity", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "c7ed54d476f8a72eedffcdd1e16b9c94e27cada4", "id": "mobility.ir.indicators.staggered_hours_indicators", "ignore_all": false, "interface_hash": "8fb3bb20745cb4b2c06ac1ac7d4a2a404f25fa16", "mtime": 1722327416, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\staggered_hours_indicators.py", "plugin_data": null, "size": 851, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1752154451, "dep_lines": [21, 10, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 30, 31, 32, 33, 34, 35, 36, 37, 42, 20, 24, 3, 4, 5, 6, 7, 8, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "api_abstraction.api.event_reporter", "api_abstraction.api.factories", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "mobility.builders.csv_multi_reader", "mobility.builders.excel_reader", "mobility.builders.exceptions", "mobility.builders.json_builder", "mobility.builders.territory_builder", "mobility.ir.map_elements", "mobility.ir.study", "mobility.workers.coworking_scenario_computer", "mobility.workers.emission_computer", "mobility.workers.geocoder", "mobility.workers.isochrone_computer", "mobility.workers.territory_computer", "mobility.workers.territory_controller", "mobility.workers.time_error_employee_filter", "mobility.workers.transport_mode_computer", "mobility.workers.travel_timer", "mobility.interface", "mobility.serializers", "<PERSON><PERSON><PERSON><PERSON>", "logging", "os", "sys", "datetime", "typing", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "configparser", "mobility.builders", "mobility.ir", "mobility.ir.indicators", "mobility.ir.new_indicators", "mobility.ir.territory", "mobility.serializers.excel_serializer", "mobility.serializers.geopackage_serializer", "mobility.serializers.json_serializer", "mobility.serializers.report_serializer", "mobility.workers", "types", "typing_extensions"], "hash": "fdd161cd3e483be4058b30fbca08361bf6a76612", "id": "mobility.entry_points.run_study", "ignore_all": false, "interface_hash": "7bf3c4b5838a123e25067093687ddc076bb35b3a", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\entry_points\\run_study.py", "plugin_data": null, "size": 13709, "suppressed": [], "version_id": "1.16.1"}
#!/usr/bin/env python3
"""
Test script to verify the corrected 4-level flow processing logic.
"""

import pandas as pd
from strasbourg_aggregation_flow import process_csv_to_4_level_flows


def test_corrected_flow_processing():
    """Test the complete CSV processing function."""

    # Create sample data that includes various flow types
    sample_data = {
        "origin_zone": [
            "Strasbourg - Centre",
            "Strasbourg - Gare",
            "Strasbourg quartiers est",
            "EMS_1ere couronne sud",  # External to Strasbourg
            "Strasbourg - Krutenau",
            "BMNord_Sud Ouest",  # External to external (should be skipped)
            "HorsZone",  # External to Strasbourg
        ],
        "destination_zone": [
            "Strasbourg - Gare",
            "Strasbourg quartiers est",
            "Strasbourg - Centre",
            "Strasbourg - Centre",  # External to Strasbourg
            "EMS_Couronne Nord",  # Strasbourg to external
            "EMS_1ere couronne sud",  # External to external (should be skipped)
            "Strasbourg quartiers sud",  # External to Strasbourg
        ],
        "count": [25, 20, 15, 30, 22, 18, 14],
    }

    df = pd.DataFrame(sample_data)
    print("Sample CSV data:")
    print(df)
    print()

    # Process the data
    flow_data = process_csv_to_4_level_flows(df)

    print(f"Generated {len(flow_data)} 4-level flow records:")
    for i, (level1, level2, level3, level4, count) in enumerate(flow_data):
        print(
            f"{i+1:2d}. {level1:25} → {level2:15} → {level3:15} → {level4:25} ({count:2d})"
        )

    print()

    # Verify the complete structure logic
    print("Verification of Complete Structure Logic:")
    print("✓ All Level 1 origins should be Strasbourg communes")
    print("✓ All Level 4 destinations should be Strasbourg communes")
    print("✓ Levels 2-3 should include both Strasbourg and external departments")
    print(
        "✓ Should include intra-Strasbourg, external→Strasbourg, and Strasbourg→external flows"
    )

    # Check the structure
    all_strasbourg_origins = all(f[0].startswith("Strasbourg") for f in flow_data)
    all_strasbourg_destinations = all(f[3].startswith("Strasbourg") for f in flow_data)

    # Check flow types
    intra_strasbourg = [
        f for f in flow_data if f[1] == "Strasbourg" and f[2] == "Strasbourg"
    ]
    external_to_strasbourg = [
        f
        for f in flow_data
        if not f[1].startswith("Strasbourg") and f[2] == "Strasbourg"
    ]
    strasbourg_to_external = [
        f
        for f in flow_data
        if f[1] == "Strasbourg" and not f[2].startswith("Strasbourg")
    ]

    print(f"All Level 1 are Strasbourg communes: {all_strasbourg_origins}")
    print(f"All Level 4 are Strasbourg communes: {all_strasbourg_destinations}")
    print(f"Intra-Strasbourg flows: {len(intra_strasbourg)}")
    print(f"External → Strasbourg flows: {len(external_to_strasbourg)}")
    print(f"Strasbourg → External flows: {len(strasbourg_to_external)}")

    if all_strasbourg_origins and all_strasbourg_destinations and len(flow_data) > 3:
        print("\n✅ SUCCESS: Complete 4-level structure is working properly!")
        print("   - Level 1: Only Strasbourg communes (origins)")
        print("   - Level 2: All departments (Strasbourg + external)")
        print("   - Level 3: All departments (complete mobility picture)")
        print("   - Level 4: Only Strasbourg communes (destinations)")
        print(
            "   - Includes all relevant flow types with proper aggregation/disaggregation"
        )
    else:
        print("\n❌ ERROR: Structure validation failed!")


if __name__ == "__main__":
    test_corrected_flow_processing()

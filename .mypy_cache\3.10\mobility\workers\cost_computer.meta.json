{"data_mtime": **********, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 25, 26, 27, 28, 29, 4, 13, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.workers.employee_cost_calculator", "mobility.workers.health_cost_calculator", "mobility.workers.infrastructure_cost_calculator", "mobility.workers.transport_cost_calculator", "mobility.workers.work_accident_cost_calculator", "mobility.funky", "mobility.quantity", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "types", "typing_extensions"], "hash": "6775c685527b4bceb38b2b42c1b4b771b68bcaf7", "id": "mobility.workers.cost_computer", "ignore_all": false, "interface_hash": "28a9edbae996b224b81efdfc316373083b6ea2c2", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\cost_computer.py", "plugin_data": null, "size": 21668, "suppressed": [], "version_id": "1.16.1"}
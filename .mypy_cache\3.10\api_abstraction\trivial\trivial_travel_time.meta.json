{"data_mtime": 1753176774, "dep_lines": [8, 9, 10, 11, 12, 14, 13, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.quantity", "json", "collections", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "api_abstraction.api.api", "enum", "json.decoder", "mobility", "mobility.ir", "typing_extensions"], "hash": "d0f471a28106af8a05c72be16607a95451c91c17", "id": "api_abstraction.trivial.trivial_travel_time", "ignore_all": false, "interface_hash": "a3055ecbb27e53cf46442f3e0f6173d52e20e166", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\trivial\\trivial_travel_time.py", "plugin_data": null, "size": 9374, "suppressed": ["geopandas", "shapely"], "version_id": "1.16.1"}
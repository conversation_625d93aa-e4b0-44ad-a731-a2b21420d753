{"data_mtime": **********, "dep_lines": [5, 7, 8, 10, 11, 12, 13, 14, 15, 3, 5, 1, 2, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.api_core.exceptions", "google.maps.routing_v2", "google.protobuf.duration_pb2", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "api_abstraction.google.base_google_api", "api_abstraction.google.routes_api", "mobility.ir.geo_study", "mobility.ir.transport", "unittest.mock", "google.api_core", "datetime", "typing", "google", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "abc", "api_abstraction.api", "api_abstraction.google", "contextlib", "enum", "google.maps", "google.maps.routing_v2.types", "google.maps.routing_v2.types.route", "google.maps.routing_v2.types.routes_service", "google.protobuf", "google.protobuf.internal", "google.protobuf.internal.well_known_types", "google.protobuf.message", "mobility", "mobility.ir", "re", "typing_extensions", "unittest"], "hash": "ca87e91a77c0d2c4f6b3545329b0d4f694c93b76", "id": "api_abstraction.tests.google.test_routes_api", "ignore_all": false, "interface_hash": "edf4bf641ed216cd48ebd7ae98727a91e6728311", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\tests\\google\\test_routes_api.py", "plugin_data": null, "size": 12693, "suppressed": [], "version_id": "1.16.1"}
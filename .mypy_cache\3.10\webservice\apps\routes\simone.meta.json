{"data_mtime": 1752154451, "dep_lines": [31, 32, 33, 36, 55, 25, 27, 28, 29, 30, 37, 38, 39, 49, 50, 51, 52, 53, 54, 56, 26, 40, 41, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 22], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "webservice.apps.repositories.query_record", "mobility.entry_points.run_pre_study", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.transport", "mobility.ir.webstudy", "mobility.serializers.report_quantity_formatters", "mobility.use_cases.record_query", "mobility.workers.zfe_impact_computer", "webservice.apps.cartographer", "webservice.apps.db", "webservice.apps.departments", "webservice.apps.storage", "mobility.funky", "mobility.profiler", "mobility.quantity", "dataclasses", "json", "logging", "smtplib", "textwrap", "collections", "datetime", "enum", "itertools", "typing", "flask", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "flask.app", "flask.blueprints", "flask.config", "flask.globals", "flask.helpers", "flask.templating", "flask.wrappers", "fractions", "json.encoder", "mobility", "mobility.entry_points", "mobility.ir", "mobility.ir.indicators", "mobility.ir.new_indicators", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.repositories.config_repository", "mobility.serializers", "mobility.use_cases", "mobility.use_cases.base_use_case", "mobility.workers", "numbers", "ssl", "types", "typing_extensions", "webservice.apps.repositories", "werkzeug", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.wrappers"], "hash": "c5bb62485e933c72b6da79a4c665f2cea3d87bfd", "id": "webservice.apps.routes.simone", "ignore_all": false, "interface_hash": "6e2b6049b5f1dc54b4daaf2b064352436d27f4e2", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\routes\\simone.py", "plugin_data": null, "size": 41823, "suppressed": ["plotly", "flask_cors"], "version_id": "1.16.1"}
{"data_mtime": 1751444419, "dep_lines": [12, 9, 10, 11, 1, 2, 4, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 6], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["webservice.apps.repositories.query_record", "mobility.use_cases.compute_query_stat", "mobility.use_cases.get_query", "webservice.apps.db", "datetime", "typing", "flask", "mobility", "builtins", "_frozen_importlib", "_typeshed", "abc", "configparser", "flask.blueprints", "flask.globals", "flask.helpers", "flask.templating", "flask.wrappers", "mobility.ir", "mobility.ir.query_record", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.repositories.config_repository", "mobility.use_cases", "mobility.use_cases.base_use_case", "types", "typing_extensions", "webservice.apps.repositories", "werkzeug", "werkzeug.datastructures", "werkzeug.wrappers"], "hash": "e6c2bca9932a836af47435a6bfb428f2d16546cd", "id": "webservice.apps.routes.queries", "ignore_all": false, "interface_hash": "e139d449336980474ccde69532f891a84668c9c0", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apps\\routes\\queries.py", "plugin_data": null, "size": 4036, "suppressed": ["flask_bcrypt", "flask_httpauth"], "version_id": "1.16.1"}
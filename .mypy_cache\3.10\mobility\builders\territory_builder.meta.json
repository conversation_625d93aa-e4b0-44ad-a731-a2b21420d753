{"data_mtime": 1752056503, "dep_lines": [9, 10, 16, 17, 18, 19, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.builders.exceptions", "mobility.builders.osm_infrastructure", "mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.site", "mobility.ir.territory", "json", "os", "pathlib", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "genericpath", "io", "json.decoder", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.transport", "ntpath", "osmium", "osmium.io", "osmium.simple_handler", "typing_extensions"], "hash": "68ef7e681cd6afaf46d482d4a5cbd01002aa1343", "id": "mobility.builders.territory_builder", "ignore_all": false, "interface_hash": "9161b81fb57339d7c08e2b81cfd98010f53a8ba4", "mtime": 1723449311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\builders\\territory_builder.py", "plugin_data": null, "size": 9281, "suppressed": ["g<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}
import random
from typing import Any, Dict, List, Optional, Tuple, TypeVar

import networkx
import numpy
from scipy.spatial import Delaunay

from mobility.ir.color_mode import ColorNamedStops
from mobility.ir.cost import CostCategory, CostPayer
from mobility.ir.geo_study import Localised
from mobility.ir.transport import TransportMode, TransportType
from mobility.ir.work_mode import WorkMode

RGB = Tuple[int, int, int]
HSL = Tuple[float, float, float]


class ColorPicker:
    blue = "#295da7"
    light_blue = "#5394cf"
    green_blue = "#2A69AB"
    pacific_blue = "#1ea1b4"
    dark_sky_blue = "#90c4da"
    red = "#d83324"
    barn_red = "#7a0101"
    rosso_corsa = "#CE2210"
    electric_red = "#eb536e"
    grey = "#cccccc"
    light_grey = "#dddddd"
    dark_grey = "#aaaaaa"
    darker_grey = "#6a6a6a"
    green = "#72b257"
    electric_green = "#52ca72"
    forest_green = "#019431"
    yellow = "#edbe53"
    jonquil = "#F5C601"
    safety_orange = "#ff780a"
    red_orange = "#f04f18"
    orange_crayola = "#FF742E"
    orange_soft = "#f28414"
    orange_engineering = "#bb3014"
    orange_brown = "#e35503"
    dark_red = "#8c1c13"
    persian_plum = "#66101f"
    black = "#0b0b0b"
    white = "#ffffff"
    purple = "#9c5fb1"
    simone_purple = "#8e5b8b"
    simone_orange = "#e58613"
    simone_yellow = "#d9b948"
    simone_grey = "#858ac1"
    gold = "#c9ae11"
    bronze = "#d67d15"
    silver = "#a8a698"
    citec_brick = "#9f261f"
    citec_apple_green = "#6ab023"
    citec_aniseed = "#c9d200"
    citec_cerulean = "#006271"
    blue_greened = "#2895ba"
    fast_electric_green = "#58d8a5"
    light_orange = "#f2a345"
    blue_reded = "#7f46af"

    def __init__(self) -> None:
        self.colors: Dict[Any, str] = {
            0: self.light_blue,
            1: self.red,
            2: "#91C499",
            3: "#CFD11A",
            4: "#5F506B",
            5: "#D17A22",
            6: "#531CB3",
            7: "#CC92C2",
            8: "#0B4F6C",
            9: "#F79F79",
            TransportMode.WALK: self.yellow,
            TransportMode.BICYCLE: self.green,
            TransportMode.PUBLIC_TRANSPORT: self.blue,
            TransportMode.CAR: self.red,
            TransportMode.CARPOOLING: self.orange_soft,
            TransportMode.ELECTRIC_BICYCLE: self.electric_green,
            TransportMode.ELECTRIC_CAR: self.electric_red,
            TransportMode.MOTORCYCLE: self.orange_brown,
            TransportMode.ELECTRIC_MOTORCYCLE: self.light_orange,
            TransportMode.FAST_BICYCLE: self.fast_electric_green,
            TransportMode.CAR_PUBLIC_TRANSPORT: self.blue_reded,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: self.blue_greened,
            TransportType.BUS: self.blue,
            TransportType.TRAM: self.jonquil,
            TransportType.TRAIN: self.black,
            TransportType.MONORAIL: self.pacific_blue,
            TransportType.SUBWAY: self.forest_green,
            TransportType.FERRY: self.persian_plum,
            "modelity": self.blue,
            "modelity_light": self.light_blue,
            "warning": self.orange_crayola,
            "critical": self.red,
            "supercritical": self.barn_red,
            "comfort": self.light_blue,
            "acceptable": self.blue,
            "neutral": self.grey,
            "neutral2": self.light_grey,
            "black": self.black,
            "white": self.white,
            "blue": self.blue,
            "light_blue": self.light_blue,
            "red": self.orange_crayola,
            "dark_red": self.red,
            "darkest_red": self.barn_red,
            "grey": self.grey,
            "light_grey": self.light_grey,
            "green": self.green,
            "yellow": self.yellow,
            CostPayer.EMPLOYEE: self.light_blue,
            CostPayer.COMPANY: self.safety_orange,
            CostPayer.SOCIETY: self.green,
            CostCategory.TRAVEL: self.simone_purple,
            CostCategory.FACILITY: self.simone_orange,
            CostCategory.ORGANISATION: self.simone_yellow,
            CostCategory.ENVIRONMENT: self.simone_grey,
            "gold": self.gold,
            "silver": self.silver,
            "bronze": self.bronze,
            "carpooling": self.orange_soft,
            WorkMode.ON_SITE_FULL_TIME: self.blue,
            WorkMode.COWORKING: self.simone_purple,
            WorkMode.HOME_OFFICE: self.light_blue,
        }

    def pick_random(self) -> str:
        return self._pick_random_color()

    def pick(self, color_id: Any) -> str:
        if isinstance(color_id, str) and self.is_hex_color(color_id):
            return color_id
        try:
            return self.colors[color_id]
        except KeyError:
            try:
                c = getattr(self, color_id)
                if isinstance(c, str):
                    return c
            except AttributeError:
                pass
        color = self._pick_random_color()
        self.colors[color_id] = color
        return color

    def pick_on_whitening_scale(self, color_id: Any, whiten: float) -> str:
        base_color = self.pick(color_id)
        h, s, lum = self.convert_rgb_to_hsl(self.convert_str_to_rgb(base_color))
        new_sat = s * (1.0 - whiten)
        new_light = lum + whiten * (1.0 - lum)
        return self.convert_rgb_to_str(self.convert_hsl_to_rgb((h, new_sat, new_light)))

    def pick_on_bicolor_scale(
        self, from_color_id: Any, to_color_id: Any, factor: float
    ) -> str:
        from_color = self.pick(from_color_id)
        to_color = self.pick(to_color_id)
        fh, fs, fl = self.convert_rgb_to_hsl(self.convert_str_to_rgb(from_color))
        th, ts, tl = self.convert_rgb_to_hsl(self.convert_str_to_rgb(to_color))
        delta_h = th - fh
        if abs(delta_h) >= 180:
            if delta_h > 0:
                delta_h -= 360
            else:
                delta_h += 360
        if fs == 0.0:
            new_h = th
        elif ts == 0.0:
            new_h = fh
        else:
            new_h = fh + factor * delta_h
        new_s = fs + factor * (ts - fs)
        new_l = fl + factor * (tl - fl)
        return self.convert_rgb_to_str(self.convert_hsl_to_rgb((new_h, new_s, new_l)))

    def pick_on_color_named_stops(
        self, color_named_stops: ColorNamedStops, colorable: Any
    ) -> str:
        colorable_stop = color_named_stops.mapping.get(colorable, 0.0)
        smaller_stops = [
            stop for stop in color_named_stops.color_stops if stop <= colorable_stop
        ]
        greater_stops = [
            stop for stop in color_named_stops.color_stops if stop >= colorable_stop
        ]
        if len(smaller_stops) == 0 and len(greater_stops) == 0:
            return self.pick_random()
        if len(greater_stops) != 0:
            upper_stop = min(greater_stops)
        else:
            upper_stop = max(smaller_stops)
        if len(smaller_stops) != 0:
            lower_stop = max(smaller_stops)
        else:
            lower_stop = min(greater_stops)
        delta = abs(upper_stop - lower_stop)
        if delta == 0.0:
            return self.pick(color_named_stops.color_stops[lower_stop])
        else:
            factor = (colorable_stop - lower_stop) / delta
            return self.pick_on_bicolor_scale(
                color_named_stops.color_stops[lower_stop],
                color_named_stops.color_stops[upper_stop],
                factor,
            )

    def pick_site_color(self, site_n: int) -> str:
        return {
            0: self.red,
            1: self.blue,
            2: self.yellow,
            3: self.green,
            4: self.safety_orange,
            5: self.light_blue,
            6: self.simone_purple,
            7: self.forest_green,
            8: self.rosso_corsa,
        }.get(site_n, self._pick_random_color())

    def _pick_random_color(self) -> str:
        return f"#{''.join([random.choice('0123456789ABCDEF') for _ in range(6)])}"

    def pick_text_color(self, background_color: str) -> str:
        try:
            color_int = int(background_color[1:], 16)
            b = color_int % 0x100
            g = (color_int // 0x100) % 0x100
            r = (color_int // 0x10000) % 0x100
            if r + g + b > 3 * (0xFF / 2):
                return self.black
            else:
                return self.white
        except ValueError:
            return self.black

    @staticmethod
    def is_hex_color(color: str) -> bool:
        return (
            len(color) == 7
            and color[0] == "#"
            and all(d in "0123456789abcdefABCDEF" for d in color[1:])
        )

    @staticmethod
    def convert_str_to_rgb(color: str) -> RGB:
        if not ColorPicker.is_hex_color(color):
            raise ValueError(f"Wrong format for color {color}")
        color_int = int(color[1:], 16)
        b = color_int % 0x100
        g = (color_int // 0x100) % 0x100
        r = (color_int // 0x10000) % 0x100
        return r, g, b

    @staticmethod
    def convert_str_to_rgba(
        color: str, alpha: float = 1.0
    ) -> Tuple[int, int, int, int]:
        r, g, b = ColorPicker.convert_str_to_rgb(color)
        return r, g, b, int(255 * alpha)

    @staticmethod
    def convert_rgb_to_str(rgb: RGB) -> str:
        r, g, b = rgb
        return f"#{r:02x}{g:02x}{b:02x}"

    T3 = TypeVar("T3", bound=Localised)

    @staticmethod
    def triangulate(points: List[T3]) -> networkx.Graph:
        if len(points) == 0:
            return networkx.Graph()
        np_points = []
        for point in points:
            coords = point.get_coordinates()
            np_points.append([coords.longitude, coords.latitude])
        delaunay_triangulation = Delaunay(numpy.array(np_points))
        graph = networkx.Graph()
        for simplex in delaunay_triangulation.simplices:
            graph.add_edge(simplex[0], simplex[1])
            graph.add_edge(simplex[1], simplex[2])
            graph.add_edge(simplex[2], simplex[0])
        return graph

    @staticmethod
    def colorize(points: List[T3], nb_colors: Optional[int] = None) -> Dict[T3, int]:
        if len(points) <= 2:
            return {points[i]: i for i in range(len(points))}
        graph = ColorPicker.triangulate(points)
        if nb_colors is None:
            coloring = networkx.greedy_color(graph)
        else:
            nb_colors = max(nb_colors, max((d for n, d in graph.degree())) + 1)
            coloring = networkx.equitable_color(graph, nb_colors)
        return {points[node]: color_index for node, color_index in coloring.items()}

    @staticmethod
    def convert_rgb_to_hsl(rgb: RGB) -> HSL:
        r, g, b = rgb
        R = r / 255.0
        G = g / 255.0
        B = b / 255.0
        M = max(R, G, B)
        m = min(R, G, B)
        L = (M + m) / 2.0
        if m == M:
            S = 0.0
            H = 0.0
        else:
            if L > 0.5:
                S = (M - m) / (2.0 - M - m)
            elif L <= 0.5:
                S = (M - m) / (M + m)
            if R == M:
                H = (G - B) / (M - m)
            elif G == M:
                H = 2.0 + (B - R) / (M - m)
            elif B == M:
                H = 4.0 + (R - G) / (M - m)
            H *= 60
            while H < 0.0:
                H += 360.0
            while H > 360.0:
                H -= 360.0
        return H, S, L

    @staticmethod
    def convert_hsl_to_rgb(hsl: HSL) -> RGB:
        h, s, lum = hsl

        def intermediate_convert(p: float, q: float, t: float) -> float:
            if t < 0.0:
                t += 1.0
            if t > 1.0:
                t -= 1.0
            if t < 1.0 / 6.0:
                return p + (q - p) * 6.0 * t
            if t < 1.0 / 2.0:
                return q
            if t < 2.0 / 3.0:
                return p + (q - p) * (2.0 / 3.0 - t) * 6.0
            return p

        if s == 0:
            r = g = b = lum
        else:
            q = lum * (1.0 + s) if lum < 0.5 else lum + s - lum * s
            p = 2.0 * lum - q
            r = intermediate_convert(p, q, h / 360.0 + 1.0 / 3.0)
            g = intermediate_convert(p, q, h / 360.0)
            b = intermediate_convert(p, q, h / 360.0 - 1.0 / 3.0)
        return int(r * 255), int(g * 255), int(b * 255)

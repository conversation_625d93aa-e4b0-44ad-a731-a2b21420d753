{".class": "MypyFile", "_fullname": "mobility.builders.json_builder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BicycleAmenitiesScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.BicycleAmenitiesScenario", "kind": "Gdef"}, "BicycleAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleAmenity", "kind": "Gdef"}, "BicycleLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.BicycleLine", "kind": "Gdef"}, "CarAmenitiesScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.CarAmenitiesScenario", "kind": "Gdef"}, "CarAmenity": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarAmenity", "kind": "Gdef"}, "CarWay": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.CarWay", "kind": "Gdef"}, "Commute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Commute", "kind": "Gdef"}, "ConsolidatedJsonBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder", "name": "ConsolidatedJsonBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_study"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_study"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConsolidatedJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build of ConsolidatedJsonBuilder", "ret_type": "mobility.ir.study.ConsolidatedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_bicycle_amenity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_bicycle_amenity", "name": "construct_bicycle_amenity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_bicycle_amenity of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_bicycle_amenity_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_bicycle_amenity_scenario", "name": "construct_bicycle_amenity_scenario", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_bicycle_amenity_scenario of ConsolidatedJsonBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.BicycleAmenitiesScenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_bicycle_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_bicycle_lines", "name": "construct_bicycle_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_bicycle_lines of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.BicycleLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_car_amenity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_car_amenity", "name": "construct_car_amenity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_car_amenity of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarAmenity"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_car_amenity_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_car_amenity_scenario", "name": "construct_car_amenity_scenario", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_car_amenity_scenario of ConsolidatedJsonBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.CarAmenitiesScenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_car_ways": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_car_ways", "name": "construct_car_ways", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_car_ways of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.CarWay"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_coworking_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_coworking_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_coworking_scenario", "name": "construct_coworking_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_coworking_scenario"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_coworking_scenario of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, "mobility.ir.employee.GeoEmployee", "mobility.ir.site.CoworkingSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_geocode_failed_both_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_geocode_failed_both_scenarios", "name": "construct_geocode_failed_both_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_geocode_failed_both_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_geocode_failed_employees_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_geocode_failed_employees_scenarios", "name": "construct_geocode_failed_employees_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_geocode_failed_employees_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_geocode_failed_sites_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_geocode_failed_sites_scenarios", "name": "construct_geocode_failed_sites_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_geocode_failed_sites_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_isochrones": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_isochrones", "name": "construct_isochrones", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_isochrones of ConsolidatedJsonBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.SiteIsochrone"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_poi_scenarios", "name": "construct_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_poi_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.site.GeoSite", "mobility.ir.poi.PointOfInterest", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_public_transport_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_public_transport_lines", "name": "construct_public_transport_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_public_transport_lines of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportLine"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_public_transport_stations_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_public_transport_stations_scenario", "name": "construct_public_transport_stations_scenario", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_public_transport_stations_scenario of ConsolidatedJsonBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PublicTransportStationsScenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_public_transport_stops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_public_transport_stops", "name": "construct_public_transport_stops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_public_transport_stops of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.infrastructure.PublicTransportStop"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_raw_id_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_raw_id_scenario", "name": "construct_raw_id_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenario"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_raw_id_scenario of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_raw_id_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_raw_id_scenarios", "name": "construct_raw_id_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_raw_id_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_raw_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_raw_poi_scenarios", "name": "construct_raw_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_raw_poi_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_raw_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_raw_scenarios", "name": "construct_raw_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_raw_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_scenarios", "name": "construct_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_time_failed_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_time_failed_scenarios", "name": "construct_time_failed_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_time_failed_scenarios of ConsolidatedJsonBuilder", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_timed_infrastructure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.construct_timed_infrastructure", "name": "construct_timed_infrastructure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_timed_infrastructure of ConsolidatedJsonBuilder", "ret_type": "mobility.ir.study.TimedInfrastructure", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "employees_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.employees_map", "name": "employees_map", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "json_study": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.json_study", "name": "json_study", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "scenarios_data_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.scenarios_data_map", "name": "scenarios_data_map", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "sites_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.sites_map", "name": "sites_map", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConsolidatedJsonBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConsolidatedJsonBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConsolidatedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ConsolidatedStudy", "kind": "Gdef"}, "ConsolidatedStudyConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter", "name": "ConsolidatedStudyConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConsolidatedStudyConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["mobility.builders.json_builder.ConsolidatedStudyConverter", "mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_consolidated_to_timed_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.convert_consolidated_to_timed_scenarios", "name": "convert_consolidated_to_timed_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scenarios"], "arg_types": ["mobility.builders.json_builder.ConsolidatedStudyConverter", {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_consolidated_to_timed_scenarios of ConsolidatedStudyConverter", "ret_type": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.TimedCommuteData"], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_modal_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.convert_to_modal_study", "name": "convert_to_modal_study", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedStudyConverter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_modal_study of ConsolidatedStudyConverter", "ret_type": "mobility.ir.study.ModalStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_timed_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.convert_to_timed_study", "name": "convert_to_timed_study", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConsolidatedStudyConverter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_timed_study of ConsolidatedStudyConverter", "ret_type": "mobility.ir.study.TimedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "study": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.study", "name": "study", "setter_type": null, "type": "mobility.ir.study.ConsolidatedStudy"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConsolidatedStudyConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConsolidatedStudyConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV1", "name": "ConvertJsonV1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV1", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV1.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV1"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConvertJsonV1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_territory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_study"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV1._compute_territory", "name": "_compute_territory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_study"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV1", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_territory of ConvertJsonV1", "ret_type": "mobility.ir.territory.Territory", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV1.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV1", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV1.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}, "scenario_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConvertJsonV1.scenario_fields", "name": "scenario_fields", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV10": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV10", "name": "ConvertJsonV10", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV10", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV10", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV10.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV10", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV10", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV10.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV10.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV10", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV11": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV11", "name": "ConvertJsonV11", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV11", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV11", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV11.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV11"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConvertJsonV11", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV11.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV11", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV11", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crit_air_computer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.ConvertJsonV11.crit_air_computer", "name": "crit_air_computer", "setter_type": null, "type": "mobility.builders.crit_air_computer.CritAirComputer"}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV11.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV11.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV11", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV12": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV12", "name": "ConvertJsonV12", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV12", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV12", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV12.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV12", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV12", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV12.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV12.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV12", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV13": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV13", "name": "ConvertJsonV13", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV13", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV13", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV13.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV13.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV13", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV14": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV14", "name": "ConvertJsonV14", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV14", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV14", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV14.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV14", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV14", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV14.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV14.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV14", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV15": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV15", "name": "ConvertJsonV15", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV15", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV15", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV15.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV15.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV15", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV16": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV16", "name": "ConvertJsonV16", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV16", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV16", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV16.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV16", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV16", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV16.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV16.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV16", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV17": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV17", "name": "ConvertJsonV17", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV17", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV17", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV17.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV17.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV17", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV2", "name": "ConvertJsonV2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV2", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV2.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV2", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV2.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV3", "name": "ConvertJsonV3", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV3", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV3", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV3.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV3", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV3", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV3.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV3.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV3", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV4", "name": "ConvertJsonV4", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV4", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV4", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV4.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV4", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV4", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV4.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV4.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV4", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV5", "name": "ConvertJsonV5", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV5", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV5", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV5.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV5", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV5", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV5.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV5.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV5", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV6", "name": "ConvertJsonV6", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV6", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV6", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV6.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV6", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV6", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV6.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV6.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV6", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV7", "name": "ConvertJsonV7", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV7", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV7", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV7.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV7", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV7", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV7.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV7.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV7", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV8", "name": "ConvertJsonV8", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV8", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV8", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV8.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV8", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV8", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV8.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV8.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV8", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertJsonV9": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.ConvertJsonV9", "name": "ConvertJsonV9", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.ConvertJsonV9", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.ConvertJsonV9", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.ConvertJsonV9.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.ConvertJsonV9", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of ConvertJsonV9", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.ConvertJsonV9.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.ConvertJsonV9.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.ConvertJsonV9", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoworkingSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.CoworkingSite", "kind": "Gdef"}, "CritAirComputer": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.crit_air_computer.CritAirComputer", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FailedGeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.FailedGeoEmployee", "kind": "Gdef"}, "FailedGeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.FailedGeoSite", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef"}, "JSONVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.JSONVersion", "name": "JSONVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "mobility.builders.json_builder.JSONVersion", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.JSONVersion", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "V1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V1", "name": "V1", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "V10": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V10", "name": "V10", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "V11": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V11", "name": "V11", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 11}, "type_ref": "builtins.int"}}}, "V12": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V12", "name": "V12", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, "type_ref": "builtins.int"}}}, "V13": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V13", "name": "V13", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 13}, "type_ref": "builtins.int"}}}, "V14": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V14", "name": "V14", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 14}, "type_ref": "builtins.int"}}}, "V15": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V15", "name": "V15", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 15}, "type_ref": "builtins.int"}}}, "V16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V16", "name": "V16", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "V17": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V17", "name": "V17", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 17}, "type_ref": "builtins.int"}}}, "V18": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V18", "name": "V18", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 18}, "type_ref": "builtins.int"}}}, "V2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V2", "name": "V2", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "V3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V3", "name": "V3", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "V4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V4", "name": "V4", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "V5": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V5", "name": "V5", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "V6": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V6", "name": "V6", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "V7": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V7", "name": "V7", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "V8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V8", "name": "V8", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "V9": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JSONVersion.V9", "name": "V9", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}, "_support_old_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.builders.json_builder.JSONVersion._support_old_version", "name": "_support_old_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "mobility.builders.json_builder.JSONVersion"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_support_old_version of JSONVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.builders.json_builder.JSONVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.JSONVersion._support_old_version", "name": "_support_old_version", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "mobility.builders.json_builder.JSONVersion"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_support_old_version of JSONVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", "mobility.builders.json_builder.JSONVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "version_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.builders.json_builder.JSONVersion.from_string", "name": "from_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "version_str"], "arg_types": [{".class": "TypeType", "item": "mobility.builders.json_builder.JSONVersion"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_string of JSONVersion", "ret_type": "mobility.builders.json_builder.JSONVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.JSONVersion.from_string", "name": "from_string", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "version_str"], "arg_types": [{".class": "TypeType", "item": "mobility.builders.json_builder.JSONVersion"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_string of JSONVersion", "ret_type": "mobility.builders.json_builder.JSONVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JSONVersion.to_string", "name": "to_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.JSONVersion"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_string of JSONVersion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.JSONVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.JSONVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSON_SCHEMA_DIR": {".class": "SymbolTableNode", "cross_ref": "mobility.constants.JSON_SCHEMA_DIR", "kind": "Gdef"}, "JSON_VERSION": {".class": "SymbolTableNode", "cross_ref": "mobility.constants.JSON_VERSION", "kind": "Gdef"}, "JsonPromoter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.JsonPromoter", "name": "JsonPromoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.JsonPromoter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "_iterate_on_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "sub_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JsonPromoter._iterate_on_node", "name": "_iterate_on_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "sub_dict"], "arg_types": ["mobility.builders.json_builder.JsonPromoter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iterate_on_node of JsonPromoter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JsonPromoter.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_dict"], "arg_types": ["mobility.builders.json_builder.JsonPromoter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert of JsonPromoter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JsonPromoter.convert_element", "name": "convert_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.JsonPromoter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_element of JsonPromoter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.JsonPromoter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.JsonPromoter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JsonStudyPromoter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.JsonStudyPromoter", "name": "JsonStudyPromoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.JsonStudyPromoter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JsonStudyPromoter.convert_element", "name": "convert_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.JsonStudyPromoter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_element of JsonStudyPromoter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.JsonStudyPromoter.convert_study_element", "name": "convert_study_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "arg_types": ["mobility.builders.json_builder.JsonStudyPromoter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_study_element of JsonStudyPromoter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.builders.json_builder.JsonStudyPromoter.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.JsonStudyPromoter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.JsonStudyPromoter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModalCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.ModalCommuteData", "kind": "Gdef"}, "ModalStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ModalStudy", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PointOfInterest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.poi.PointOfInterest", "kind": "Gdef"}, "PublicTransportLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportLine", "kind": "Gdef"}, "PublicTransportStationsScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.PublicTransportStationsScenario", "kind": "Gdef"}, "PublicTransportStop": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportStop", "kind": "Gdef"}, "RefResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.RefResolver", "name": "RefResolver", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.builders.json_builder.RefResolver", "source_any": null, "type_of_any": 3}}}, "Scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenario", "kind": "Gdef"}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "SchemaError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.SchemaError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.builders.json_builder.SchemaError", "source_any": null, "type_of_any": 3}}}, "SiteIsochrone": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.SiteIsochrone", "kind": "Gdef"}, "StudyData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.StudyData", "kind": "Gdef"}, "StudyJsonBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.builders.json_builder.StudyJsonBuilder", "name": "StudyJsonBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.StudyJsonBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.builders.json_builder", "mro": ["mobility.builders.json_builder.StudyJsonBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_name"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_json_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder._extract_json_version", "name": "_extract_json_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_json_version of StudyJsonBuilder", "ret_type": "mobility.builders.json_builder.JSONVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder._load_json", "name": "_load_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_name"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_json of StudyJsonBuilder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_old_json_study_version_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder._normalize_old_json_study_version_format", "name": "_normalize_old_json_study_version_format", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_normalize_old_json_study_version_format of StudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["study"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.add_poi_scenarios", "name": "add_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_poi_scenarios of StudyJsonBuilder", "ret_type": "mobility.ir.study.ConsolidatedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.add_poi_scenarios", "name": "add_poi_scenarios", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["study"], "arg_types": ["mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_poi_scenarios of StudyJsonBuilder", "ret_type": "mobility.ir.study.ConsolidatedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "build_consolidated_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.build_consolidated_study", "name": "build_consolidated_study", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_consolidated_study of StudyJsonBuilder", "ret_type": "mobility.ir.study.ConsolidatedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_modal_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.build_modal_study", "name": "build_modal_study", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_modal_study of StudyJsonBuilder", "ret_type": "mobility.ir.study.ModalStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_timed_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.build_timed_study", "name": "build_timed_study", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.builders.json_builder.StudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_timed_study of StudyJsonBuilder", "ret_type": "mobility.ir.study.TimedStudy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "file_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.file_name", "name": "file_name", "setter_type": null, "type": "builtins.str"}}, "json_study": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.json_study", "name": "json_study", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.builders.json_builder.StudyJsonBuilder.version", "name": "version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.builders.json_builder.StudyJsonBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.builders.json_builder.StudyJsonBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TimedBicycleCommute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedBicycleCommute", "kind": "Gdef"}, "TimedCarCommute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedCarCommute", "kind": "Gdef"}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "TimedInfrastructure": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedInfrastructure", "kind": "Gdef"}, "TimedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedStudy", "kind": "Gdef"}, "TimedTransportStopCommute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedTransportStopCommute", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.ValidationError", "name": "ValidationError", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.builders.json_builder.ValidationError", "source_any": null, "type_of_any": 3}}}, "WrongJsonFormatError": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.exceptions.WrongJsonFormatError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.builders.json_builder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compute_poi_scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.compute_poi_scenario.compute_poi_scenario", "kind": "Gdef"}, "convert_consolidated_to_timed_commute_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.convert_consolidated_to_timed_commute_data", "name": "convert_consolidated_to_timed_commute_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["mobility.ir.commute_data.ModalCommuteData"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_consolidated_to_timed_commute_data", "ret_type": "mobility.ir.commute_data.TimedCommuteData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_closest_city_center": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.territory_computer.find_closest_city_center", "kind": "Gdef"}, "get_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.get_schema", "name": "get_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["uri"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_schema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mobility.builders.json_builder.validate", "name": "validate", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "mobility.builders.json_builder.validate", "source_any": null, "type_of_any": 3}}}, "validate_latest_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["json_study", "study_schema_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.builders.json_builder.validate_latest_json_schema", "name": "validate_latest_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["json_study", "study_schema_name"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_latest_json_schema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "mobility\\builders\\json_builder.py"}
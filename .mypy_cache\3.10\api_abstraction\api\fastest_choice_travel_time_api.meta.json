{"data_mtime": 1752056504, "dep_lines": [4, 5, 6, 7, 1, 2, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.transport", "functools", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility", "mobility.ir"], "hash": "31270acf869fb5d8498fd18ba711079cf20288ca", "id": "api_abstraction.api.fastest_choice_travel_time_api", "ignore_all": false, "interface_hash": "2e0d5bee7af902ce6363caef9339bbb711adff44", "mtime": 1752052790, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\api\\fastest_choice_travel_time_api.py", "plugin_data": null, "size": 1170, "suppressed": [], "version_id": "1.16.1"}
import logging
import math
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Sequence, Tuple, Union

from plotly import express
from plotly import graph_objects as go
from plotly.subplots import make_subplots
from shapely.geometry import MultiPolygon, mapping, shape

from mobility import config
from mobility.converters.accessibility import AccessibilitySlice, AccessibilitySlices
from mobility.funky import (
    ImmutableDict,
    apply_option,
    get_some,
    space_values,
    split_long_name,
    time_scale,
)
from mobility.ir.cost import CostPayer, IndividualCosts, MobilityAccountCosts
from mobility.ir.employee import GeoEmployee
from mobility.ir.framing_strategies import (
    FrameAllPoints,
    FrameAllPointWithCenter,
    FrameBigIslands,
    FrameStandardDeviation,
)
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.indicators.bicycle_infrastructure_indicators import (
    BicycleInfrastructureIndicators,
)
from mobility.ir.indicators.car_infrastructure_indicators import (
    CarInfrastructureIndicators,
)
from mobility.ir.indicators.indicators import Indicators
from mobility.ir.indicators.inter_scenario_indicators import InterScenarioIndicators
from mobility.ir.indicators.poi_scenario_indicators import PoiScenarioIndicators
from mobility.ir.indicators.pt_infrastructure_indicators import (
    PtInfrastructureIndicators,
)
from mobility.ir.indicators.scenario_indicators import ScenarioIndicators
from mobility.ir.map_elements import ArcGISIdentity, PointSymbolStyles, PointSymbolTypes
from mobility.ir.study import GeoSite
from mobility.ir.transport import TransportMode, TransportModeLattice, TransportType
from mobility.ir.work_mode import WorkMode
from mobility.quantity import (
    Quantity,
    adimensional,
    euros,
    minutes,
    seconds,
    tonEC,
    yearly,
)
from mobility.serializers.arcgis_map_maker import ArcGISMapMaker
from mobility.serializers.base_map_maker import BaseMapMaker
from mobility.serializers.chart_writer import (
    BaseChartWriter,
    CachedChartWriter,
    ChartWriter,
    PlaceholderChartWriter,
)
from mobility.serializers.charters.cost_grapher import CostGrapher
from mobility.serializers.charters.executive_summary_table_grapher import (
    ExecutiveSummaryTableGrapher,
)
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher
from mobility.serializers.charters.multi_bar_graph_with_threshold_grapher import (
    MultiBarGraphThresholdGrapher,
)
from mobility.serializers.charters.multiple_quantity_delta_grapher import (
    MultipleQuantityDeltaGrapher,
)
from mobility.serializers.charters.pie_grapher import PieGrapher
from mobility.serializers.charters.quantity_delta_card_grapher import (
    QuantityDeltaCardGrapher,
)
from mobility.serializers.charters.quantity_delta_grapher import QuantityDeltaGrapher
from mobility.serializers.charters.workforce_by_modes_grapher import (
    WorkforceByModesGrapher,
)
from mobility.serializers.illustrator import Illustrator
from mobility.serializers.plotly_map_maker import PlotlyMapMaker
from mobility.serializers.plotly_theme import Theme
from mobility.workers.color_picker import ColorPicker
from mobility.workers.executive_summary_computer import compute_executive_summary
from mobility.workers.ideal_scenario import IdealScenarioComputer
from mobility.workers.zfe_impact_computer import ZFEImpactCalendar, ZFEImpactComputer


class ChartTracer(object):
    comfortable_bound = 20
    acceptable_bound = 40
    warning_bound = 60
    stable_bound = 5
    critical_bound = 20
    pie_hole = 0.5

    def __init__(
        self,
        output_dir: str,
        indicators: Indicators,
        generate_graphs: bool,
        illustrator: Illustrator,
        arcgis_identity: ArcGISIdentity,
        use_cached_graphs: bool = False,
    ) -> None:
        self.output_dir = output_dir
        self.indicators = indicators
        self.scenario_collectors = [
            indicators.present_scenario
        ] + self.indicators.alternative_scenarios
        self.scenario_nicknames = [s.nickname for s in self.scenario_collectors]
        self.generate_graphs = generate_graphs
        self.use_cached_graphs = use_cached_graphs
        self.theme = Theme()
        self.modes = [
            (TransportMode.WALK, "Marche à pied"),
            (TransportMode.BICYCLE, "Vélo"),
            (TransportMode.ELECTRIC_BICYCLE, "Vélo électrique"),
            (TransportMode.FAST_BICYCLE, "Vélo électrique 45km/h"),
            (TransportMode.BICYCLE_PUBLIC_TRANSPORT, "Vélo + TC"),
            (TransportMode.PUBLIC_TRANSPORT, "Transport en commun"),
            (TransportMode.CAR_PUBLIC_TRANSPORT, "Voiture + TC"),
            (TransportMode.ELECTRIC_CAR, "Voiture électrique"),
            (TransportMode.CAR, "Voiture"),
            (TransportMode.MOTORCYCLE, "Moto"),
            (TransportMode.CARPOOLING, "Covoiturage"),
            (TransportMode.ELECTRIC_MOTORCYCLE, "Moto électrique"),
        ]
        self.mode_svg_icons = {
            TransportMode.WALK: "walk",
            TransportMode.PUBLIC_TRANSPORT: "bus",
            TransportMode.CAR: "car",
            TransportMode.BICYCLE: "bicycle",
            TransportMode.CARPOOLING: "carpooling",
            TransportMode.ELECTRIC_BICYCLE: "electric_bicycle",
            TransportMode.ELECTRIC_CAR: "electric_car",
            TransportMode.MOTORCYCLE: "motorcycle",
            TransportMode.FAST_BICYCLE: "fast_bicycle",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "bicycle_and_bus",
            TransportMode.CAR_PUBLIC_TRANSPORT: "car_and_bus",
            TransportMode.ELECTRIC_MOTORCYCLE: "electric_motorcycle",
        }
        self.basic_modes = [
            TransportMode.WALK,
            TransportMode.BICYCLE,
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.CAR,
        ]
        self.color_picker = ColorPicker()
        if generate_graphs:
            if use_cached_graphs:
                self.writer: BaseChartWriter = CachedChartWriter(output_dir)
            else:
                self.writer = ChartWriter(output_dir)
        else:
            self.writer = PlaceholderChartWriter(output_dir)
        self.cost_grapher = CostGrapher(output_dir)
        self.quantity_delta_grapher = QuantityDeltaGrapher(output_dir)
        self.multiple_quantity_delta_grapher = MultipleQuantityDeltaGrapher(output_dir)
        self.zfe_computer = ZFEImpactComputer(
            config["mobility"].get("territories_dir", ".")
        )
        self.illustrator = illustrator
        self.arcgis_identity = arcgis_identity

    def get_mapper(self, use_plotly: bool, style: str = "light") -> BaseMapMaker:
        if use_plotly:
            mapper: BaseMapMaker = PlotlyMapMaker(self.theme, self.writer, style)
        else:
            mapper = ArcGISMapMaker(self.writer, self.arcgis_identity)
        return mapper

    def _save_chart(
        self,
        chart: Any,
        name: str,
        width_height_ratio: float = 2,
        size: float = 1,
    ) -> str:
        height = size * 500
        chart.update_layout(width=width_height_ratio * height, height=height)
        return self.writer.write_chart(chart, name)

    def plot_travel_time_distribution(
        self, *scenario_indicators: ScenarioIndicators
    ) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)
        scenario_nicknames, comfort, acceptable, warning, critical = [], [], [], [], []
        for scenario in scenarios:
            scenario_nicknames.append(scenario.nickname)
            comfort.append(
                scenario.get_nb_employees_in(AccessibilitySlices.comfortable)
            )
            acceptable.append(
                scenario.get_nb_employees_in(AccessibilitySlices.acceptable)
            )
            warning.append(scenario.get_nb_employees_in(AccessibilitySlices.warning))
            critical.append(scenario.get_nb_employees_in(AccessibilitySlices.critical))
        total_commutes = self.indicators.study.nb_success_employees
        labelize = formatter(total_commutes)
        chart = go.Figure()
        bars = zip(
            ["< 20 min", "20 à 40 min", "40 à 60 min", "60+ minutes  "],
            [comfort, acceptable, warning, critical],
        )
        for legend, values in bars:
            chart.add_trace(
                go.Bar(
                    name=legend,
                    x=scenario_nicknames,
                    y=[100.0 * n / total_commutes for n in values],
                    text=[labelize(n) for n in values],
                )
            )
        chart.update_layout(
            template="base+vertical_light_range+stack_bar+criticity",
            title_text="Répartition des employés par tranche de temps de trajet pendulaire",
            legend_title_text="Part (et nombre) d'employés",
        )
        return self._save_chart(
            chart,
            f"travel_time_stacked_bar_{'_'.join([s.nickname for s in scenarios])}.svg",
        )

    @staticmethod
    def _get_access_times(
        scenario: ScenarioIndicators, slices: List[AccessibilitySlice]
    ) -> List[int]:
        return [scenario.get_nb_employees_in(s) for s in slices]

    def plot_travel_time_comparison(
        self,
        scenario: ScenarioIndicators,
        scenario_reference: Optional[ScenarioIndicators] = None,
    ) -> str:
        scenario_reference_nickname = ""
        categories = ["< 20 min", "20 à 40 min", "40 à 60 min", "60+ minutes"]
        slice = [
            AccessibilitySlices.comfortable,
            AccessibilitySlices.acceptable,
            AccessibilitySlices.warning,
            AccessibilitySlices.critical,
        ]
        labelize = formatter(self.indicators.study.nb_success_employees)

        scenario_data = self._get_access_times(scenario, slice)
        data = [
            go.Bar(
                name=scenario.nickname,
                x=categories,
                y=scenario_data,
                text=[labelize(n) for n in scenario_data],
                opacity=0.7,
                marker=dict(color=self.color_picker.pick("modelity")),
            )
        ]

        if scenario_reference is not None:
            scenario_reference_nickname = scenario_reference.nickname
            format_p = formatter_percents(self.indicators.study.nb_success_employees)
            scenario_reference_data = self._get_access_times(scenario_reference, slice)
            data.append(
                go.Scatter(
                    x=categories,
                    y=scenario_reference_data,
                    mode="lines+markers+text",
                    name=scenario_reference.nickname,
                    text=format_p(scenario_reference_data),
                    textposition="top center",
                    textfont=dict(color=self.color_picker.pick("critical")),
                    marker=dict(
                        symbol="circle",
                        color=self.color_picker.pick("critical"),
                        size=10,
                    ),
                )
            )
        layout = go.Layout(
            template="base+vertical_light_range",
            title_text="Répartition des employés par tranche de temps de trajet pendulaire",
        )
        chart = go.Figure(data=data, layout=layout)
        return self._save_chart(
            chart,
            f"travel_time_bar_{scenario_reference_nickname}_{scenario.nickname}.svg",
        )

    def plot_cumulative_access_time(
        self,
        scenario: ScenarioIndicators,
        scenario_reference: Optional[ScenarioIndicators] = None,
    ) -> str:
        scenario_reference_nickname = ""
        categories = ["20 min", "30 min", "40 min", "50 min", "1h", "1h30", "+ 1h30"]
        cumulative_slices = [
            AccessibilitySlice(None, 20 * 60),
            AccessibilitySlice(None, 30 * 60),
            AccessibilitySlice(None, 40 * 60),
            AccessibilitySlice(None, 50 * 60),
            AccessibilitySlice(None, 60 * 60),
            AccessibilitySlice(None, 90 * 60),
            AccessibilitySlice(None, None),
        ]
        labelize = formatter(self.indicators.study.nb_success_employees)
        scenario_data = self._get_access_times(scenario, cumulative_slices)
        data = [
            go.Bar(
                name=scenario.nickname,
                x=categories,
                y=scenario_data,
                text=[labelize(d) for d in scenario_data],
                opacity=0.7,
                marker=dict(
                    color=self.color_picker.pick("modelity"),
                ),
            )
        ]
        if scenario_reference is not None:
            scenario_reference_nickname = scenario_reference.nickname
            scenario_reference_data = self._get_access_times(
                scenario_reference, cumulative_slices
            )
            format_p = formatter_percents(self.indicators.study.nb_success_employees)
            data.append(
                go.Scatter(
                    name=scenario_reference.nickname,
                    x=categories,
                    y=scenario_reference_data,
                    mode="lines+markers+text",
                    text=format_p(scenario_reference_data),
                    textposition="middle left",
                    textfont=dict(color=self.color_picker.pick("critical")),
                    opacity=0.9,
                    marker=dict(
                        symbol="circle",
                        color=self.color_picker.pick("critical"),
                        size=10,
                    ),
                )
            )
        layout = go.Layout(
            template="base+vertical_light_range",
            title_text="Effectif (% et nombre) par temps d'accès au site",
        )
        chart = go.Figure(data=data, layout=layout)
        return self._save_chart(
            chart,
            f"cumulative_access_time_{scenario_reference_nickname}_{scenario.nickname}.svg",
        )

    def plot_travel_time_and_cumulative(self, scenario: ScenarioIndicators) -> str:
        categories = [
            "< 20 min",
            "20-30 min",
            "30-40 min",
            "40-50 min",
            "50-60 min",
            "1h-1h30",
            "> 1h30",
        ]
        slices = [
            AccessibilitySlice(None, 20 * 60),
            AccessibilitySlice(20 * 60, 30 * 60),
            AccessibilitySlice(30 * 60, 40 * 60),
            AccessibilitySlice(40 * 60, 50 * 60),
            AccessibilitySlice(50 * 60, 60 * 60),
            AccessibilitySlice(60 * 60, 90 * 60),
            AccessibilitySlice(90 * 60, None),
        ]

        labelize = formatter(self.indicators.study.nb_success_employees)
        scenario_data = self._get_access_times(scenario, slices)
        data = [
            go.Bar(
                name=scenario.nickname,
                x=categories,
                y=scenario_data,
                text=[labelize(d) for d in scenario_data],
                opacity=0.7,
                marker=dict(
                    color=self.color_picker.pick("modelity"),
                ),
            )
        ]

        cumulative_slices = [
            AccessibilitySlice(None, 20 * 60),
            AccessibilitySlice(None, 30 * 60),
            AccessibilitySlice(None, 40 * 60),
            AccessibilitySlice(None, 50 * 60),
            AccessibilitySlice(None, 60 * 60),
            AccessibilitySlice(None, 90 * 60),
            AccessibilitySlice(None, None),
        ]
        scenario_cumulative_data = self._get_access_times(scenario, cumulative_slices)
        format_p = formatter_percents(self.indicators.study.nb_success_employees)
        data.append(
            go.Scatter(
                name=f"% cumulé {scenario.nickname}",
                x=categories,
                y=scenario_cumulative_data,
                yaxis="y2",
                mode="lines+markers+text",
                text=format_p(scenario_cumulative_data),
                textposition="middle left",
                textfont=dict(color=self.color_picker.pick("critical")),
                opacity=0.9,
                marker=dict(
                    symbol="circle",
                    color=self.color_picker.pick("critical"),
                    size=10,
                ),
            )
        )

        layout = go.Layout(
            template="base+vertical_light_range",
            yaxis={"anchor": "x"},
            yaxis2={"side": "right", "overlaying": "y", "anchor": "x"},
            title_text="Répartition des employés par tranche de temps de trajet pendulaire",
        )
        return self._save_chart(
            go.Figure(data=data, layout=layout),
            f"travel_time_and_cumulative_{scenario.nickname}.svg",
        )

    def plot_travel_time_violins(self, *scenario_indicators: ScenarioIndicators) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)
        violins = []
        min_max: List[float] = []
        for scenario in scenarios:
            dist = [t / 60.0 for t in scenario.compute_travel_times()]
            min_max += dist
            min_max = [min(min_max), max(min_max)]
            dist.sort()
            violins.append(
                go.Violin(
                    y=dist,
                    meanline={"visible": True, "width": 4},
                    name=scenario.nickname if len(scenarios) > 1 else "",
                    points=False,
                    scalegroup="0",
                    showlegend=False,
                )
            )
        scatter = self._add_mean_scatter_legend("Moyenne des temps de trajet")
        y_ticks = time_scale(int(math.floor(min_max[0])), int(math.ceil(min_max[1])), 7)
        y_ticks_text = format_durations(y_ticks)
        layout = go.Layout(
            template="base+vertical+monochrome",
            yaxis_tickvals=y_ticks,
            yaxis_ticktext=y_ticks_text,
            title_text=f"Distribution{'s' if len(scenarios) > 1 else ''}"
            " des temps de trajet pendulaire des employés",
        )
        chart = go.Figure(data=violins + [scatter], layout=layout)
        return self._save_chart(
            chart,
            f"travel_time_violins_{'_'.join([s.nickname for s in scenarios])}.svg",
        )

    def plot_travel_time_comparison_violins(
        self,
        left_scenario_indicators: ScenarioIndicators,
        right_scenario_indicators: ScenarioIndicators,
    ) -> str:
        violins = []
        scenarios = zip(
            [left_scenario_indicators, right_scenario_indicators],
            ["negative", "positive"],
            [self.color_picker.pick("neutral"), self.color_picker.pick("modelity")],
        )
        min_max: List[float] = []
        for scenario, side, color in scenarios:
            dist = [t / 60.0 for t in scenario.compute_travel_times()]
            min_max += dist
            min_max = [min(min_max), max(min_max)]
            dist.sort()
            violins.append(
                go.Violin(
                    y0=0,
                    x=dist,
                    meanline={"visible": True, "width": 4},
                    points=False,
                    side=side,
                    name=scenario.nickname,
                    line_color=color,
                    orientation="h",
                    scalegroup="0",
                )
            )
        x_ticks = time_scale(int(math.floor(min_max[0])), int(math.ceil(min_max[1])), 6)
        x_ticks_text = format_durations(x_ticks)
        layout = go.Layout(
            template="base+horizontal_grid",
            showlegend=True,
            violinmode="overlay",
            xaxis_tickvals=x_ticks,
            xaxis_ticktext=x_ticks_text,
            title_text="Comparaison des temps de trajet actuels et futurs",
        )
        scatter = self._add_mean_scatter_legend("Moyenne des temps de trajet")
        chart = go.Figure(data=violins + [scatter], layout=layout)
        return self._save_chart(
            chart,
            f"travel_time_comparison_violins_{left_scenario_indicators.nickname}_{right_scenario_indicators.nickname}.svg",
        )

    def plot_travel_time_diff_violins(
        self, *inter_scenario_indicators: InterScenarioIndicators
    ) -> str:
        inter_scenarios = self._get_all_inter_scenarios_if_empty(
            inter_scenario_indicators
        )
        if len(inter_scenarios) == 1:
            layout_entries = "x"
            direction_template = "horizontal_grid"
            side = "positive"
        else:
            layout_entries = "y"
            direction_template = "vertical"
            side = "both"
        chart = go.Figure()
        min_max: List[float] = []
        for scenario in inter_scenarios:
            dist = [t / 60.0 for t in scenario.compute_diff_times()]
            min_max += dist
            min_max = [min(min_max), max(min_max)]
            dist.sort()
            chart.add_trace(
                go.Violin(
                    meanline={"visible": True, "width": 4},
                    name=scenario.get_nicknames()[1],
                    points=False,
                    scalegroup="0",
                    side=side,
                    showlegend=False,
                    **{layout_entries: dist},
                )
            )
        chart.add_trace(self._add_mean_scatter_legend("Moyenne des écarts de temps"))
        ticks = time_scale(int(math.floor(min_max[0])), int(math.ceil(min_max[1])), 7)
        ticks_text = format_durations(ticks, True)
        chart.update_layout(
            template=f"base+{direction_template}+monochrome",
            title_text="Évolution des écarts du temps de trajet par rapport au site actuel",
            yaxis=dict(
                zerolinecolor=self.color_picker.pick("dark_red"), zerolinewidth=2
            ),
        )
        chart.update_layout(
            **{
                f"{layout_entries}axis_tickvals": ticks,
                f"{layout_entries}axis_ticktext": ticks_text,
            }
        )
        return self._save_chart(
            chart,
            f"travel_time_diff_violins_{'_'.join(['_'.join(s.get_nicknames()) for s in inter_scenarios])}.svg",
        )

    def plot_scenarios_percent_access_time(self, percentile: int) -> str:
        nicknames = []
        percentiles = []
        for scenario in self.scenario_collectors:
            nicknames.append(scenario.nickname)
            p = scenario.compute_percentile(percentile)
            if p is None:
                p = 0.0
            percentiles.append(int(round(p / 60)))
        bar = go.Bar(
            x=nicknames,
            y=percentiles,
            text=format_durations(percentiles),
            textposition="auto",
        )
        layout = go.Layout(
            template="base+vertical_light_range",
            title_text=f"Temps d’accès pour {percentile}% des collaborateurs",
            colorway=[self.color_picker.pick("modelity_light")],
        )
        chart = go.Figure(data=[bar], layout=layout)
        return self._save_chart(
            chart, f"scenarios_{percentile}_percent_access_time.svg"
        )

    def plot_scenarios_minutes_access_time(
        self, accessibility_slice: AccessibilitySlice
    ) -> str:
        nicknames = []
        access_time = []
        for scenario in self.scenario_collectors:
            nicknames.append(scenario.nickname)
            access_time.append(scenario.get_nb_employees_in(accessibility_slice))
        total_commutes = self.indicators.study.nb_success_employees
        labelize = formatter(total_commutes)
        bar = go.Bar(
            x=nicknames,
            y=access_time,
            text=[labelize(n) for n in access_time],
            textposition="auto",
        )
        duration = (
            format_duration(accessibility_slice.upper_bound // 60)
            if accessibility_slice.upper_bound is not None
            else "oo min"
        )
        layout = go.Layout(
            template="base+vertical_light_range",
            title_text=f"Pourcentage d’accessibilité en {duration}",
            colorway=[self.color_picker.pick("modelity_light")],
        )
        chart = go.Figure(data=[bar], layout=layout)
        return self._save_chart(
            chart,
            f"scenarios_{accessibility_slice.upper_bound}_access_time.svg",
            1,
        )

    def plot_diff_times_stacked_bars(self) -> str:
        gains, stables, losses_acceptable, losses_warning, losses_critical = (
            [],
            [],
            [],
            [],
            [],
        )
        for scenario in self.indicators.inter_scenarios:
            (
                gain,
                stable,
                loss_acceptable,
                loss_warning,
                loss_critical,
            ) = self._get_diff_intervals(scenario)

            gains.append(gain)
            stables.append(stable)
            losses_acceptable.append(loss_acceptable)
            losses_warning.append(loss_warning)
            losses_critical.append(loss_critical)

        nicknames = [s.get_nicknames()[1] for s in self.indicators.inter_scenarios]
        labelize = formatter(self.indicators.study.nb_success_employees)
        data = zip(
            [
                f"Gain +{format_bond(AccessibilitySlices.gain.upper_bound)} min",
                "Stable",
                f"Perte +{format_bond(AccessibilitySlices.loss_acceptable.lower_bound)} min",
                f"Perte +{format_bond(AccessibilitySlices.loss_warning.lower_bound)} min",
                f"Perte +{format_bond(AccessibilitySlices.loss_critical.lower_bound)} min",
            ],
            [gains, stables, losses_acceptable, losses_warning, losses_critical],
        )
        bars = [
            go.Bar(
                name=legend, y=nicknames, x=values, text=[labelize(n) for n in values]
            )
            for legend, values in data
        ]
        layout = go.Layout(
            template="base+horizontal+stack_bar+criticity",
            title_text="Évolution des temps de trajet pour chaque site envisagé",
        )
        chart = go.Figure(data=bars, layout=layout)
        return self._save_chart(chart, "diff_times_stacked_bars.svg")

    def plot_detailed_diff_times_bars(self, scenario: InterScenarioIndicators) -> str:
        data = [
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(None, -60 * 60)
                ),
                "Gain > 1h",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-60 * 60, -50 * 60)
                ),
                "Gain > 50 min",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-50 * 60, -40 * 60)
                ),
                "Gain > 40 min",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-40 * 60, -30 * 60)
                ),
                "Gain > 30 min",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-30 * 60, -20 * 60)
                ),
                "Gain > 20 min",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-20 * 60, -5 * 60)
                ),
                "Gain > 5 min",
                self.color_picker.pick("comfort"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(-5 * 60, 5 * 60)
                ),
                "Stable",
                self.color_picker.pick("acceptable"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(5 * 60, 20 * 60)
                ),
                "Perte > 5 min",
                self.color_picker.pick("warning"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(20 * 60, 30 * 60)
                ),
                "Perte > 20 min",
                self.color_picker.pick("critical"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(30 * 60, 40 * 60)
                ),
                "Perte > 30 min",
                self.color_picker.pick("critical"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(40 * 60, 50 * 60)
                ),
                "Perte > 40 min",
                self.color_picker.pick("critical"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(50 * 60, 60 * 60)
                ),
                "Perte > 50 min",
                self.color_picker.pick("supercritical"),
            ),
            (
                scenario.count_employees_in_diff_interval(
                    AccessibilitySlice(60 * 60, None)
                ),
                "Perte > 1h",
                self.color_picker.pick("supercritical"),
            ),
        ]
        data = trim_zeros(data)
        labelize = formatter(self.indicators.study.nb_success_employees)

        bar = go.Bar(
            x=[slice for _, slice, _ in data],
            y=[time for time, _, _ in data],
            text=[labelize(time) for time, _, _ in data],
            textposition="auto",
            marker={"color": [color for _, _, color in data]},
        )

        layout = go.Layout(
            template="base+vertical_light_range",
            title_text="Écart des temps de trajet",
        )

        chart = go.Figure(data=bar, layout=layout)
        return self._save_chart(
            chart,
            f"detailed_diff_times_bars_{'_'.join(scenario.get_nicknames())}.svg",
        )

    def plot_failed_employees_distribution(self) -> str:
        pie_grapher = PieGrapher(self.output_dir, graph_scale=0.6)
        slices = [
            self.indicators.study.nb_success_employees,
            self.indicators.study.nb_failed_times_employees,
            self.indicators.study.nb_failed_geoloc_employees,
        ]
        colors = [
            self.color_picker.pick("modelity"),
            self.color_picker.pick("warning"),
            self.color_picker.pick("critical"),
        ]
        icons = [
            "journey",
            "no_journey",
            "no_geolocation",
        ]
        fslices, fcolors, ficons = [], [], []
        for s, c, i in zip(slices, colors, icons):
            if s > 0:
                fslices.append(float(s))
                fcolors.append(c)
                ficons.append(i)
        title = "Part des employés considérés dans l’étude"
        file_id = "failed_employees_distribution"
        return pie_grapher.make_pie_graph(
            title, [""], [fslices], [fcolors], [ficons], file_id
        )

    def plot_gain_and_loss(self, inter_scenario: InterScenarioIndicators) -> str:
        labels, values, texts = [], [], []
        data = [
            (
                "Gain de temps",
                f"-{round(inter_scenario.average_gain_faster_employees / 60)} min",
                inter_scenario.number_of_faster_commutes,
            ),
            (
                "Perte de temps",
                f"+{round(inter_scenario.average_loss_slower_employees / 60)} min",
                inter_scenario.number_of_slower_commutes,
            ),
        ]
        for label, text, value in data:
            if value != 0:
                labels.append(label)
                values.append(value)
                texts.append(
                    f"{formatter_percent(value, self.indicators.study.nb_success_employees)}<br>moy : {text}"
                )
        pie = go.Pie(
            labels=labels,
            values=values,
            hole=self.pie_hole,
            marker={
                "colors": [
                    self.color_picker.pick("modelity"),
                    self.color_picker.pick("neutral"),
                ]
            },
            text=texts,
            textinfo="text",
        )
        layout = go.Layout(
            template="base",
            title_text="Bilan des pertes et des gains",
        )
        chart = go.Figure(data=[pie], layout=layout)
        return self._save_chart(
            chart,
            f"gain_and_loss_{'_'.join(inter_scenario.get_nicknames())}.svg",
            0.8,
            1.6,
        )

    def plot_90_percentile_indicator(self, scenarios: InterScenarioIndicators) -> str:
        percentile = scenarios.scenario_indicator_to.compute_percentile(90)
        percentile_ref_ = scenarios.scenario_indicator_from.compute_percentile(90)
        chart = self._get_indicator_chart(
            round(percentile / 60) if percentile is not None else 0,
            "Temps d’accès pour 90% de l'effectif",
            round(percentile_ref_ / 60) if percentile_ref_ is not None else 0,
            suffix=" min",
        )
        return self._save_chart(
            chart,
            f"scenario_90_percentile_{'_'.join(scenarios.get_nicknames())}.svg",
            2.5,
            1 / 2,
        )

    def plot_present_scenario_mean_travel_time_indicator(
        self, scenario: ScenarioIndicators
    ) -> str:
        average_time = round(scenario.average_travel_time / 60)
        chart = self._get_indicator_chart(
            average_time, "Temps de transport moyen<br>porte à porte", suffix=" min"
        )
        return self._save_chart(
            chart,
            f"scenario_mean_travel_time_indicator_{scenario.nickname}.svg",
            2,
            1 / 2,
        )

    def plot_present_scenario_median_travel_time_indicator(
        self, scenario: ScenarioIndicators
    ) -> str:
        median_time_s = scenario.compute_median_time()
        median_time = round(median_time_s / 60) if median_time_s is not None else 0
        chart = self._get_indicator_chart(
            median_time, "Temps de transport médian<br>porte à porte", suffix=" min"
        )
        return self._save_chart(
            chart,
            f"scenario_median_travel_time_indicator_{scenario.nickname}.svg",
            2,
            1 / 2,
        )

    def plot_scenario_mean_travel_time_indicator(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        mean_time = round(inter_scenario.scenario_indicator_to.average_travel_time / 60)
        mean_time_ref = round(
            inter_scenario.scenario_indicator_from.average_travel_time / 60
        )
        chart = self._get_indicator_chart(
            mean_time,
            "Temps de transport moyen",
            reference_number=mean_time_ref,
            suffix=" min",
        )
        return self._save_chart(
            chart,
            f"scenario_mean_travel_time_indicator_{'_'.join(inter_scenario.get_nicknames())}.svg",
            2,
            1 / 2,
        )

    def plot_scenario_median_travel_time_indicator(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        median_time_s = inter_scenario.scenario_indicator_to.compute_median_time()
        median_time = round(median_time_s / 60) if median_time_s is not None else 0
        median_time_ref_s = inter_scenario.scenario_indicator_from.compute_median_time()
        median_time_ref = (
            round(median_time_ref_s / 60) if median_time_ref_s is not None else 0
        )
        chart = self._get_indicator_chart(
            median_time,
            "Temps de transport médian",
            reference_number=median_time_ref,
            suffix=" min",
        )
        return self._save_chart(
            chart,
            f"scenario_median_travel_time_indicator_{'_'.join(inter_scenario.get_nicknames())}.svg",
            2,
            1 / 2,
        )

    def plot_critical_cases_indicator(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        nb_critical_cases = (
            inter_scenario.critical_cases.loss_of_time
            + inter_scenario.critical_cases.high_present_travel_time_and_loss_of_time
            + inter_scenario.critical_cases.high_new_travel_time
        )
        chart = self._get_indicator_chart(nb_critical_cases, "Nombre de cas critiques")
        return self._save_chart(
            chart,
            f"critical_cases_indicator_{'_'.join(inter_scenario.get_nicknames())}.svg",
            2,
            1 / 2,
        )

    @staticmethod
    def _get_indicator_chart(
        number: float,
        title: str,
        reference_number: Optional[float] = None,
        suffix: str = "",
        relative: bool = False,
    ) -> go.Figure:
        if relative:
            delta = {
                "reference": reference_number,
                "relative": relative,
                "valueformat": ".1%",
            }
        else:
            delta = {"reference": reference_number, "relative": relative}
        indicator = go.Indicator(
            mode="number" if reference_number is None else "number+delta",
            value=number,
            title={"text": title},
            number={"suffix": suffix},
            delta=delta,
        )
        layout = go.Layout(
            template="base",
            margin={"l": 0, "r": 0, "t": 0, "b": 0},
        )
        return go.Figure(data=[indicator], layout=layout)

    def plot_gain_and_loss_mean_times(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        mean_time_gain = inter_scenario.average_gain_faster_employees
        mean_time_loss = inter_scenario.average_loss_slower_employees
        data = []
        bars = zip(
            ["Moyenne gain de temps", "Moyenne perte de temps  "],
            [mean_time_gain, mean_time_loss],
            [1.0, -1.0],
        )
        for name, value, factor in bars:
            data.append(
                go.Bar(
                    name=name,
                    x=[""],
                    y=[factor * value],
                    text=[format_duration(round(value / 60))],
                )
            )

        layout = go.Layout(
            template="base+vertical_light_range+wave_bar+accent",
            title_text="Moyennes de temps des pertes et des gains",
        )
        chart = go.Figure(data=data, layout=layout)
        return self._save_chart(
            chart,
            f"gain_and_loss_mean_times_{'_'.join(inter_scenario.get_nicknames())}.svg",
        )

    def plot_scenarios_mean_transport_time(self) -> str:
        data = [
            int(round(s.average_travel_time / 60)) for s in self.scenario_collectors
        ]
        chart = express.line(
            x=self.scenario_nicknames,
            y=data,
            text=format_durations(data),
            labels={"y": "", "x": ""},
            template="base+vertical_light_range+boulettes+monochrome",
            title="Évolution des temps de transport moyens des employés",
        )
        return self._save_chart(chart, "scenarios_mean_transport_time.svg")

    def add_mode_line_trace(
        self,
        chart: go.Figure,
        x: List,
        y: List,
        mode: TransportMode,
        text_format: Callable,
        annotations: List[Dict],
        row: Optional[int] = None,
        col: Optional[int] = None,
        vertical_padding: float = 0.0,
    ) -> None:
        name = str(mode)
        for transport_mode, label in self.modes:
            if mode == transport_mode:
                name = label
        chart.add_trace(
            go.Scatter(
                name=name,
                x=x,
                y=y,
                text=text_format(y),
                marker_color=self.color_picker.pick(mode),
            ),
            row=row,
            col=col,
        )
        non_none_y = [yi for yi in y if yi is not None]
        if vertical_padding > 0.0 and len(non_none_y) > 0:
            delta = vertical_padding * (max(non_none_y) - min(non_none_y))
            min_y = min(non_none_y) - delta
            max_y = max(non_none_y) + delta
            chart.update_layout({f"yaxis{row}": {"range": [min_y, max_y]}})
        if len(non_none_y) == 0:
            position = 0
            visible = False
        else:
            position = non_none_y[-1]
            visible = True
        annotations.append(
            dict(
                xref="paper",
                x=1.0,
                y=position,
                yref=f"y{row}" if row is not None else "y",
                xanchor="left",
                yanchor="middle",
                text=mode.charcode(),
                font={
                    "family": "ModelitySymbols",
                    "size": 40,
                    "color": self.color_picker.pick(mode),
                },
                showarrow=False,
                visible=visible,
            )
        )

    def plot_scenarios_mean_transport_time_per_transport_mode(self) -> str:
        chart = go.Figure()
        nicknames = [s.nickname for s in self.scenario_collectors]
        annotations: List[Dict] = []
        for mode, mode_label in self.modes:
            raw_times = [
                s.construct_average_travel_time(mode) for s in self.scenario_collectors
            ]
            times = [int(round(t / 60.0)) if t is not None else None for t in raw_times]
            self.add_mode_line_trace(
                chart,
                nicknames,
                times,
                mode,
                format_durations,
                annotations,
            )
        annotations_positions = space_values([a["y"] for a in annotations], 5.0)
        for ann, pos in zip(annotations, annotations_positions):
            ann["y"] = pos
        chart.update_layout(
            template="base+vertical_light_range+boulettes",
            annotations=annotations,
            showlegend=False,
            title_text="Temps de trajet par mode de transport",
        )
        return self._save_chart(
            chart,
            "scenarios_mean_transport_time_per_transport_mode.svg",
            1,
            2,
        )

    def plot_poi_times(self, scenario: PoiScenarioIndicators, site: GeoSite) -> str:
        annotations: List[Dict] = []
        modes = [
            TransportMode.CAR,
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.BICYCLE,
            TransportMode.WALK,
        ]
        rows = len(modes)
        chart = make_subplots(
            rows=rows, cols=1, shared_xaxes=True, vertical_spacing=0.27 / rows
        )
        access_times = scenario.get_poi_access_times(modes, site)
        valid_pois = self.indicators.study.get_pois()
        closest_poi = [
            p for p in scenario.get_closest_poi(access_times) if p in valid_pois
        ]
        for i, mode in enumerate(modes, start=1):
            poi_names, times = [], []
            for poi in closest_poi:
                poi_names.append(split_long_name(poi.name))
                time = access_times[poi][mode]
                times.append(int(time / minutes) if time is not None else time)
            self.add_mode_line_trace(
                chart,
                poi_names,
                times,
                mode,
                format_durations,
                annotations,
                row=i,
                col=1,
                vertical_padding=0.6,
            )
        chart.update_layout(
            template="base+vertical_light_range+boulettes",
            annotations=annotations,
            showlegend=False,
            title_text=f"Temps de trajet au départ du site {site.nickname}",
        )
        return self._save_chart(
            chart, f"poi_times_{scenario.nickname}.svg", width_height_ratio=2.8
        )

    def plot_scenario_mean_transport_time_per_transport_mode(
        self, scenario: ScenarioIndicators
    ) -> str:
        return self._save_chart(
            self.get_transport_time_per_mode_chart(scenario),
            f"scenario_mean_transport_time_per_transport_mode_{scenario.nickname}.svg",
        )

    def plot_scenario_diff_mean_transport_time_per_transport_mode(
        self, scenario: ScenarioIndicators, scenario_reference: ScenarioIndicators
    ) -> str:
        return self._save_chart(
            self.get_transport_time_per_mode_chart(scenario, scenario_reference),
            f"scenario_diff_mean_by_mode_{scenario_reference.nickname}_{scenario.nickname}.svg",
            1,
            2,
        )

    def get_transport_time_per_mode_chart(
        self,
        scenario: ScenarioIndicators,
        scenario_reference: Optional[ScenarioIndicators] = None,
    ) -> str:
        chart = go.Figure()
        mean_times = []
        colors = []
        annotations = []
        annotations_title = ""
        max_time_min = 0
        nb_modes_depicted = 4
        for i, (mode, _) in enumerate(self.modes):
            time_s = scenario.construct_average_travel_time(mode)
            time_min = int(round(time_s / 60)) if time_s else None
            if time_min:
                max_time_min = max(max_time_min, time_min)
                if mode not in self.basic_modes:
                    nb_modes_depicted += 1
        y_displayed = -1
        for i, (mode, _) in enumerate(self.modes):
            time_s = scenario.construct_average_travel_time(mode)
            if time_s is None and mode not in self.basic_modes:
                continue
            y_displayed += 1
            time_min = int(round(time_s / 60)) if time_s else None
            mean_times.append(time_min)
            colors.append(self.color_picker.pick(mode))
            annotations.append(
                {
                    "x": time_min + 0.1 * max_time_min if time_min else 1,
                    "xref": "x",
                    "y": y_displayed,
                    "xanchor": "left",
                    "yanchor": "middle",
                    "showarrow": False,
                    "text": mode.charcode(),
                    "font": {
                        "family": "ModelitySymbols",
                        "size": 320 / nb_modes_depicted,
                        "color": (
                            self.color_picker.pick(mode)
                            if time_min
                            else self.color_picker.pick("neutral")
                        ),
                    },
                }
            )
            if scenario_reference is not None:
                annotations_title = "Différence avec le site actuel (en minute)"
                ref_time_s = scenario_reference.construct_average_travel_time(mode)
                ref_time_min = int(round(ref_time_s / 60)) if ref_time_s else None
                diff_time = (
                    time_min - ref_time_min if time_min and ref_time_min else None
                )
                if diff_time and diff_time > 0:
                    bg_color = self.color_picker.pick("modelity_light")
                    font_color = "white"
                else:
                    bg_color = self.color_picker.pick("neutral")
                    font_color = "black"
                square_size = (800 - 40 * (nb_modes_depicted - 1)) / nb_modes_depicted
                annotations.append(
                    {
                        "width": square_size,
                        "height": square_size,
                        "x": -1,
                        "y": y_displayed,
                        "xanchor": "right",
                        "yanchor": "middle",
                        "showarrow": False,
                        "text": f"{diff_time:+} min" if diff_time else "=",
                        "font_color": font_color,
                        "bgcolor": bg_color,
                    }
                )
        chart.add_trace(
            go.Bar(
                x=mean_times,
                text=format_durations(mean_times),
                textposition="auto",
                marker_color=colors,
            )
        )
        chart.update_layout(
            template="base+horizontal_light",
            title="Temps de trajet moyen par mode de transport",
            annotations=annotations,
            yaxis_title_text=annotations_title,
        )
        return chart

    def plot_mean_transport_time_per_transport_mode_polar(
        self, *scenario_indicators: ScenarioIndicators
    ) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)

        polars = []
        labels = [label for _, label in self.modes]
        colors = [
            self.color_picker.pick("modelity"),
            self.color_picker.pick("critical"),
        ]
        positions = ["middle right", "middle left"]
        for position, color, scenario in zip(positions, colors, scenarios):
            mean_times = [
                scenario.construct_average_travel_time(mode) for mode, _ in self.modes
            ]
            polars.append(
                go.Scatterpolar(
                    mode="markers+text",
                    r=mean_times,
                    text=[
                        f"{int(round(t / 60, 0))}min" if t else 0 for t in mean_times
                    ],
                    textposition=position,
                    theta=labels,
                    fill="toself",
                    connectgaps=True,
                    name=scenario.nickname,
                    textfont=dict(color=color),
                    opacity=0.9,
                    marker=dict(
                        symbol="circle",
                        color=color,
                        size=10,
                    ),
                )
            )

        layout = go.Layout(
            template="base",
            polar={
                "radialaxis": {
                    "visible": False,
                    "rangemode": "normal",
                    "tickfont": dict(family="Lato", size=16, color="#555"),
                },
                "bgcolor": self.color_picker.pick("neutral2"),
                "gridshape": "linear",
            },
            title_text="Temps de trajet par mode de transport",
        )
        chart = go.Figure(data=polars, layout=layout)
        return self._save_chart(
            chart,
            f"mean_transport_time_per_transport_mode_polar_{'_'.join([s.nickname for s in scenarios])}.svg",
        )

    def plot_scenarios_gain_and_loss(self) -> str:
        def labelize(value: int, mean: float) -> str:
            _format = formatter(self.indicators.study.nb_success_employees)
            return f"{_format(value)}<br>moy : {format_duration(round(mean / 60))}"

        chart = go.Figure()
        scenario_nicknames, gain, mean_gain, loss, mean_loss, same = (
            [],
            [],
            [],
            [],
            [],
            [],
        )
        for scenario in self.indicators.inter_scenarios:
            scenario_nicknames.append(scenario.scenario_indicator_to.nickname)
            gain.append(scenario.number_of_strictly_faster_commutes)
            mean_gain.append(scenario.average_gain_strictly_faster_employees)
            loss.append(scenario.number_of_slower_commutes)
            mean_loss.append(scenario.average_loss_slower_employees)
            same.append(scenario.number_of_same_duration_commutes)
        total_commutes = self.indicators.study.nb_success_employees
        gain_bar = ("Gain de temps", (gain, mean_gain), 1.0, "modelity")
        loss_bar = ("Perte de temps  ", (loss, mean_loss), -1.0, "neutral")
        no_change_bar = (
            "Pas de changement",
            (same, [0.0] * len(same)),
            1.0,
            "modelity_light",
        )
        if any(nc > 0 for nc in same):
            bars = [no_change_bar, gain_bar, loss_bar]
        else:
            bars = [gain_bar, loss_bar]
        for name, data, factor, color in bars:
            values, means = data
            chart.add_trace(
                go.Bar(
                    name=name,
                    x=scenario_nicknames,
                    y=[factor * 100.0 * v / total_commutes for v in values],
                    text=[labelize(value, mean) for value, mean in zip(values, means)],
                    marker_color=self.color_picker.pick(color),
                )
            )
        chart.update_layout(
            template="base+vertical_light+wave_bar+accent",
            title_text="Bilan des pertes et des gains",
            legend_title_text="Part (et nombre) d'employés",
        )
        return self._save_chart(chart, "scenarios_gain_and_loss.svg")

    def plot_scenarios_carbon_emission(
        self,
        *scenarios_indicators: ScenarioIndicators,
        width_height_ratio: int = 2,
        size: int = 1,
    ) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenarios_indicators)
        nicknames, ges_car, ges_pt = [], [], []

        for scenario in scenarios:
            nicknames.append(scenario.nickname)
            ges_car.append(scenario.get_teq_carbon_emission(TransportMode.CAR))
            ges_pt.append(
                scenario.get_teq_carbon_emission(TransportMode.PUBLIC_TRANSPORT)
            )

        chart = go.Figure(
            data=[
                go.Bar(
                    name="Voiture",
                    x=nicknames,
                    y=ges_car,
                    text=ges_car,
                    marker={"color": self.color_picker.pick(TransportMode.CAR)},
                ),
                go.Bar(
                    name="Transport en commun",
                    x=nicknames,
                    y=ges_pt,
                    text=ges_pt,
                    marker={
                        "color": self.color_picker.pick(TransportMode.PUBLIC_TRANSPORT)
                    },
                ),
            ]
        )

        chart.update_layout(
            template="base+stack_bar+vertical_light_range",
            title_text="Émissions de gaz à effet de serre par an",
            legend_title_text="Émissions de gaz à effet de serre (teq/an)",
        )

        return self._save_chart(
            chart,
            f"carbon_emission_{'_'.join([s.nickname for s in scenarios])}.svg",
            width_height_ratio,
            size,
        )

    def plot_carbon_emission(
        self, scenario: ScenarioIndicators, width_height_ratio: int = 2, size: int = 1
    ) -> str:
        pie_grapher = PieGrapher(
            self.output_dir, absolute_value_formatter=lambda v: f" ({v * tonEC})"
        )
        slices, colors, icons = [], [], []
        for mode, label in self.modes:
            value = scenario.get_teq_carbon_emission(mode)
            if value != 0:
                slices.append(value)
                colors.append(self.color_picker.pick(mode))
                icons.append(self.mode_svg_icons[mode])
        title = "Émissions de gaz à effet de serre par an"
        file_id = f"carbon_emission_{scenario.nickname}.svg"
        return pie_grapher.make_pie_graph(
            title, [""], [slices], [colors], [icons], file_id
        )

    def plot_present_scenario_carbon_emission_indicator(
        self, scenario: ScenarioIndicators
    ) -> str:
        ges = int(scenario.get_teq_carbon_emission())
        chart = self._get_indicator_chart(ges, "Émissions de GES par an", suffix=" teq")
        return self._save_chart(
            chart,
            f"scenario_carbon_emission_indicator_{scenario.nickname}.svg",
            2,
            1 / 2,
        )

    def plot_carbon_emission_indicator(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        present_ges = inter_scenario.scenario_indicator_from.get_teq_carbon_emission()
        ges = inter_scenario.scenario_indicator_to.get_teq_carbon_emission()
        chart = self._get_indicator_chart(
            ges, "Émissions de GES par an", present_ges, suffix=" teq", relative=True
        )
        return self._save_chart(
            chart,
            f"carbon_emission_indicator_{'_'.join(inter_scenario.get_nicknames())}.svg",
            2,
            1 / 2,
        )

    def create_emission_bar_chart(
        self, y_label: str, x_value: int | float, color: str, title: str
    ) -> go.Figure:
        chart = go.Figure(
            data=[
                go.Bar(
                    y=[y_label],
                    x=[x_value],
                    marker_color=self.color_picker.pick(color),
                    text=[str(x_value * tonEC)],
                    textposition="outside",
                )
            ]
        )
        chart.update_layout(
            template="horizontal",
            xaxis=dict(showticklabels=False),
            yaxis=dict(showticklabels=False),
            showlegend=False,
            title=dict(text=title, font=dict(size=25)),
        )
        return chart

    def plot_objective_2050_yearly_carbon_emission_indicator_bar_chart(
        self,
    ) -> go.Figure:
        country = self.indicators.study.territory.country
        demonym = country.get_demonym()
        objective_ec = country.emissions.objective_ec
        objective_year = country.emissions.objective_year
        if country.emissions.objective_source != "":
            objective_source = f" ({country.emissions.objective_source})"
        else:
            objective_source = ""
        return self.create_emission_bar_chart(
            y_label=f"Objectif {objective_year}",
            x_value=int(objective_ec / tonEC),
            color="#05228C",
            title=(
                "Objectif de réduction de l'empreinte carbone d'un"
                f" {demonym} à l'horizon {objective_year}{objective_source}"
            ),
        )

    def plot_mean_employee_teq_carbon_emission_indicator_bar_chart(
        self, scenario: ScenarioIndicators
    ) -> go.Figure:
        mean_carbon_emission = scenario.get_yearly_carbon_emission_per_employee()
        mean_teq_carbon_emission = float(mean_carbon_emission / tonEC)
        return self.create_emission_bar_chart(
            y_label="Émissions de GES par employé",
            x_value=mean_teq_carbon_emission,
            color="#146767",
            title=(
                "Empreinte carbone moyenne des déplacements domicile-travail d'un salarié"
            ),
        )

    def plot_current_yearly_carbon_emission_indicator_bar_chart(self) -> go.Figure:
        country = self.indicators.study.territory.country
        demonym = country.get_demonym()
        current_ec = country.emissions.current_ec
        transport_share_current_ec = country.emissions.transport_share_current_ec
        chart = go.Figure(
            data=[
                go.Bar(
                    y=["Bilan carbone", "Bilan carbone"],
                    x=[
                        float(transport_share_current_ec / tonEC),
                        float(current_ec / tonEC)
                        - float(transport_share_current_ec / tonEC),
                    ],
                    marker=dict(
                        color=[
                            "#BBBFC1",
                            "#05228C",
                        ]
                    ),
                ),
            ]
        )

        chart.update_layout(
            template="horizontal+stack_bar",
            xaxis=dict(
                showticklabels=False,
            ),
            yaxis=dict(
                showticklabels=False,
            ),
            annotations=[
                dict(
                    x=float(transport_share_current_ec / tonEC),
                    y=-0.7,
                    text=str(transport_share_current_ec),
                    showarrow=False,
                ),
                dict(
                    x=int(current_ec / tonEC),
                    y=-0.7,
                    text=str(current_ec),
                    showarrow=False,
                ),
                dict(
                    x=float(transport_share_current_ec / tonEC) / 2,
                    y=0,
                    text="Part transport",
                    showarrow=False,
                    align="center",
                ),
            ],
            showlegend=False,
            title=dict(
                text=f"Empreinte carbone moyenne d'un {demonym} et part des transports",
                font=dict(size=25),
            ),
        )
        return chart

    def plot_yearly_carbon_emission_indicator_bar_chart(
        self, scenario: ScenarioIndicators, width_height_ratio: int = 2, size: int = 1
    ) -> str:
        objective_chart = (
            self.plot_objective_2050_yearly_carbon_emission_indicator_bar_chart()
        )
        mean_employee_chart = (
            self.plot_mean_employee_teq_carbon_emission_indicator_bar_chart(scenario)
        )
        current_chart = self.plot_current_yearly_carbon_emission_indicator_bar_chart()
        stacked_charts = [
            current_chart,
            objective_chart,
            mean_employee_chart,
        ]
        subplot_titles = [c.layout.title.text for c in stacked_charts]
        chart = make_subplots(
            rows=3,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=subplot_titles,
        )
        for i, c in enumerate(stacked_charts, start=1):
            for trace in c.data:
                chart.add_trace(trace, row=i, col=1)
            for annotation in c.layout.annotations:
                annotation["yref"] = f"y{i}"
                chart.add_annotation(annotation)
        for i in range(1, 4):
            chart.update_xaxes(showticklabels=False, row=i, col=1)
            chart.update_yaxes(showticklabels=False, row=i, col=1)
        for annotation in chart["layout"]["annotations"]:
            annotation["font"] = dict(size=26)
        chart.update_layout(
            template="base+horizontal+stack_bar",
            title_text="Émissions de GES",
            margin=dict(t=100, b=5),
            showlegend=False,
            plot_bgcolor="#FFFFFF",
        )
        return self._save_chart(
            chart,
            f"yearly_carbon_emission_indicator_bar_chart_{scenario.nickname}.svg",
            width_height_ratio,
            size,
        )

    def plot_carbon_emission_versus_country_goal(
        self, scenario: ScenarioIndicators
    ) -> str:
        country = self.indicators.study.territory.country
        if country.emissions.objective_source != "":
            objective_source = f" ({country.emissions.objective_source})"
        else:
            objective_source = ""
        charter = MultiBarGraphThresholdGrapher(self.output_dir)
        file_id = f"yearly_carbon_emission_vs_country_goal_{scenario.nickname}"
        title = (
            "Comparaison des émissions moyennes domicile-travail"
            f" avec l’objectif carbone national{objective_source}"
        )
        bars = {
            "Situation actuelle": {
                "all": country.emissions.current_ec,
                "transport": country.emissions.transport_share_current_ec,
            },
            f"Objectifs-cibles {country.emissions.objective_year}": {
                "all": country.emissions.objective_ec,
                "transport": country.emissions.transport_share_objective_ec,
            },
        }
        colors = {
            "Situation actuelle": {
                "all": "dark_grey",
                "transport": "darker_grey",
            },
            f"Objectifs-cibles {country.emissions.objective_year}": {
                "all": "green",
                "transport": "green",
            },
        }
        icons = {
            "all": "planet",
            "transport": "truck",
        }
        threshold = scenario.get_yearly_carbon_emission_per_employee()
        threshold_title = (
            f"émissions moyennes actuelles domicile-travail d'un employé "
            f"de {scenario.get_sites()[0].nickname}"
        )
        demonym = country.get_demonym()
        category_legend = {
            "all": f"Empreinte carbone moyenne d’un {demonym}",
            "transport": "Part du transport dans cette empreinte moyenne",
        }
        return charter.make_graph(
            file_id,
            title,
            bars,
            colors,
            icons,
            threshold,
            threshold_title,
            category_legend,
        )

    def plot_scenarios_critical_cases(
        self, *inter_scenario_indicators: InterScenarioIndicators
    ) -> str:
        inter_scenarios = self._get_all_inter_scenarios_if_empty(
            inter_scenario_indicators
        )
        nicknames, high_present_tt_and_loss, high_new_tt, loss_time = [], [], [], []
        labelize = formatter(self.indicators.study.nb_success_employees)

        for scenario in inter_scenarios:
            nicknames.append(scenario.get_nicknames()[1])
            critical_cases = scenario.critical_cases
            high_present_tt_and_loss.append(
                critical_cases.high_present_travel_time_and_loss_of_time
            )
            high_new_tt.append(critical_cases.high_new_travel_time)
            loss_time.append(critical_cases.loss_of_time)
        chart = go.Figure(
            data=[
                go.Bar(
                    name=f"Perte de temps > {InterScenarioIndicators.loss_of_time_threshold} min",
                    x=nicknames,
                    y=loss_time,
                    text=[labelize(n) for n in loss_time],
                ),
                go.Bar(
                    name=f"Nouveau temps de trajet > {InterScenarioIndicators.high_new_travel_time_threshold} min",
                    x=nicknames,
                    y=high_new_tt,
                    text=[labelize(n) for n in high_new_tt],
                ),
                go.Bar(
                    name=f"Perte de temps avec trajet actuel > {InterScenarioIndicators.high_present_travel_time_and_loss_of_time_threshold} min",
                    x=nicknames,
                    y=high_present_tt_and_loss,
                    text=[labelize(n) for n in high_present_tt_and_loss],
                ),
            ]
        )

        chart.update_layout(
            template="base+stack_bar+vertical_light_range+criticity",
            title_text="Cas critiques cumulés",
        )

        return self._save_chart(
            chart, f"scenarios_critical_cases_{'_'.join(nicknames)}.svg"
        )

    def plot_scenarios_line_critical_cases(self) -> str:
        loss_of_time = []
        high_new_travel_time = []
        high_present_travel_time_and_loss_of_time = []
        nicknames = []
        labelize = formatter_percents(self.indicators.study.nb_success_employees)

        for scenario in self.indicators.inter_scenarios:
            nicknames.append(scenario.get_nicknames()[1])
            loss_of_time.append(scenario.critical_cases.loss_of_time)
            high_new_travel_time.append(scenario.critical_cases.high_new_travel_time)
            high_present_travel_time_and_loss_of_time.append(
                scenario.critical_cases.high_present_travel_time_and_loss_of_time
            )

        chart = make_subplots(
            rows=3,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.09,
            subplot_titles=(
                "Temps actuel supérieur à 1h et rallongement du temps de trajet après déménagement",
                f"Nouveau temps de trajet supérieur à {InterScenarioIndicators.high_new_travel_time_threshold} minutes",
                f"Perte de temps supérieure à {InterScenarioIndicators.loss_of_time_threshold} minutes",
            ),
        )

        chart.add_trace(
            go.Scatter(x=nicknames, y=loss_of_time, text=labelize(loss_of_time)),
            row=3,
            col=1,
        )
        chart.add_trace(
            go.Scatter(
                x=nicknames,
                y=high_new_travel_time,
                text=labelize(high_new_travel_time),
            ),
            row=2,
            col=1,
        )
        chart.add_trace(
            go.Scatter(
                x=nicknames,
                y=high_present_travel_time_and_loss_of_time,
                text=labelize(high_present_travel_time_and_loss_of_time),
            ),
            row=1,
            col=1,
        )

        chart.update_layout(
            showlegend=False,
            template="base+vertical_light_range+criticity+boulettes",
        )
        for subplot_title in chart["layout"]["annotations"]:
            subplot_title["font"] = {
                "family": "Lato",
                "size": 22,
                "color": self.color_picker.pick("modelity"),
            }
        return self._save_chart(chart, "scenarios_line_critical_cases.svg")

    def plot_critical_cases(self, scenario: InterScenarioIndicators) -> str:
        diff_times, present_travel_times = [], []
        for _, times in scenario.get_impacted_employees(None, None, None).items():
            diff_times.append(times["diff"] / 60)
            present_travel_times.append(times["duration_from"] / 60)

        chart = go.Figure()
        x_time_scale = time_scale(
            int(math.floor(min(diff_times))), int(math.ceil(max(diff_times))), 10
        )
        y_time_scale = time_scale(
            int(math.floor(min(present_travel_times))),
            int(math.ceil(max(present_travel_times))),
            10,
        )
        chart.add_trace(
            go.Histogram2d(
                x=diff_times,
                xgap=0,
                ygap=0,
                y=present_travel_times,
                histfunc="count",
                histnorm="percent",
                xbins={
                    "start": x_time_scale[0],
                    "end": x_time_scale[-1],
                    "size": x_time_scale[1] - x_time_scale[0],
                },
                ybins={
                    "start": y_time_scale[0],
                    "end": y_time_scale[-1],
                    "size": y_time_scale[1] - y_time_scale[0],
                },
                colorbar={
                    "ticks": "",
                    "ticksuffix": "%",
                    "title": "densité<br>de l'effectif",
                },
                colorscale="Blues",
            )
        )
        chart.add_trace(
            go.Scatter(
                x=diff_times,
                y=present_travel_times,
                mode="markers",
                showlegend=False,
                marker=dict(
                    symbol="circle",
                    opacity=0.7,
                    color="white",
                    size=8,
                    line={"width": 1},
                ),
            )
        )
        x_ticks_text = format_durations(x_time_scale, True)
        y_ticks_text = format_durations(y_time_scale)
        chart.update_layout(
            xaxis=dict(
                title={"text": "Écart des temps après déménagement"},
                ticks="",
                showgrid=True,
                zeroline=False,
                showline=False,
                tickvals=x_time_scale,
                ticktext=x_ticks_text,
            ),
            yaxis=dict(
                title={"text": "Temps de trajet actuel", "standoff": 20},
                ticks="",
                showgrid=True,
                zeroline=False,
                tickvals=y_time_scale,
                ticktext=y_ticks_text,
            ),
            template="base+vertical+monochrome",
            plot_bgcolor="white",
            title_text="Employés par temps de trajet et écart de temps après déménagement",
        )
        return self._save_chart(
            chart,
            f"critical_cases_{'_'.join([n for n in scenario.get_nicknames()])}.svg",
            3 / 2,
            2.5,
        )

    def plot_critical_travel_time(self, scenario: ScenarioIndicators) -> str:
        data = [
            scenario.get_nb_employees_in(AccessibilitySlice(60 * 60, 70 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(70 * 60, 80 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(80 * 60, 90 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(90 * 60, None)),
        ]
        data_cumulative = [
            scenario.get_nb_employees_in(AccessibilitySlice(60 * 60, 70 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(60 * 60, 80 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(60 * 60, 90 * 60)),
            scenario.get_nb_employees_in(AccessibilitySlice(60 * 60, None)),
        ]

        total_commutes = self.indicators.study.nb_success_employees
        labelize = formatter(total_commutes)
        categories = ["1h à 1h10", "1h10 à 1h20", "1h20 à 1h30", "+1h30"]
        bar = go.Bar(
            name=scenario.nickname,
            x=categories,
            y=data,
            text=[labelize(n) for n in data],
            opacity=0.7,
            marker=dict(
                color=self.color_picker.pick("modelity"),
            ),
        )
        percent_cumulative_data = [
            100 * d / self.indicators.study.nb_success_employees
            for d in data_cumulative
        ]
        scatter = go.Scatter(
            x=categories,
            y=percent_cumulative_data,
            yaxis="y2",
            mode="lines+markers+text",
            name=f"% cumulé {scenario.nickname}",
            text=[f"{round(p)}%" for p in percent_cumulative_data],
            textposition="top center",
            textfont=dict(color=self.color_picker.pick("critical")),
            opacity=0.9,
            marker=dict(
                symbol="circle",
                color=self.color_picker.pick("critical"),
                size=10,
            ),
        )
        layout = go.Layout(
            template="base+vertical_light_range",
            yaxis={"anchor": "x"},
            yaxis2={"side": "right", "overlaying": "y", "anchor": "x"},
            title_text="Collaborateurs avec un temps de trajet supérieur"
            " à 60 minutes",
        )
        chart = go.Figure(data=[bar, scatter], layout=layout)
        return self._save_chart(chart, f"critical_travel_time_{scenario.nickname}.svg")

    def plot_employee_variance(self, *scenarios_indicators: ScenarioIndicators) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenarios_indicators)
        nicknames = [s.nickname for s in scenarios]
        labelize = formatter_percents(self.indicators.study.nb_success_employees)
        annotations: List[Dict] = []
        chart = go.Figure()

        for mode in [TransportMode.PUBLIC_TRANSPORT, TransportMode.WALK]:
            nb_employees = [s.count_employees_per_mode(mode) for s in scenarios]
            self.add_mode_line_trace(
                chart, nicknames, nb_employees, mode, labelize, annotations
            )
        chart.update_layout(
            template="base+vertical_light_range+boulettes",
            annotations=annotations,
            showlegend=False,
            title_text="Évolution des effectifs par mode de transport",
        )
        return self._save_chart(
            chart,
            f"employee_variance_lines_{'_'.join([s.nickname for s in scenarios])}.svg",
        )

    def plot_declared_modes_pie(self, *scenario_indicators: ScenarioIndicators) -> str:
        pie_grapher = PieGrapher(self.output_dir)
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)
        modes_and_label: List[Tuple[Optional[TransportMode], str]] = []
        modes_and_label += self.modes
        modes_and_label += [(None, "Inconnu")]
        slices = []
        colors = []
        icons = []
        subtitles = []
        for scenario in scenarios:
            repartition_per_mode = (
                scenario.count_employees_repartition_per_declared_mode()
            )
            sc_values, sc_icons, sc_colors = [], [], []
            for mode, label in modes_and_label:
                value = repartition_per_mode[mode]
                if value != 0:
                    sc_values.append(float(value))
                    if mode is None:
                        sc_icons.append("employee")
                        sc_colors.append(self.color_picker.grey)
                    else:
                        sc_icons.append(self.mode_svg_icons[mode])
                        sc_colors.append(self.color_picker.pick(mode))
            slices.append(sc_values)
            colors.append(sc_colors)
            icons.append(sc_icons)
            subtitles.append(scenario.nickname)
        if len(subtitles) == 1:
            subtitles = [""]
        title = "Effectif par mode de transport fourni à Modelity"
        file_id = f"modal_split_pies_{'_'.join([s.nickname for s in scenarios])}"
        return pie_grapher.make_pie_graph(
            title, subtitles, slices, colors, icons, file_id
        )

    def plot_unknown_modes_pie(self, *scenario_indicators: ScenarioIndicators) -> str:
        pie_grapher = PieGrapher(self.output_dir)
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)
        slices = []
        colors = []
        icons = []
        subtitles = []
        for scenario in scenarios:
            repartition_per_mode = (
                scenario.count_employees_with_inferred_mode_repartition_per_mode()
            )
            sc_values, sc_icons, sc_colors = [], [], []
            for mode, label in self.modes:
                value = repartition_per_mode[mode]
                if value != 0:
                    sc_values.append(float(value))
                    sc_icons.append(self.mode_svg_icons[mode])
                    sc_colors.append(self.color_picker.pick(mode))
            slices.append(sc_values)
            colors.append(sc_colors)
            icons.append(sc_icons)
            subtitles.append(scenario.nickname)
        if len(subtitles) == 1:
            subtitles = [""]
        title = "Modes de transport modélisés pour les modes inconnus"
        file_id = (
            f"modal_unknown_split_pies_{'_'.join([s.nickname for s in scenarios])}.svg"
        )
        return pie_grapher.make_pie_graph(
            title, subtitles, slices, colors, icons, file_id
        )

    def plot_modal_split(self, *scenarios_indicators: ScenarioIndicators) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenarios_indicators)
        nicknames = []
        for scenario in scenarios:
            nicknames.append(scenario.nickname)
        labelize = formatter(self.indicators.study.nb_success_employees)
        chart = go.Figure()
        for mode, mode_label in self.modes:
            repartition = [
                s.count_employees_repartition_per_mode()[mode] for s in scenarios
            ]
            chart.add_trace(
                go.Bar(
                    name=mode_label + "  ",
                    y=nicknames,
                    x=repartition,
                    text=[labelize(n) for n in repartition],
                    marker_color=self.color_picker.pick(mode),
                )
            )
        chart.update_layout(
            template="base+horizontal+stack_bar",
            title_text="Répartition modale actuelle des déplacements pendulaires",
        )

        return self._save_chart(
            chart,
            f"modal_split_stacked_bar_{'_'.join([s.nickname for s in scenarios])}.svg",
        )

    def plot_modal_split_when_modal_shift_bicycle(self) -> str:
        labelize = formatter(self.indicators.study.nb_success_employees)
        chart = go.Figure()
        bike_shift_indicators = list(
            reversed(self.indicators.present_scenario.get_bike_shift_scenarios())
        )
        present_indicator = self.indicators.present_scenario
        modal_split = {
            mode: [
                i.employees_count_per_mode.get(mode, 0) for i in bike_shift_indicators
            ]
            + [present_indicator.count_employees_per_mode(mode)]
            for mode in TransportMode
        }
        for mode, mode_label in self.modes:
            repartition = modal_split[mode]
            if all(n <= 0 for n in repartition):
                continue
            chart.add_trace(
                go.Bar(
                    name=mode_label + "  ",
                    y=[split_long_name(i.spec.nickname) for i in bike_shift_indicators]
                    + [split_long_name(present_indicator.nickname)],
                    x=repartition,
                    text=[labelize(n) for n in repartition],
                    marker_color=self.color_picker.pick(mode),
                )
            )
        chart.update_layout(
            template="base+horizontal+stack_bar",
            title_text="Évolution des effectifs par scénario d'adoption du vélo",
            legend_traceorder="reversed",
        )
        return self._save_chart(chart, "modal_split_when_modal_shift_bicycle.svg", 3)

    def plot_modal_split_when_modal_shift_public_transport(self) -> str:
        labelize = formatter(self.indicators.study.nb_success_employees)
        chart = go.Figure()
        pt_shift_indicators = list(
            reversed(
                self.indicators.present_scenario.get_public_transport_shift_scenarios()
            )
        )
        present_indicator = self.indicators.present_scenario
        modal_split = {
            mode: [i.employees_count_per_mode.get(mode, 0) for i in pt_shift_indicators]
            + [present_indicator.count_employees_per_mode(mode)]
            for mode in TransportMode
        }
        for mode, mode_label in self.modes:
            repartition = modal_split[mode]
            if all(n <= 0 for n in repartition):
                continue
            chart.add_trace(
                go.Bar(
                    name=mode_label + "  ",
                    y=[split_long_name(i.spec.nickname) for i in pt_shift_indicators]
                    + [split_long_name(present_indicator.nickname)],
                    x=repartition,
                    text=[labelize(n) for n in repartition],
                    marker_color=self.color_picker.pick(mode),
                )
            )
        chart.update_layout(
            template="base+horizontal+stack_bar",
            title_text="Évolution des effectifs par scénario d'adoption des TC",
            legend_traceorder="reversed",
        )
        return self._save_chart(
            chart,
            "modal_split_when_modal_shift_public_transport.svg",
            width_height_ratio=3,
        )

    def _count_employees_in_time_interval(
        self,
        mapping: ImmutableDict[GeoEmployee, Quantity],
        min_bound: Optional[Quantity],
        max_bound: Optional[Quantity],
    ) -> int:
        nb = 0
        for quantity in mapping.values():
            if (min_bound is None or min_bound <= quantity) and (
                max_bound is None or quantity < max_bound
            ):
                nb += 1
        return nb

    def plot_transport_times_when_modal_shift_bicycle(self) -> str:
        labelize = formatter(self.indicators.study.nb_success_employees)
        indicators = self.indicators.present_scenario.get_bike_shift_scenarios()
        transport_times = {
            "< 10 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, None, 10 * minutes
                )
                for i in indicators
            ],
            "10 à 20 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, 10 * minutes, 20 * minutes
                )
                for i in indicators
            ],
            "> 20 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, 20 * minutes, None
                )
                for i in indicators
            ],
        }
        nicknames = [i.spec.nickname for i in indicators]
        bars = [
            go.Bar(
                name=legend, x=nicknames, y=values, text=[labelize(v) for v in values]
            )
            for legend, values in transport_times.items()
        ]
        layout = go.Layout(
            template="base+vertical_light+stack_bar+criticity",
            title_text="Temps de transport des nouveaux cyclistes",
        )
        chart = go.Figure(data=bars, layout=layout)
        return self._save_chart(
            chart,
            "plot_transport_times_when_modal_shift_bicycle.svg",
            width_height_ratio=3,
        )

    def plot_transport_times_when_modal_shift_public_transport(self) -> str:
        labelize = formatter(self.indicators.study.nb_success_employees)
        indicators = (
            self.indicators.present_scenario.get_public_transport_shift_scenarios()
        )
        transport_times = {
            "< 20 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, None, 20 * minutes
                )
                for i in indicators
            ],
            "20 à 40 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, 20 * minutes, 40 * minutes
                )
                for i in indicators
            ],
            "40 à 60 min": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, 40 * minutes, 60 * minutes
                )
                for i in indicators
            ],
            "+ 1h": [
                self._count_employees_in_time_interval(
                    i.shifters_travel_time, 60 * minutes, None
                )
                for i in indicators
            ],
        }
        nicknames = [i.spec.nickname for i in indicators]
        bars = [
            go.Bar(
                name=legend, x=nicknames, y=values, text=[labelize(n) for n in values]
            )
            for legend, values in transport_times.items()
        ]
        layout = go.Layout(
            template="base+vertical_light+stack_bar+criticity",
            title_text="Temps de transport des nouveaux salariés en transport en commun",
            legend_traceorder="reversed",
        )
        chart = go.Figure(data=bars, layout=layout)
        return self._save_chart(
            chart,
            "plot_transport_times_when_modal_shift_public_transport.svg",
            width_height_ratio=3,
        )

    def plot_remote_working_transport_mode_distribution(self) -> str:
        remote_scenarios = self.indicators.present_scenario.get_remote_scenarios()
        chart = go.Figure()
        for mode, mode_label in self.modes:
            values = []
            for remote_scenario in remote_scenarios:
                values.append(
                    remote_scenario.remote_employees_count_per_mode.get(mode, 0)
                )
            chart.add_trace(
                go.Bar(
                    name=mode_label,
                    x=["Scénario 1", "Scénario 2", "Scénario 3", "Scénario 4"],
                    y=values,
                    text=[n for n in values],
                    marker_color=self.color_picker.pick(mode),
                )
            )
        chart.update_layout(
            template="base+vertical_light+stack_bar",
            title_text="Modes de transport des télétravailleurs retenus",
        )
        return self._save_chart(
            chart, "remote_working_transport_mode_distribution.svg", 1, 2
        )

    def plot_staggered_hours_mean_times(self) -> str:
        chart = go.Figure()
        indicators = self.indicators.present_scenario.staggered_hours_indicators
        scenarios = [i.nickname for i in indicators]
        annotations: List[Dict] = []

        def to_minutes(q: Quantity) -> int:
            return int(q / minutes)

        mean_times = [apply_option(i.mean_duration, to_minutes) for i in indicators]

        for mode in [TransportMode.CAR, TransportMode.PUBLIC_TRANSPORT]:
            times = [
                apply_option(i.mean_duration_per_mode.get(mode), to_minutes)
                for i in indicators
            ]
            self.add_mode_line_trace(
                chart, scenarios, times, mode, format_durations, annotations
            )
        chart.add_trace(
            go.Scatter(
                name="Tous les modes",
                x=scenarios,
                y=mean_times,
                text=format_durations(mean_times),
                marker_color=self.color_picker.pick("modelity_light"),
            )
        )
        annotations_positions = space_values([a["y"] for a in annotations], 5.0)
        for ann, pos in zip(annotations, annotations_positions):
            ann["y"] = pos
        chart.update_layout(
            template="base+vertical_light_range+boulettes",
            annotations=annotations,
            showlegend=True,
            title_text="Temps de transport moyen selon l'heure d'arrivée au travail",
        )
        return self._save_chart(chart, "staggered_hours_mean_times.svg", 1, 2)

    def plot_modal_split_pie(self, *scenario_indicators: ScenarioIndicators) -> str:
        scenarios = self._get_all_scenarios_if_empty(scenario_indicators)
        pie_grapher = PieGrapher(self.output_dir)
        subtitles, slices, colors, icons = [], [], [], []
        for scenario in scenarios:
            sc_slices, sc_colors, sc_icons = [], [], []
            repartition_per_mode = scenario.count_employees_repartition_per_mode()
            for mode, label in self.modes:
                if repartition_per_mode[mode] <= 0:
                    continue
                sc_slices.append(float(repartition_per_mode[mode]))
                sc_colors.append(self.color_picker.pick(mode))
                sc_icons.append(self.mode_svg_icons[mode])
            slices.append(sc_slices)
            colors.append(sc_colors)
            icons.append(sc_icons)
            if len(scenarios) > 1:
                subtitles.append(scenario.nickname)
            else:
                subtitles.append("")
        title = "Répartition modale actuelle des déplacements pendulaires"
        file_id = "modal_split_" + "_".join([s.nickname for s in scenarios])
        return pie_grapher.make_pie_graph(
            title, subtitles, slices, colors, icons, file_id
        )

    def _get_all_scenarios_if_empty(
        self, scenario: Tuple[ScenarioIndicators, ...]
    ) -> List[ScenarioIndicators]:
        return list(scenario) if len(scenario) > 0 else self.scenario_collectors

    def plot_employees_and_site_map(
        self,
        scenario: ScenarioIndicators,
        frame_all: bool = False,
        height_cm: int = 10,
        width_cm: int = 10,
        color_by_mode: bool = True,
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(use_plotly)
        if color_by_mode:
            self.add_employees_by_mode_to_map(mapper, scenario)
        else:
            self.add_employees_by_site_to_map(mapper, scenario)
        self.add_sites_to_map(mapper, scenario)
        if frame_all:
            mapper.set_framing_strategy(FrameAllPoints())
        else:
            mapper.set_framing_strategy(
                FrameStandardDeviation(
                    radius=1.0,
                    must_frame=list(scenario.get_sites_geocoordinates().values()),
                )
            )
        mapper.set_legend("")
        if color_by_mode:
            mapper.set_title("Localisation des employés par mode de transport")
        else:
            mapper.set_title("Localisation des employés par site de rattachement")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height,
            width,
            f"employee_location_{scenario.nickname}_{frame_all}_{color_by_mode}.svg",
        )

    def plot_employees_and_site_map_ideal_scenario(
        self,
        base_scenario: ScenarioIndicators,
        frame_all: bool = False,
        height_cm: float = 10,
        width_cm: float = 10,
        kind: str = "both",
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(use_plotly)
        modes = [
            (TransportMode.WALK, "Marche à pied"),
            (TransportMode.BICYCLE, "Vélo"),
            (TransportMode.ELECTRIC_BICYCLE, "Vélo électrique"),
            (TransportMode.PUBLIC_TRANSPORT, "Transport en commun"),
            (TransportMode.ELECTRIC_CAR, "Voiture électrique"),
            (TransportMode.CAR, "Voiture"),
            (TransportMode.MOTORCYCLE, "Moto"),
            (TransportMode.CARPOOLING, "Covoiturage"),
            (TransportMode.ELECTRIC_MOTORCYCLE, "Moto électrique"),
            (TransportMode.FAST_BICYCLE, "Vélo électrique 45km/h"),
            (TransportMode.CAR_PUBLIC_TRANSPORT, "Voiture + TC"),
            (TransportMode.BICYCLE_PUBLIC_TRANSPORT, "Vélo + TC"),
            (WorkMode.HOME_OFFICE, "Télétravail"),
            (WorkMode.COWORKING, "Coworking"),
            (WorkMode.ON_SITE_FULL_TIME, "Sur site"),
        ]
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        employees_modes = {
            c.origin: c.data.best_mode for c in base_scenario.scenario.commutes
        }
        TUnion = Union[str, TransportMode, WorkMode]
        points: Dict[TUnion, List[GeoCoordinates]] = {}
        for employee, mode_shift in employees_plan.items():
            coords = employee.coordinates
            best_mode: Union[str, TransportMode, WorkMode] = employees_modes[employee]
            if mode_shift == "WALK":
                if kind == "both" or kind == "transport_mode":
                    best_mode = TransportMode.WALK
                elif kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            elif mode_shift == "BICYCLE":
                if kind == "both" or kind == "transport_mode":
                    best_mode = TransportMode.BICYCLE
                elif kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            elif mode_shift == "PUBLIC_TRANSPORT":
                if kind == "both" or kind == "transport_mode":
                    best_mode = TransportMode.PUBLIC_TRANSPORT
                elif kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            elif mode_shift == "CARPOOLING":
                if kind == "both" or kind == "transport_mode":
                    best_mode = TransportMode.CARPOOLING
                elif kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            elif mode_shift == "CAR":
                if kind == "both" or kind == "transport_mode":
                    best_mode = TransportMode.CAR
                elif kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            elif mode_shift == "REMOTE_WORKING":
                if kind == "both" or kind == "work_mode":
                    best_mode = WorkMode.HOME_OFFICE
            elif mode_shift == "COWORKING":
                if kind == "both" or kind == "work_mode":
                    best_mode = WorkMode.COWORKING
            elif mode_shift == "NO_CHANGE":
                if kind == "work_mode":
                    best_mode = WorkMode.ON_SITE_FULL_TIME
            if best_mode in points.keys():
                points[best_mode].append(coords)
            else:
                points[best_mode] = [coords]
        for mode, list_coords in points.items():
            color = self.color_picker.pick(mode)
            mapper.add_points(list_coords, [], color, 0.25, 1, dict(modes)[mode])
        self.add_sites_to_map(mapper, base_scenario)
        if frame_all:
            mapper.set_framing_strategy(FrameAllPoints())
        else:
            mapper.set_framing_strategy(
                FrameStandardDeviation(
                    radius=1.0,
                    must_frame=list(base_scenario.get_sites_geocoordinates().values()),
                )
            )
        mapper.set_legend("")
        if kind == "both":
            mapper.set_title("Mode de travail potentiel des employés")
        elif kind == "transport_mode":
            mapper.set_title("Mode de transport potentiel des employés")
        elif kind == "work_mode":
            mapper.set_title("Lieu de travail potentiel des employés")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height,
            width,
            f"employee_location_{base_scenario.nickname}_ideal_{kind}_{frame_all}.svg",
        )

    def plot_coworking_map(
        self,
        scenario: ScenarioIndicators,
        width_cm: float = 10.0,
        height_cm: float = 10.0,
        use_plotly: bool = False,
    ) -> str:
        coworking_indicators = scenario.coworking_indicators
        mapper = self.get_mapper(use_plotly)
        site_color_index = self.color_picker.colorize(
            list(coworking_indicators.sites_coworkers)
        )
        employees_legend: Optional[str] = "Employés"
        site_legend: Optional[str] = "Lieux de coworking"
        for site in coworking_indicators.sites_coworkers:
            employees = coworking_indicators.sites_coworkers[site]
            if site in site_color_index:
                cluster_color = self.color_picker.pick_site_color(
                    site_color_index[site]
                )
            else:
                cluster_color = self.color_picker.pick_site_color(0)
            mapper.add_points(
                [e.coordinates for e in employees],
                [],
                cluster_color,
                size_cm=0.25,
                opacity=1,
                name_in_legend=employees_legend,
            )
            employees_legend = None
            mapper.add_points(
                [site.get_coordinates()],
                [],
                cluster_color,
                size_cm=0.3,
                opacity=1,
                name_in_legend=site_legend,
                marker_style=PointSymbolStyles.STAR,
            )
            site_legend = None
        if len(coworking_indicators.sites_coworkers) == 0:
            # Put something on the map if no coworking (avoid framing error in map maker)
            self.add_sites_to_map(mapper, scenario)
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(height, width, f"coworking_{scenario.nickname}.svg")

    def plot_clustering_map(
        self,
        scenario: ScenarioIndicators,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
        use_plotly: bool = False,
    ) -> str:
        cluster_indicators = scenario.clustering_indicators
        mapper = self.get_mapper(use_plotly)
        employees_legend: Optional[str] = "Employés"
        cluster_legend: Optional[str] = "Lieu d’implantation potentiel"
        for cluster_id, cluster in enumerate(cluster_indicators.clusters):
            cluster_color = self.color_picker.pick(cluster_id)
            mapper.add_points(
                [e.coordinates for e in cluster.employees],
                [],
                cluster_color,
                size_cm=0.25,
                opacity=1.0,
                name_in_legend=employees_legend,
            )
            employees_legend = None
            if cluster_id != 0:
                mapper.add_points(
                    [cluster.center],
                    [],
                    cluster_color,
                    size_cm=0.3,
                    opacity=1.0,
                    name_in_legend=cluster_legend,
                    marker_style=PointSymbolStyles.DIAMOND,
                )
                cluster_legend = None
        self.add_sites_to_map(mapper, scenario)
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(height, width, f"clustering_{scenario.nickname}.svg")

    def plot_first_clustering_map(
        self,
        scenario: ScenarioIndicators,
        cluster_number: int = 1,
        cluster_algo: str = "agglo",
        height_cm: float = 10.0,
        width_cm: float = 10.0,
        use_plotly: bool = False,
    ) -> str:
        cluster_indicators = scenario.clustering_indicators
        cluster = cluster_indicators.clusters[cluster_number]
        cluster_color = self.color_picker.pick(cluster_number)
        mapper = self.get_mapper(use_plotly)
        mapper.add_points(
            [e.coordinates for e in cluster.employees],
            [],
            cluster_color,
            size_cm=0.25,
            opacity=1.0,
            name_in_legend="Employés",
        )
        mapper.add_points(
            [cluster.center],
            [],
            cluster_color,
            size_cm=0.30,
            opacity=1.0,
            name_in_legend="Lieu d’implantation potentiel",
            marker_style=PointSymbolStyles.DIAMOND,
        )
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height, width, f"first_clustering_{cluster_number}_{scenario.nickname}.svg"
        )

    def plot_carpool_map(
        self,
        scenario: ScenarioIndicators,
        frame_all: bool = True,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
        title: Optional[str] = None,
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(
            use_plotly, style="mapbox://styles/camshaka/clcourmzh003014pf9he3aiw6"
        )
        thickness = 0.25
        carpool_indicators = scenario.carpool_indicators
        group_color_index = self.color_picker.colorize(
            [g[-1] for g in carpool_indicators.carpool_groups]
        )
        for n_group, group in enumerate(carpool_indicators.carpool_groups):
            if group[-1] in group_color_index:
                group_color = self.color_picker.pick(group_color_index[group[-1]])
            else:
                logging.warning(f"{group[-1].nickname} not in group_color_index")
                group_color = self.color_picker.pick_random()
            driver, passengers = group[:1], group[1:]
            if n_group == 0:
                passenger_legend = "Passagers"
                drivers_legend = "Conducteurs"
            else:
                passenger_legend = ""
                drivers_legend = ""
            mapper.add_points(
                points=[p.coordinates for p in passengers],
                labels=[],
                size_cm=thickness,
                color=group_color,
                opacity=1.0,
                name_in_legend=passenger_legend,
            )
            mapper.add_points(
                points=[d.coordinates for d in driver],
                labels=[],
                size_cm=thickness,
                color=group_color,
                opacity=1.0,
                name_in_legend=drivers_legend,
                marker_style=PointSymbolStyles.DIAMOND,
            )
        self.add_sites_to_map(mapper, scenario)

        if frame_all:
            mapper.set_framing_strategy(FrameAllPoints())
            suffix_name = "all"
        else:
            mapper.set_framing_strategy(
                FrameStandardDeviation(
                    radius=1.0,
                    must_frame=list(scenario.get_sites_geocoordinates().values()),
                )
            )
            suffix_name = "zoom"
        if title is not None:
            mapper.set_title(title)
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height, width, f"carpool_{scenario.nickname}_{suffix_name}.svg"
        )

    def plot_pt_infrastructure_map(
        self,
        scenario: ScenarioIndicators,
        pt_infrastructure_indicators: PtInfrastructureIndicators,
        use_plotly: bool = False,
        height_cm: float = 10,
        width_cm: float = 10,
    ) -> str:
        map_maker = self.get_mapper(use_plotly)
        thicknesses = {
            TransportType.BUS: 0.12,
            TransportType.TRAM: 0.15,
            TransportType.TRAIN: 0.12,
            TransportType.SUBWAY: 0.2,
        }
        ordered_types = [
            TransportType.SUBWAY,
            TransportType.TRAIN,
            TransportType.TRAM,
            TransportType.BUS,
            TransportType.MONORAIL,
            TransportType.AERIALWAY,
            TransportType.FERRY,
        ]
        lines: Dict[TransportType, List[Tuple[GeoCoordinates, ...]]] = defaultdict(list)
        metro_lines: Dict[str, List[Tuple[GeoCoordinates, ...]]] = defaultdict(list)
        metro_colours: Dict[str, str] = {}
        for line in pt_infrastructure_indicators.lines:
            if line.kind == TransportType.SUBWAY:
                color = (
                    self.color_picker.pick(line.kind)
                    if line.colour is None
                    else line.colour
                )
                metro_colours[line.short_name] = color
                metro_lines[line.short_name] += list(line.points)
            else:
                lines[line.kind] += list(line.points)
        if len(ordered_types) != len(list(TransportType)):
            raise ValueError(f"Types to use ({ordered_types}) miss some TransportType")
        written_stops = []
        add_stop_to_legend = True
        for transport_type in ordered_types:
            type_stops = [
                s
                for s in pt_infrastructure_indicators.stops
                if transport_type in s.lines_kinds
                or (len(s.lines_kinds) == 0 and transport_type == TransportType.BUS)
            ]
            thickness = thicknesses.get(transport_type, 0.1)
            stops_to_write = [s for s in type_stops if s.name not in written_stops]
            stop_color = self.color_picker.pick("light_grey")
            if len(stops_to_write) > 0 and add_stop_to_legend:
                legend_name = "Arrêt de transport public"
                add_stop_to_legend = False
            else:
                legend_name = ""
            map_maker.add_points(
                points=[s.coordinates for s in stops_to_write],
                labels=[],
                color=stop_color,
                size_cm=thickness * 1.5,
                opacity=1,
                outline_color=self.color_picker.pick("black"),
                name_in_legend=legend_name,
            )
            map_maker.add_annotations(
                points=[s.coordinates for s in stops_to_write],
                text=[f"{s.name}" for s in stops_to_write],
                font="Lato",
                size_cm=2.0 * thickness,
                opacity=1,
                y_offset=0,
            )
            written_stops += [s.name for s in stops_to_write]
        for metro, metro_line in metro_lines.items():
            map_maker.add_multi_line(
                tuple(metro_line),
                color=metro_colours[metro],
                thickness_cm=thicknesses.get(TransportType.SUBWAY, 0.1),
                opacity=1,
                name_in_legend=f"Métro {metro} ",
            )
        for kind, tc_lines in lines.items():
            map_maker.add_multi_line(
                tuple(tc_lines),
                color=self.color_picker.pick(kind),
                thickness_cm=thicknesses.get(kind, 0.1),
                opacity=1,
                name_in_legend=kind.translate_to_string().capitalize() + " ",
            )
        site = pt_infrastructure_indicators.site
        self.add_sites_list_to_map(map_maker, [site])
        isochrone_bound = 15 * minutes
        self.add_sites_isochrones_to_map(
            map_maker, [site], TransportMode.WALK, [isochrone_bound]
        )
        map_maker.set_legend("")
        map_maker.set_title("Desserte en transports publics du site")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return map_maker.map(
            height,
            width,
            f"pt_infrastructure_{site.nickname}.svg",
        )

    def compute_amenity_icon_size(self, nb_amenities: int) -> float:
        base_size = 0.3
        if nb_amenities < 10:
            return base_size * 2
        else:
            return base_size

    def plot_bicycle_infrastructure_map(
        self,
        scenario: ScenarioIndicators,
        bicycle_infrastructure_indicators: BicycleInfrastructureIndicators,
        use_plotly: bool = False,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
    ) -> str:
        map_maker = self.get_mapper(use_plotly)
        amenities = bicycle_infrastructure_indicators.get_close_amenities_by_time(
            10 * minutes
        )
        icon_size = self.compute_amenity_icon_size(len(amenities))
        icon_size_pt = int(icon_size * 72 / 2.54)
        added_names = set()

        for amenity in amenities:
            for i, amenity_type in enumerate(amenity.types):
                name_in_legend = amenity_type.translate_to_string()
                if name_in_legend not in added_names:
                    map_maker.add_points(
                        [amenity.coordinates],
                        labels=amenity.name,
                        color=self.color_picker.pick("black"),
                        size_cm=icon_size,
                        outline_width_cm=0.0,
                        opacity=1,
                        name_in_legend=name_in_legend,
                        icon_url=amenity_type.to_icon_url(),
                        marker_type=PointSymbolTypes.PICTURE,
                        marker_style=PointSymbolStyles.from_amenity_type(amenity_type),
                        xoffset=icon_size_pt * (i // 2),
                        yoffset=icon_size_pt * (i % 2),
                    )
                    added_names.add(name_in_legend)
        lane_thickness = 0.1
        lane_color = self.color_picker.electric_green
        line_thickness = 0.1
        line_color = self.color_picker.forest_green
        lanes = []
        lines = []
        for n_line, line in enumerate(bicycle_infrastructure_indicators.lines):
            if line.is_lane:
                lanes += list(line.points)
            else:
                lines += list(line.points)
        map_maker.add_multi_line(
            tuple(lanes),
            color=lane_color,
            thickness_cm=lane_thickness,
            opacity=0.8,
            name_in_legend="Bandes cyclables",
        )
        map_maker.add_multi_line(
            tuple(lines),
            color=line_color,
            thickness_cm=line_thickness,
            opacity=0.8,
            name_in_legend="Pistes cyclables",
        )
        site = bicycle_infrastructure_indicators.site
        self.add_sites_list_to_map(map_maker, [site])
        isochrone_bound = 10 * minutes
        self.add_sites_isochrones_to_map(
            map_maker, [site], TransportMode.WALK, [isochrone_bound]
        )
        map_maker.set_framing_strategy(
            FrameAllPointWithCenter(
                center_points=[bicycle_infrastructure_indicators.site.coordinates],
            )
        )
        map_maker.set_legend("")
        map_maker.set_title("Réseau cyclable autour du site")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return map_maker.map(
            height, width, f"bicycle_infrastructure_{site.nickname}.svg"
        )

    def plot_car_infrastructure_map(
        self,
        scenario: ScenarioIndicators,
        car_infrastructure_indicators: CarInfrastructureIndicators,
        use_plotly: bool = False,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
    ) -> str:
        map_maker = self.get_mapper(use_plotly)
        amenities = car_infrastructure_indicators.amenities
        icon_size = self.compute_amenity_icon_size(len(amenities))
        icon_size_pt = int(icon_size * 72 / 2.54)
        added_names = set()

        for amenity in amenities:
            for i, amenity_type in enumerate(amenity.types):
                name_in_legend = amenity_type.translate_to_string()
                if name_in_legend not in added_names:
                    map_maker.add_points(
                        [amenity.coordinates],
                        labels=amenity.name,
                        color=self.color_picker.pick("black"),
                        size_cm=icon_size,
                        outline_width_cm=0.0,
                        opacity=1,
                        name_in_legend=name_in_legend,
                        icon_url=amenity_type.to_icon_url(),
                        marker_type=PointSymbolTypes.PICTURE,
                        marker_style=PointSymbolStyles.from_amenity_type(amenity_type),
                        xoffset=icon_size_pt * (i // 2),
                        yoffset=icon_size_pt * (i % 2),
                    )
                    added_names.add(name_in_legend)
        name_in_legend = "Autoroutes et voies rapides"
        lines = []
        color = self.color_picker.pick(TransportMode.CAR)
        for line in car_infrastructure_indicators.ways:
            lines += list(line.points)
        map_maker.add_multi_line(
            tuple(lines),
            color=color,
            thickness_cm=0.1,
            opacity=0.8,
            name_in_legend=name_in_legend,
        )
        site = car_infrastructure_indicators.site
        self.add_sites_list_to_map(map_maker, [site])
        isochrone_bound = 15 * minutes
        self.add_sites_isochrones_to_map(
            map_maker, [site], TransportMode.WALK, [isochrone_bound]
        )
        map_maker.set_legend("")
        map_maker.set_title("Infrastructure routière à proximité")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return map_maker.map(height, width, f"car_infrastructure_{site.nickname}.svg")

    def plot_pois(
        self,
        scenario: ScenarioIndicators,
        height_cm: float = 10,
        width_cm: float = 30,
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(use_plotly)
        pois = self.indicators.study.get_pois()
        mapper.add_points(
            [p.coordinates for p in pois],
            [],
            self.color_picker.pick("modelity_light"),
            0.3,
            1.0,
            name_in_legend="Localisation des POIs",
        )
        mapper.add_annotations(
            points=[p.coordinates for p in pois],
            text=[p.name for p in pois],
            font="Lato, Material Icons, Noto Emoji, sans-serif",
            size_cm=0.3,
            color=self.color_picker.pick("black"),
            y_offset=5,
        )
        self.add_sites_to_map(mapper, scenario)
        mapper.set_legend(
            "Localisation des sites et POI du scénario, zone accessible en transports en commun"
            " (30 min, 1 h)"
        )
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(height, width, f"points_of_interest_{scenario.nickname}.svg")

    def plot_employees_and_sites_map(
        self,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
        frame_all: bool = False,
        color_by_mode: bool = True,
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(use_plotly)
        scenarios = self.indicators.alternative_scenarios + [
            self.indicators.present_scenario
        ]
        all_sites = []
        for scenario in scenarios:
            self.add_sites_to_map(mapper, scenario)
            all_sites += list(scenario.get_sites_geocoordinates().values())
        if frame_all:
            mapper.set_framing_strategy(FrameAllPoints())
        else:
            mapper.set_framing_strategy(
                FrameStandardDeviation(
                    radius=1.0,
                    must_frame=all_sites,
                )
            )
        if color_by_mode:
            self.add_employees_by_mode_to_map(mapper, self.indicators.present_scenario)
        else:
            self.add_scenario_employees_to_map(mapper, self.indicators.present_scenario)
        if color_by_mode:
            mapper.set_legend("Localisation des employés par mode de transport")
        else:
            mapper.set_legend(
                "Localisation des employés et sites dans tous les scénarios"
            )
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height, width, f"employees_and_sites_map_{frame_all}_{color_by_mode}.svg"
        )

    def plot_employees_and_sites_diff_times_map(
        self,
        scenario: InterScenarioIndicators,
        height_cm: float = 10.0,
        width_cm: float = 10.0,
        frame_all: bool = False,
        use_plotly: bool = False,
    ) -> str:
        mapper = self.get_mapper(use_plotly)
        self.add_employees_by_delta_travel_time_in_buckets_to_map(mapper, scenario)
        self.add_sites_to_map(mapper, scenario.scenario_indicator_to)
        if frame_all:
            framing = "all"
            mapper.set_framing_strategy(FrameAllPoints())
        else:
            framing = "closest"
            mapper.set_framing_strategy(
                FrameStandardDeviation(
                    radius=0.7,
                    must_frame=list(
                        scenario.scenario_indicator_to.get_sites_geocoordinates().values()
                    ),
                )
            )
        mapper.set_legend("")
        if frame_all:
            mapper.set_title("Gain de temps des tous les employés")
        else:
            mapper.set_title("Gain de temps des employés proches du site")
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        return mapper.map(
            height,
            width,
            f"delta_time_map_{scenario.scenario_indicator_to.nickname}_{framing}.svg",
        )

    def plot_employees_and_isochrones_map(
        self,
        scenario: ScenarioIndicators,
        isochrone_mode: TransportMode,
        isochrone_timings: List[int],
        isochrone_not_framed: List[int] = [],
        frame_all: bool = False,
        height_cm: float = 10,
        width_cm: float = 10,
        use_plotly: bool = False,
        pt_infrastructure_indicators: Optional[PtInfrastructureIndicators] = None,
        sites: Optional[List[GeoSite]] = None,
    ) -> str:
        if sites is None:
            sites = list(scenario.get_sites())
        mapper = self.get_mapper(use_plotly)
        self.add_sites_employees_to_map(mapper, scenario, sites)
        self.add_sites_list_to_map(mapper, sites)
        self.add_sites_isochrones_to_map(
            mapper,
            sites,
            isochrone_mode,
            [timing * minutes for timing in isochrone_timings],
        )
        s_timings = "_".join([str(t) for t in isochrone_timings])
        if frame_all:
            framing_qualifier = "all"
            mapper.set_framing_strategy(FrameAllPoints())
        else:
            framing_qualifier = "closest"
            not_framed_indices = [
                len(isochrone_timings) - i - 1
                for i in range(len(isochrone_timings))
                if isochrone_timings[i] in isochrone_not_framed
            ]
            mapper.set_framing_strategy(
                FrameBigIslands(
                    must_frame=list(scenario.get_sites_geocoordinates().values()),
                    not_framed_geometries_indexes=not_framed_indices,
                )
            )
        mapper.set_legend("")
        title_iso = "Isochrones"
        title_emp = ""
        if not frame_all:
            title_emp += " autour du site"
        if len(title_iso) + len(title_emp) > 85:
            sep = "<br>"
        else:
            sep = " "
        title = title_iso + sep + title_emp
        mapper.set_title(title)
        file_name = (
            f"{framing_qualifier}_employees_and_"
            f"isochrones_{isochrone_mode}_{s_timings}_"
            + "_".join(site.nickname for site in sites)
            + ".svg"
        )
        height = int(height_cm * 72 / 2.54)
        width = int(width_cm * 72 / 2.54)
        if pt_infrastructure_indicators is not None:
            if any(
                line.kind == TransportType.TRAIN
                for line in pt_infrastructure_indicators.lines
            ):
                mapper.add_multi_line(
                    tuple(),
                    color="#828282",
                    thickness_cm=0.1,
                    opacity=1,
                    name_in_legend="Réseau ferroviaire",
                )
        return mapper.map(height, width, file_name)

    def add_sites_to_map(
        self,
        mapper: BaseMapMaker,
        scenario: ScenarioIndicators,
        legend_label: Optional[str] = None,
    ) -> None:
        sites = list(scenario.get_sites())
        self.add_sites_list_to_map(mapper, sites, legend_label)

    def add_sites_list_to_map(
        self,
        mapper: BaseMapMaker,
        sites: List[GeoSite],
        legend_label: Optional[str] = None,
    ) -> None:
        if len(sites) > 5:
            nb_in_legend = 1
            legend_label = "Sites"
        else:
            nb_in_legend = len(sites)
        for n_site, site in enumerate(sites):
            coords = site.get_coordinates()
            main_color = self.color_picker.pick("white")
            contour_color = self.illustrator.site_color[site]
            if n_site < nb_in_legend:
                label_in_legend = legend_label or f"Site {site.nickname}"
            else:
                label_in_legend = None
            mapper.add_points(
                [coords],
                [],
                main_color,
                0.7,
                0.9,
                label_in_legend,
                outline=True,
                outline_color=contour_color,
                outline_width_cm=0.1,
                marker_type=PointSymbolTypes.SIMPLE,
                marker_style=PointSymbolStyles.STAR,
                yoffset=-10,
            )

    def add_employees_by_mode_to_map(
        self, mapper: BaseMapMaker, scenario: ScenarioIndicators
    ) -> None:
        for mode, label in self.modes:
            coords = scenario.get_employees_geocoordinates(mode)
            if len(coords) > 0:
                color = self.color_picker.pick(mode)
                mapper.add_points(coords, [], color, 0.25, 1, label)

    def add_employees_by_site_to_map(
        self, mapper: BaseMapMaker, scenario: ScenarioIndicators
    ) -> None:
        sites_dict = scenario.scenario.get_destinations()
        label = (
            "Employés (couleur selon le site)" if len(sites_dict) > 1 else "Employés"
        )
        for site in sorted(sites_dict, key=lambda s: s.id):
            coords = []
            commutes = sites_dict[site]
            for commute in commutes:
                origin, commute_data = commute
                coords.append(origin.get_coordinates())
            color = (
                self.color_picker.pick("white")
                if len(sites_dict) == 1
                else self.illustrator.site_color[site]
            )
            mapper.add_points(coords, [], color, 0.25, 1, label)
            label = ""

    def add_scenario_employees_to_map(
        self, mapper: BaseMapMaker, scenario: ScenarioIndicators
    ) -> None:
        employees = list(scenario.scenario.get_origins())
        self.add_employees_to_map(mapper, employees)

    def add_sites_employees_to_map(
        self, mapper: BaseMapMaker, scenario: ScenarioIndicators, sites: List[GeoSite]
    ) -> None:
        employees_mapping = scenario.scenario.get_origins()
        employees = []
        for employee, destinations in employees_mapping.items():
            (destination,) = destinations
            employee_site, _ = destination
            if employee_site in sites:
                employees.append(employee)
        self.add_employees_to_map(mapper, employees)

    def add_employees_to_map(
        self, mapper: BaseMapMaker, employees: List[GeoEmployee]
    ) -> None:
        coords = [e.get_coordinates() for e in employees]
        mapper.add_points(
            coords,
            [],
            self.color_picker.pick("white"),
            0.25,
            1.0,
            name_in_legend="Employés",
        )

    def add_employees_by_delta_travel_time_in_buckets_to_map(
        self, mapper: BaseMapMaker, scenario: InterScenarioIndicators
    ) -> None:
        employees_delta_times = scenario.get_geo_employees_diff_time()
        employees_delta_seconds = {
            e: int(d / seconds) for e, d in employees_delta_times.items()
        }
        buckets = zip(
            [
                AccessibilitySlices.gain_very_happy,
                AccessibilitySlices.gain_happy,
                AccessibilitySlices.gain_acceptable,
                AccessibilitySlices.stable,
                AccessibilitySlices.loss_acceptable,
                AccessibilitySlices.loss_warning,
                AccessibilitySlices.loss_critical,
            ],
            [
                self.color_picker.pick("#006400"),
                self.color_picker.pick("#32cd32"),
                self.color_picker.pick("#d0ff14"),
                self.color_picker.pick("neutral"),
                self.color_picker.pick("#ff8c00"),
                self.color_picker.pick("#ff4500"),
                self.color_picker.pick("#9b111e"),
            ],
            [
                "gain > 40 min",
                "gain de 20 à 40 min",
                "gain de 5 à 20 min",
                "stable de -5 à 5 min",
                "perte de 5 à 20 min",
                "perte de 20 à 40 min",
                "perte > 40 min",
            ],
        )
        for bucket, color, label in buckets:
            points = [
                e.coordinates
                for e, delta in employees_delta_seconds.items()
                if bucket.contains(delta)
            ]
            if len(points) > 0:
                mapper.add_points(points, [], color, 0.25, 1, label)

    def add_scenario_isochrones_to_map(
        self,
        mapper: BaseMapMaker,
        scenario: ScenarioIndicators,
        mode: TransportMode,
        durations: List[Quantity],
    ) -> None:
        sites = list(scenario.get_sites())
        self.add_sites_isochrones_to_map(mapper, sites, mode, durations)

    def add_sites_isochrones_to_map(
        self,
        mapper: BaseMapMaker,
        sites: List[GeoSite],
        mode: TransportMode,
        durations: List[Quantity],
    ) -> None:
        site_isochrones = self.indicators.get_isochrones()
        isochrones_layers: Dict[Quantity, Dict] = {}
        for duration in durations:
            duration_isochrones = []
            for site in sites:
                try:
                    iso = site_isochrones[site][mode][duration]
                except KeyError:
                    logging.warning(
                        f"No isochrone available for site {site.nickname} by {mode} at {duration}"
                    )
                    continue
                duration_isochrones.append(iso)
            isochrones_layers[duration] = unionize_isochrones(duration_isochrones)
        isochrone_properties = []
        for i, duration in enumerate(durations, start=2):
            opacity = 1 / (2 * i)
            outline_whiteness = (i - 2) / (len(durations) + 1)
            outline_color = self.color_picker.pick_on_whitening_scale(
                "modelity_light", outline_whiteness
            )
            isochrone_properties.append((duration, opacity, outline_color))

        isochrone_properties = sorted(
            isochrone_properties, key=lambda x: x[0], reverse=True
        )

        for duration, opacity, outline_color in isochrone_properties:
            mapper.add_geometry(
                isochrones_layers[duration],
                f"Isochrone {mode.translate_to_string()} {duration}",
                self.color_picker.pick("modelity_light"),
                opacity,
                outline_color=outline_color,
                outline_opacity=1,
                outline_width_cm=0.1,
            )

    def _get_all_inter_scenarios_if_empty(
        self, scenarios: Tuple[InterScenarioIndicators, ...]
    ) -> List[InterScenarioIndicators]:
        return (
            list(scenarios) if len(scenarios) > 0 else self.indicators.inter_scenarios
        )

    def _add_mean_scatter_legend(self, name: str) -> go.scatter:
        return go.Scatter(
            y=[None],
            x=[None],
            showlegend=True,
            mode="lines",
            name=name,
            line={
                "color": self.color_picker.pick("modelity"),
                "dash": "dash",
                "width": 3,
            },
        )

    @staticmethod
    def _get_diff_intervals(scenario: InterScenarioIndicators) -> List[int]:
        return [
            scenario.count_employees_in_diff_interval(AccessibilitySlices.gain),
            scenario.count_employees_in_diff_interval(AccessibilitySlices.stable),
            scenario.count_employees_in_diff_interval(
                AccessibilitySlices.loss_acceptable
            ),
            scenario.count_employees_in_diff_interval(AccessibilitySlices.loss_warning),
            scenario.count_employees_in_diff_interval(
                AccessibilitySlices.loss_critical
            ),
        ]

    def plot_scenario_mobility_account(self, scenario: ScenarioIndicators) -> str:
        return self.cost_grapher.make_mobility_account_graph(
            scenario.costs, scenario.nickname
        )

    def plot_interscenario_mobility_delta_account(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        return self.cost_grapher.make_mobility_delta_account_graph(
            inter_scenario.scenario_indicator_from.costs,
            inter_scenario.scenario_indicator_to.costs,
            inter_scenario.scenario_indicator_to.nickname,
        )

    def plot_mobility_delta_cost_for_company(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        return self.plot_mobility_delta_cost_for_payer(
            inter_scenario, CostPayer.COMPANY, "Coût pour l’entreprise"
        )

    def plot_mobility_delta_cost_for_employee(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        return self.plot_mobility_delta_cost_for_payer(
            inter_scenario, CostPayer.EMPLOYEE, "Coût pour les salariés"
        )

    def plot_mobility_delta_cost_for_society(
        self, inter_scenario: InterScenarioIndicators
    ) -> str:
        return self.plot_mobility_delta_cost_for_payer(
            inter_scenario, CostPayer.SOCIETY, "Coût pour la société civile"
        )

    def plot_mobility_delta_cost_for_payer(
        self, inter_scenario: InterScenarioIndicators, payer: CostPayer, title: str
    ) -> str:
        scenario_from = inter_scenario.scenario_indicator_from
        scenario_to = inter_scenario.scenario_indicator_to
        return self.quantity_delta_grapher.make_quantity_delta_graph(
            scenario_from.costs.filter(MobilityAccountCosts).total_by_payer[payer],
            scenario_to.costs.filter(MobilityAccountCosts).total_by_payer[payer],
            title,
            self.color_picker.pick(payer),
            f"cost_diff_{payer}_{scenario_from.nickname}_{scenario_to.nickname}",
        )

    def plot_ideal_plan_quantity_deltas(self, base_scenario: ScenarioIndicators) -> str:
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        ideal_scenario_summary_indicators = ideal_computer.compute_summary_indicator(
            employees_plan
        )
        include_zfe_card = False
        grapher = QuantityDeltaCardGrapher(self.output_dir, include_zfe_card)
        return grapher.make_quantity_delta_graphs(
            base_scenario, ideal_scenario_summary_indicators
        )

    def plot_ideal_plan_workforce_by_modes(
        self, base_scenario: ScenarioIndicators
    ) -> str:
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        ideal_scenario_summary_indicators = ideal_computer.compute_summary_indicator(
            employees_plan
        )
        grapher = WorkforceByModesGrapher(self.output_dir)
        return grapher.make_workforce_by_modes_graphs(
            base_scenario, ideal_scenario_summary_indicators
        )

    def plot_workforce_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [scenario.nb_employees * adimensional]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            quantities.append(bike_plan.number_of_shifters * adimensional)
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            quantities.append(tc_plan.number_of_shifters * adimensional)
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            quantities.append(remote_plan.nb_remote_workers * adimensional)
        titles.append("Coworking")
        quantities.append(scenario.coworking_indicators.nb_coworkers * adimensional)
        titles.append("Covoiturage")
        quantities.append(
            len(
                scenario.carpool_indicators.drivers
                + scenario.carpool_indicators.passengers
            )
            * adimensional
        )
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Effectif par plan",
            color=self.color_picker.pick("modelity_light"),
            file_id="workforce_by_plan",
        )

    def plot_costs_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [scenario.costs.total]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            quantities.append(bike_plan.costs.total)
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            quantities.append(tc_plan.costs.total)
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            quantities.append(remote_plan.costs.total)
        titles.append("Coworking")
        quantities.append(scenario.coworking_indicators.costs.total)
        titles.append("Covoiturage")
        quantities.append(scenario.carpool_indicators.costs.total)
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Coût total",
            color=self.color_picker.pick("modelity_light"),
            file_id="cost_by_plan",
        )

    def plot_travel_time_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [scenario.average_travel_time_quantity]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            quantities.append(bike_plan.average_travel_time)
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            quantities.append(tc_plan.average_travel_time)
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            quantities.append(remote_plan.average_travel_time)
        titles.append("Coworking")
        quantities.append(get_some(scenario.coworking_indicators.average_travel_time))
        titles.append("Covoiturage")
        quantities.append(scenario.carpool_indicators.average_travel_time)
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Temps de trajet",
            color=self.color_picker.pick("modelity_light"),
            file_id="travel_time_by_plan",
        )

    def plot_emissions_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [scenario.get_carbon_emission()]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            quantities.append(bike_plan.carbon_emission)
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            quantities.append(tc_plan.carbon_emission)
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            quantities.append(remote_plan.carbon_emission)
        titles.append("Coworking")
        quantities.append(scenario.coworking_indicators.emissions)
        titles.append("Covoiturage")
        quantities.append(scenario.carpool_indicators.carbon_emission)
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Émissions de GES",
            color=self.color_picker.pick("modelity_light"),
            file_id="carbon_emission_by_plan",
        )

    def plot_zfe_impact_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [
            scenario.zfe_impact_calendar[
                scenario.max_zfe_impact_year
            ].percent_workers_impacted
            * adimensional
        ]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            max_zfe_impact = max(
                impact.percent_workers_impacted
                for impact in bike_plan.zfe_impact_calendar.values()
            )
            quantities.append(max_zfe_impact * adimensional)
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            max_zfe_impact = max(
                impact.percent_workers_impacted
                for impact in tc_plan.zfe_impact_calendar.values()
            )
            quantities.append(max_zfe_impact * adimensional)
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            max_zfe_impact = max(
                impact.percent_workers_impacted
                for impact in remote_plan.zfe_impact_calendar.values()
            )
            quantities.append(max_zfe_impact * adimensional)
        titles.append("Coworking")
        max_zfe_impact = max(
            impact.percent_workers_impacted
            for impact in scenario.coworking_indicators.zfe_impact_calendar.values()
        )
        quantities.append(max_zfe_impact * adimensional)
        titles.append("Covoiturage")
        max_zfe_impact = max(
            impact.percent_workers_impacted
            for impact in scenario.carpool_indicators.zfe_impact_calendar.values()
        )
        quantities.append(max_zfe_impact * adimensional)
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Impact ZFE",
            color=self.color_picker.pick("modelity_light"),
            file_id="zfe_impact_by_plan",
        )

    def plot_parking_spots_by_plan(self, scenario: ScenarioIndicators) -> str:
        titles = [scenario.nickname]
        quantities = [
            scenario.count_employees_per_mode(TransportMode.CAR) * adimensional
        ]
        for bike_plan in scenario.get_bike_shift_scenarios():
            titles.append("Vélo " + bike_plan.spec.nickname)
            quantities.append(
                bike_plan.employees_count_per_mode[TransportMode.CAR] * adimensional
            )
        for tc_plan in scenario.get_public_transport_shift_scenarios():
            titles.append("Transports " + tc_plan.spec.nickname)
            quantities.append(
                tc_plan.employees_count_per_mode[TransportMode.CAR] * adimensional
            )
        for remote_plan in scenario.get_remote_scenarios():
            titles.append("Télétravail " + remote_plan.nickname)
            quantities.append(
                remote_plan.employees_count_per_mode[TransportMode.CAR] * adimensional
            )
        titles.append("Coworking")
        quantities.append(
            scenario.coworking_indicators.employees_count_per_mode[TransportMode.CAR]
            * adimensional
        )
        titles.append("Covoiturage")
        quantities.append(
            (
                scenario.carpool_indicators.employees_count_per_mode[TransportMode.CAR]
                - len(scenario.carpool_indicators.passengers)
            )
            * adimensional
        )
        return self.multiple_quantity_delta_grapher.make_multiple_quantity_delta_graph(
            scenarios_titles=titles,
            quantities=quantities,
            title="Places de parking",
            color=self.color_picker.pick("modelity_light"),
            file_id="parking_spots_by_plan",
        )

    def plot_executive_summary(self, scenario: ScenarioIndicators) -> str:
        include_zfe = False
        charter = ExecutiveSummaryTableGrapher(self.output_dir, include_zfe=include_zfe)
        executive_summary = compute_executive_summary(scenario)
        return charter.make_executive_summary_table(executive_summary)

    def plot_shift_in_employee_transport_mode(
        self, base_scenario: ScenarioIndicators
    ) -> str:
        categories_labels = {
            TransportMode.ELECTRIC_MOTORCYCLE: "Moto électrique",
            TransportMode.CARPOOLING: "Covoiturage",
            TransportMode.MOTORCYCLE: "Moto",
            TransportMode.CAR: "Autosolisme",
            TransportMode.ELECTRIC_CAR: "Voiture électrique",
            TransportMode.CAR_PUBLIC_TRANSPORT: "Voiture + TC",
            TransportMode.PUBLIC_TRANSPORT: "Transport en commun",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "Vélo + TC",
            TransportMode.FAST_BICYCLE: "Vélo électrique 45km/h",
            TransportMode.ELECTRIC_BICYCLE: "Vélo électrique",
            TransportMode.BICYCLE: "Vélo",
            TransportMode.WALK: "Marche à pied",
        }
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        ideal_scenario_summary_indicators = ideal_computer.compute_summary_indicator(
            employees_plan
        )
        charter: FlowChartGrapher[TransportMode] = FlowChartGrapher(self.output_dir)
        flows = [
            (mode_from, TransportModeLattice.abstract(mode_to))
            for mode_from, mode_to in ideal_scenario_summary_indicators.transport_mode_flows
        ]
        name = f"transport_mode_flow_chart_{base_scenario.nickname}"
        categories_order = list(categories_labels)
        categories_icons = {
            TransportMode.WALK: "walk",
            TransportMode.BICYCLE: "bicycle",
            TransportMode.ELECTRIC_BICYCLE: "electric_bicycle",
            TransportMode.PUBLIC_TRANSPORT: "bus",
            TransportMode.ELECTRIC_CAR: "electric_car",
            TransportMode.CAR: "car",
            TransportMode.MOTORCYCLE: "motorcycle",
            TransportMode.CARPOOLING: "carpooling",
            TransportMode.FAST_BICYCLE: "fast_bicycle",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "bicycle_and_bus",
            TransportMode.CAR_PUBLIC_TRANSPORT: "car_and_bus",
            TransportMode.ELECTRIC_MOTORCYCLE: "electric_motorcycle",
        }
        colors = {mode: self.color_picker.pick(mode) for mode in categories_order}
        titles = (base_scenario.nickname, "Potentiel")
        return charter.make_chart(
            name,
            "Potentiel théorique de changement de mode de transport",
            flows,
            titles,
            colors,
            categories_order,
            categories_labels,
            categories_icons,
        )

    def plot_scenario_global_roi(
        self,
        scenario_name: str,
        costs_before: IndividualCosts,
        costs_after: IndividualCosts,
    ) -> str:
        return self.cost_grapher.make_global_roi_graph(
            costs_before=costs_before,
            costs_after=costs_after,
            scenario_name=scenario_name,
        )

    def plot_remote_scenarios_global_roi_comparison(
        self,
        scenario: ScenarioIndicators,
    ) -> str:
        remote_scenarios = scenario.get_remote_scenarios()
        scenarios_costs = {i.nickname: i.costs for i in remote_scenarios}
        return self.plot_scenarios_global_roi_comparison(
            "télétravail", scenario.costs, scenarios_costs
        )

    def plot_pt_shift_scenarios_global_roi_comparison(
        self,
        scenario: ScenarioIndicators,
    ) -> str:
        pt_shift_scenarios = scenario.get_public_transport_shift_scenarios()
        scenarios_costs = {i.spec.nickname: i.costs for i in pt_shift_scenarios}
        return self.plot_scenarios_global_roi_comparison(
            "transport en commun", scenario.costs, scenarios_costs
        )

    def plot_bike_shift_scenarios_global_roi_comparison(
        self,
        scenario: ScenarioIndicators,
    ) -> str:
        bike_shift_scenarios = scenario.get_bike_shift_scenarios()
        scenarios_costs = {i.spec.nickname: i.costs for i in bike_shift_scenarios}
        return self.plot_scenarios_global_roi_comparison(
            "vélo", scenario.costs, scenarios_costs
        )

    def plot_scenarios_global_roi_comparison(
        self,
        chart_title: str,
        current_costs: IndividualCosts,
        scenarios_costs: Dict[str, IndividualCosts],
    ) -> str:
        delta_costs = {n: c - current_costs for n, c in scenarios_costs.items()}
        bars = self.compute_bars_for_cost_comparison(delta_costs)
        layout = go.Layout(
            template="base+vertical_light+stack_bar",
            title={"text": f"Comparaison coûts/bénéfices des plans {chart_title}"},
        )
        chart = go.Figure(data=bars, layout=layout)
        return self._save_chart(
            chart,
            f"plot_scenarios_global_roi_comparison_{chart_title}.svg",
            width_height_ratio=1,
            size=2,
        )

    def plot_scenarios_mobility_costs_comparison(self, indicators: Indicators) -> str:
        scenarios = [indicators.present_scenario] + indicators.alternative_scenarios
        costs = {s.nickname: s.costs.filter(MobilityAccountCosts) for s in scenarios}
        bars = self.compute_bars_for_cost_comparison(costs)
        layout = go.Layout(
            template="base+vertical_light+stack_bar",
            title={"text": "Comparaison des coûts de la mobilité"},
        )
        chart = go.Figure(data=bars, layout=layout)
        return self._save_chart(
            chart,
            "plot_scenarios_mobility_costs_comparison.svg",
            width_height_ratio=1,
            size=2,
        )

    def compute_bars_for_cost_comparison(
        self, costs: Dict[str, IndividualCosts]
    ) -> List[go.Bar]:
        bars = []
        for payer in [CostPayer.COMPANY, CostPayer.EMPLOYEE, CostPayer.SOCIETY]:
            x, y, text = [], [], []
            for nickname in costs:
                cost_by_payer = costs[nickname].total_by_payer[payer]
                x.append(nickname)
                y.append(float(cost_by_payer / (euros / yearly)))
                text.append(str(cost_by_payer))
            bars.append(
                go.Bar(
                    name=f"Coût pour {payer.translate_to_articled_string()}",
                    x=x,
                    y=y,
                    text=text,
                    marker={"color": self.color_picker.pick(payer)},
                )
            )
        return bars

    def plot_zfe_impact_calendar_as_line_graph(
        self, scenario_name: str, zfe_impact_calendar: ZFEImpactCalendar
    ) -> str:
        chart = go.Figure()
        years = []
        percent_impact = []
        for year, zfe_impact in zfe_impact_calendar.items():
            if year >= 2022:
                years.append(year)
                percent_impact.append(zfe_impact.percent_workers_impacted)
        chart.add_trace(
            go.Scatter(
                name="Part des salariés impactés",
                x=years,
                y=percent_impact,
                text=[
                    formatter_percent_number(p, 100.0) + " %" for p in percent_impact
                ],
                marker_color=self.color_picker.pick("modelity_light"),
            )
        )
        chart.update_layout(
            template="base+vertical_light_range+boulettes",
            showlegend=False,
            title_text=f"Impact ZFE dans le scénario {scenario_name} sur l’ensemble des salariés",
        )
        return self._save_chart(
            chart, f"zfe_impact_calendar_{scenario_name}.svg", width_height_ratio=2.5
        )


def formatter(total: int, separator: str = " ") -> Callable[[Optional[int]], str]:
    def _formatter(x: Optional[int]) -> str:
        if x == 0 or x is None:
            return str(x)
        else:
            return f"{formatter_percent(x, total)}{separator}({x})"

    return _formatter


def formatter_percent(value: Optional[float], total: float, delta: bool = False) -> str:
    return f"{formatter_percent_number(value, total)}%"


def formatter_percent_number(
    value: Optional[float], total: float, delta: bool = False
) -> str:
    sign = ""
    if value == 0 or value is None or total == 0:
        if delta:
            sign = "+"
        return f"{sign}0"
    else:
        percent = float(100 * value / total)
        if abs(percent) < 0.995:
            rounded = round(percent, 2)
            if rounded == 0.0:
                return "<0.01"
            else:
                if delta and rounded > 0:
                    sign = "+"
                return f"{sign}{rounded}"
        elif abs(percent) > 99 and value < total:
            rounded = round(percent, 1)
            if rounded == 100.0:
                return ">99.9"
            else:
                if delta and rounded > 0:
                    sign = "+"
                return f"{sign}{rounded}"
        else:
            rounded = round(percent)
            if delta and rounded > 0:
                sign = "+"
            return f"{sign}{rounded}"


def formatter_percents(total: int) -> Callable[[Sequence[Optional[int]]], List[str]]:
    def _formatter(values: Sequence[Optional[int]]) -> List[str]:
        return [formatter_percent(v, total) for v in values]

    return _formatter


def format_bond(bond: Optional[int]) -> str:
    return f"{round(abs(bond) / 60)}" if bond is not None else "inf"


def format_duration(t: Optional[int], delta: bool = False) -> str:
    if t is None:
        return ""
    sign = "-" if t < 0 else "+" if delta else ""
    t = abs(t)
    if t > 59:
        return f"{sign}{t // 60}h{t % 60:02}"
    else:
        return f"{sign}{t} min"


def format_durations(
    durations: Sequence[Optional[int]], delta: bool = False
) -> List[str]:
    return [format_duration(t, delta) for t in durations]


def trim_zeros(data: List[Tuple[int, str, str]]) -> List[Tuple[int, str, str]]:
    for i in (0, -1):
        while data and data[i][0] == 0:
            data.pop(i)
    return data


def compute_label_ready_isochrones(dict_isochrones: List[Dict]) -> List[Dict]:
    try:
        isochrones = [
            shape(i).buffer(0.0001, cap_style=3, join_style=2) for i in dict_isochrones
        ]
    except ValueError as e:
        logging.error(f"Could not compute isochrones labels. {e}")
        return []
    previous_isochrone = MultiPolygon()
    overlapping_isochrones = []
    for iso in isochrones:
        iso_union = iso.union(previous_isochrone)
        overlapping_isochrones.append(iso_union)
        previous_isochrone = iso_union
    return [mapping(i) for i in overlapping_isochrones]


def unionize_isochrones_lists(isochrones_lists: List[List[Dict]]) -> List[Dict]:
    return [unionize_isochrones(isochrones) for isochrones in isochrones_lists]


def unionize_isochrones(dict_isochrones: List[Dict]) -> Dict:
    isochrones = []
    try:
        isochrones = [
            shape(i).buffer(0.0001, cap_style=3, join_style=2) for i in dict_isochrones
        ]
    except ValueError as e:
        logging.error(f"Could not compute isochrones union. {e}")
    if len(isochrones) == 0:
        iso_union = MultiPolygon()
    else:
        iso_union = isochrones[0]
        for iso in isochrones[1:]:
            iso_union = iso_union.union(iso)
    return mapping(iso_union)

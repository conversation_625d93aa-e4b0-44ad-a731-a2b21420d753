{"data_mtime": 1752154451, "dep_lines": [9, 3, 9, 1, 2, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 5, 5, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.entry_points.run_study", "unittest.mock", "mobility.entry_points", "importlib", "pathlib", "unittest", "pytest", "xdg", "mobility", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.python_api", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "configparser", "contextlib", "mobility.ir", "mobility.ir.study", "os", "re", "types", "typing", "typing_extensions", "mobility.workers", "mobility.workers.territory_controller"], "hash": "f3f92adf09cee7197a8010b80675b35ed7600856", "id": "mobility.tests.test_run_study", "ignore_all": false, "interface_hash": "35f5683a3247db024b6f655063189d6c8268c40c", "mtime": 1747931728, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\test_run_study.py", "plugin_data": null, "size": 13952, "suppressed": [], "version_id": "1.16.1"}
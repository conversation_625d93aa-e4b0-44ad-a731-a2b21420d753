{".class": "MypyFile", "_fullname": "api_abstraction.otp.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "DateTimeInterval": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.date_iterators.DateTimeInterval", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OTPTravelTimeAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI", "name": "OTPTravelTimeAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.otp.api", "mro": ["api_abstraction.otp.api.OTPTravelTimeAPI", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "reporter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "reporter"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "builtins.str", "api_abstraction.api.event_reporter.EventReporter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_mode_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._add_mode_params", "name": "_add_mode_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "mode"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_mode_params of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_call_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._call_requests", "name": "_call_requests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_call_requests of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_plan_response_validity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._check_plan_response_validity", "name": "_check_plan_response_validity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_plan_response_validity of OTPTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_arrival_request_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._compute_arrival_request_time", "name": "_compute_arrival_request_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arrival_time"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_arrival_request_time of OTPTravelTimeAPI", "ret_type": "datetime.time", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_isochrone_with_departure_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary", "arrive_by", "hour", "minute", "second"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._compute_isochrone_with_departure_time", "name": "_compute_isochrone_with_departure_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary", "arrive_by", "hour", "minute", "second"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "UnionType", "items": ["mobility.ir.territory.Territory", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int", "builtins.bool", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_isochrone_with_departure_time of OTPTravelTimeAPI", "ret_type": {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.shape", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_request_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._compute_request_date", "name": "_compute_request_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_request_date of OTPTravelTimeAPI", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_emission_per_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_mode", "reported_otp_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._emission_per_mode", "name": "_emission_per_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_mode", "reported_otp_mode"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.transport.TransportMode", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_emission_per_mode of OTPTravelTimeAPI", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_itineraries_above_max_transfers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._filter_itineraries_above_max_transfers", "name": "_filter_itineraries_above_max_transfers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "max_transfers"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_filter_itineraries_above_max_transfers of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fastest_itinerary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._get_fastest_itinerary", "name": "_get_fastest_itinerary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "plan"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_fastest_itinerary of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_isochrone_fudge_delta_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._get_isochrone_fudge_delta_time", "name": "_get_isochrone_fudge_delta_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_isochrone_fudge_delta_time of OTPTravelTimeAPI", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_itinerary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._get_itinerary", "name": "_get_itinerary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "arrival_time"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_itinerary of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mode_to_bike_speed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._mode_to_bike_speed", "name": "_mode_to_bike_speed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mode_to_bike_speed of OTPTravelTimeAPI", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mode_to_otp_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._mode_to_otp_mode", "name": "_mode_to_otp_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mode_to_otp_mode of OTPTravelTimeAPI", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_smooth_shape_geographically": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coarse_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._smooth_shape_geographically", "name": "_smooth_shape_geographically", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coarse_shape"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.shape", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_smooth_shape_geographically of OTPTravelTimeAPI", "ret_type": {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.shape", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._time", "name": "_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time of OTPTravelTimeAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time_around": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "delta_deg", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._time_around", "name": "_time_around", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "delta_deg", "max_transfers"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time_around of OTPTravelTimeAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI._time_at", "name": "_time_at", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time_at of OTPTravelTimeAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.compute_isochrone", "name": "compute_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI", "mobility.ir.territory.Territory", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isochrone of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_router_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.default_router_url", "name": "default_router_url", "setter_type": null, "type": "builtins.str"}}, "get_request_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.get_request_date", "name": "get_request_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.otp.api.OTPTravelTimeAPI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_request_date of OTPTravelTimeAPI", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isochrone_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.isochrone_url", "name": "isochrone_url", "setter_type": null, "type": "builtins.str"}}, "jitter_coordinates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["origin", "destination", "delta_deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.jitter_coordinates", "name": "jitter_coordinates", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["origin", "destination", "delta_deg"], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jitter_coordinates of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.jitter_coordinates", "name": "jitter_coordinates", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["origin", "destination", "delta_deg"], "arg_types": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "jitter_coordinates of OTPTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "max_call_per_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.max_call_per_period", "name": "max_call_per_period", "setter_type": null, "type": "builtins.int"}}, "max_transfers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.max_transfers", "name": "max_transfers", "setter_type": null, "type": {".class": "NoneType"}}}, "period_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.period_in_seconds", "name": "period_in_seconds", "setter_type": null, "type": "builtins.float"}}, "request_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.request_date", "name": "request_date", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.retries", "name": "retries", "setter_type": null, "type": "builtins.int"}}, "retry_delay_in_seconds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.retry_delay_in_seconds", "name": "retry_delay_in_seconds", "setter_type": null, "type": "builtins.float"}}, "root_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.root_url", "name": "root_url", "setter_type": null, "type": "builtins.str"}}, "trip_planning_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.trip_planning_url", "name": "trip_planning_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.otp.api.OTPTravelTimeAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.otp.api.OTPTravelTimeAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "WALK_SPEED_METERS_PER_SECOND": {".class": "SymbolTableNode", "cross_ref": "mobility.constants.WALK_SPEED_METERS_PER_SECOND", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.otp.api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.api", "name": "api", "setter_type": null, "type": "api_abstraction.otp.api.OTPTravelTimeAPI"}}, "arrival_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.arrival_time", "name": "arrival_time", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "closest_tuesday_8_30_in_interval": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.hypotheses.closest_tuesday_8_30_in_interval", "kind": "Gdef"}, "compute_distance": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.distance_computer.compute_distance", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "destination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.destination", "name": "destination", "setter_type": null, "type": "mobility.ir.geo_study.GeoCoordinates"}}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.otp.api.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "geopandas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.otp.api.geopandas", "name": "geopandas", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.geopandas", "source_any": null, "type_of_any": 3}}}, "journey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.journey", "name": "journey", "setter_type": null, "type": "api_abstraction.api.travel_time_api.JourneyAttribute"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "meters": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.meters", "kind": "Gdef"}, "mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.mode", "name": "mode", "setter_type": null, "type": "mobility.ir.transport.TransportMode"}}, "origin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "api_abstraction.otp.api.origin", "name": "origin", "setter_type": null, "type": "mobility.ir.geo_study.GeoCoordinates"}}, "product": {".class": "SymbolTableNode", "cross_ref": "itertools.product", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "shape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.otp.api.shape", "name": "shape", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.shape", "source_any": null, "type_of_any": 3}}}, "shapely": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.otp.api.shapely", "name": "shapely", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.otp.api.shapely", "source_any": null, "type_of_any": 3}}}}, "path": "api_abstraction\\otp\\api.py"}
import json
from collections import defaultdict, namedtuple
from typing import Dict, List, Tuple

import geopandas
import shapely

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import ApiFail, JourneyAttribute, TravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Optional, Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import kilometers
from mobility.workers.distance_computer import compute_distance


class FailerTravelTimer(TravelTimeApi):
    max_call_per_period = 100000
    period_in_seconds = 1.0
    retry_delay_in_seconds = 0.0001
    is_trivial = True

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        raise ApiFail("Everything is fine")

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        raise ApiFail("Everything is fine")

    def compute_detours(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        raise ApiFail("Everything is fine")


class TrivialTravelTimer(TravelTimeApi):
    max_call_per_period = 100000
    period_in_seconds = 1.0
    retry_delay_in_seconds = 0.0001
    is_trivial = True

    def __init__(self, token: str, reporter: EventReporter) -> None:
        self.attributes = {
            TransportMode.WALK: JourneyAttribute(152, 152, 152),
            TransportMode.PUBLIC_TRANSPORT: JourneyAttribute(34, 34, 34),
            TransportMode.CAR: JourneyAttribute(23, 23, 23),
            TransportMode.BICYCLE: JourneyAttribute(32, 32, 32),
            TransportMode.CARPOOLING: JourneyAttribute(23, 23, 23),
            TransportMode.ELECTRIC_BICYCLE: JourneyAttribute(32, 32, 32),
            TransportMode.ELECTRIC_CAR: JourneyAttribute(23, 23, 23),
            TransportMode.MOTORCYCLE: JourneyAttribute(23, 23, 23),
            TransportMode.AIRPLANE: JourneyAttribute(120 * 60, 500000, 115000),
            TransportMode.ELECTRIC_MOTORCYCLE: JourneyAttribute(90, 90, 90),
            TransportMode.FAST_BICYCLE: JourneyAttribute(91, 91, 91),
            TransportMode.CAR_PUBLIC_TRANSPORT: JourneyAttribute(92, 92, 92),
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: JourneyAttribute(93, 93, 93),
        }
        super().__init__(token, reporter)

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        if mode in self.attributes:
            hour, _ = arrival_time
            delta = hour - 8
            attr = self.attributes[mode]
            return JourneyAttribute(
                duration=attr.duration + delta,
                distance=attr.distance,
                emission=attr.emission,
            )
        raise ApiFail(f"Mode {mode} not implemented")

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        point = shapely.geometry.point.Point(
            destination.longitude, destination.latitude
        )
        isochrone = point.buffer(boundary * 0.001 / 600)
        iso_dict = json.loads(geopandas.GeoSeries(isochrone).to_json())["features"][0][
            "geometry"
        ]
        return iso_dict

    def compute_detour_costs(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, float]]:
        detour_costs: Dict[int, Dict[int, float]] = defaultdict(dict)
        for idx_origin, origin in enumerate(origins):
            for idx_by_point, by_point in enumerate(origins):
                if by_point != origin:
                    detour_costs[idx_origin][idx_by_point] = 1.0
        return detour_costs

    def compute_detours(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        detour_costs: Dict[int, Dict[int, JourneyAttribute]] = defaultdict(dict)
        for idx_origin, origin in enumerate(origins):
            for idx_by_point, by_point in enumerate(origins):
                detour_costs[idx_origin][idx_by_point] = self._time(
                    origin, by_point, TransportMode.CAR, (8, 30)
                )
            detour_costs[idx_origin][-1] = self._time(
                origin, destination, TransportMode.CAR, (8, 30)
            )
        return detour_costs


class DistanceTravelTimer(TravelTimeApi):
    max_call_per_period = 100000
    period_in_seconds = 1.0
    retry_delay_in_seconds = 0.0001
    is_trivial = True

    def __init__(self, token: str, reporter: EventReporter):
        mode_attributes = namedtuple(
            "mode_attributes", ["speed", "delta", "emission_per_km"]
        )
        self.modes_attributes = {
            TransportMode.WALK: mode_attributes(5, 1 * 60, 0),
            TransportMode.CAR: mode_attributes(50, 10 * 60, 193.2),
            TransportMode.BICYCLE: mode_attributes(13, 3 * 60, 0),
            TransportMode.PUBLIC_TRANSPORT: mode_attributes(60, 15 * 60, 28.9),
            TransportMode.CARPOOLING: mode_attributes(50, 15 * 60, 193.2 / 2),
            TransportMode.ELECTRIC_BICYCLE: mode_attributes(20, 3 * 60, 0),
            TransportMode.ELECTRIC_CAR: mode_attributes(50, 8 * 60, 193.2 / 4),
            TransportMode.MOTORCYCLE: mode_attributes(60, 5 * 60, 193.2),
            TransportMode.AIRPLANE: mode_attributes(600, 2 * 3600, 230),
            TransportMode.ELECTRIC_MOTORCYCLE: mode_attributes(50, 5 * 60, 0),
            TransportMode.FAST_BICYCLE: mode_attributes(45, 4 * 60, 0),
            TransportMode.CAR_PUBLIC_TRANSPORT: mode_attributes(
                100, 25 * 60, 0.2 * 193.2 + 0.8 * 28.9
            ),
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: mode_attributes(
                90, 20 * 60, 0.8 * 28.9
            ),
        }
        super().__init__(token, reporter)

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        od_distance = float(compute_distance(origin, destination) / kilometers)
        if mode in self.modes_attributes:
            modal = self.modes_attributes[mode]
            duration = int(3600 * od_distance / modal.speed) + modal.delta
            distance = int(od_distance * 1000)
            emission = int(od_distance * modal.emission_per_km)
            return JourneyAttribute(
                duration=duration,
                distance=distance,
                emission=emission,
            )
        else:
            raise ApiFail(f"Mode {mode} not implemented")

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        point = shapely.geometry.point.Point(
            destination.longitude, destination.latitude
        )
        if transport_mode in self.modes_attributes:
            deg_per_kilometer = 0.0025
            speed_km_h = self.modes_attributes[transport_mode].speed
            distance_km = (boundary / 3600.0) * speed_km_h
            isochrone = point.buffer(deg_per_kilometer * distance_km)
            iso_dict = json.loads(geopandas.GeoSeries(isochrone).to_json())["features"][
                0
            ]["geometry"]
            return iso_dict
        else:
            raise ApiFail(f"Mode {transport_mode} not implemented")

    def compute_detours(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        modal = self.modes_attributes[TransportMode.CAR]
        detour_costs: Dict[int, Dict[int, JourneyAttribute]] = defaultdict(dict)
        for idx_origin, origin in enumerate(origins):
            for idx_by_point, by_point in enumerate(origins):
                od_distance = float(compute_distance(origin, by_point) / kilometers)
                duration = int(3600 * od_distance / modal.speed)
                distance = int(od_distance * 1000)
                emission = int(od_distance * modal.emission_per_km)
                detour_costs[idx_origin][idx_by_point] = JourneyAttribute(
                    duration=duration,
                    distance=distance,
                    emission=emission,
                )
            od_distance = float(compute_distance(origin, destination) / kilometers)
            duration = int(3600 * od_distance / modal.speed)
            distance = int(od_distance * 1000)
            emission = int(od_distance * modal.emission_per_km)
            detour_costs[idx_origin][-1] = JourneyAttribute(
                duration=duration,
                distance=distance,
                emission=emission,
            )
        return detour_costs

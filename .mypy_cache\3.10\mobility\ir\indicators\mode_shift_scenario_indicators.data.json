{".class": "MypyFile", "_fullname": "mobility.ir.indicators.mode_shift_scenario_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommuterProfile": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.CommuterProfile", "kind": "Gdef"}, "CurrentModeNotCompatible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible", "name": "CurrentModeNotCompatible", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CurrentModeNotCompatible", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CurrentModeNotCompatible", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.CurrentModeNotCompatible", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "IndividualCosts": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.cost.IndividualCosts", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MaximumDeltaTimeExceeded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded", "name": "MaximumDeltaTimeExceeded", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "delta_time", "type": "mobility.quantity.Quantity"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delta_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delta_time"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded", "mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MaximumDeltaTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "delta_time"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["delta_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["delta_time"], "arg_types": ["mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MaximumDeltaTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["delta_time"], "arg_types": ["mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MaximumDeltaTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "delta_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.delta_time", "name": "delta_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumDeltaTimeExceeded", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaximumResultingTimeExceeded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded", "name": "MaximumResultingTimeExceeded", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "resulting_time", "type": "mobility.quantity.Quantity"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resulting_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resulting_time"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded", "mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MaximumResultingTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "resulting_time"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["resulting_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["resulting_time"], "arg_types": ["mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MaximumResultingTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["resulting_time"], "arg_types": ["mobility.quantity.Quantity"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MaximumResultingTimeExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "resulting_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.resulting_time", "name": "resulting_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.MaximumResultingTimeExceeded", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModeShiftScenarioIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators", "name": "ModeShiftScenarioIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "spec", "type": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "average_travel_time", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "mean_time_for_shifters", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "mean_time_for_shifters_before_shift", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "carbon_emission", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "diff_emissions_percent", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "shifters_original_emissions", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "shifters_emissions_after_shift", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "annual_distance_for_shifters", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "annual_distance_by_car_for_car_shifters", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "delta_time_per_shifter", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "no_shift_reason", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 60, "name": "shifter_employees", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "non_shifter_employees", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "nb_car_parking_saved", "type": "mobility.quantity.Quantity"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "number_of_shifters", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "employees_count_per_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "employees_count_per_profile", "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "shifters_count_per_current_mode", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 67, "name": "shifters_travel_time", "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "costs", "type": "mobility.ir.cost.IndividualCosts"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "zfe_impact_calendar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "spec", "average_travel_time", "mean_time_for_shifters", "mean_time_for_shifters_before_shift", "carbon_emission", "diff_emissions_percent", "shifters_original_emissions", "shifters_emissions_after_shift", "annual_distance_for_shifters", "annual_distance_by_car_for_car_shifters", "delta_time_per_shifter", "no_shift_reason", "shifter_employees", "non_shifter_employees", "nb_car_parking_saved", "number_of_shifters", "employees_count_per_mode", "employees_count_per_profile", "shifters_count_per_current_mode", "shifters_travel_time", "costs", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "spec", "average_travel_time", "mean_time_for_shifters", "mean_time_for_shifters_before_shift", "carbon_emission", "diff_emissions_percent", "shifters_original_emissions", "shifters_emissions_after_shift", "annual_distance_for_shifters", "annual_distance_by_car_for_car_shifters", "delta_time_per_shifter", "no_shift_reason", "shifter_employees", "non_shifter_employees", "nb_car_parking_saved", "number_of_shifters", "employees_count_per_mode", "employees_count_per_profile", "shifters_count_per_current_mode", "shifters_travel_time", "costs", "zfe_impact_calendar"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators", "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.quantity.Quantity", "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModeShiftScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean_time_for_shifters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean_time_for_shifters_before_shift"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbon_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "diff_emissions_percent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shifters_original_emissions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shifters_emissions_after_shift"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "annual_distance_for_shifters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "annual_distance_by_car_for_car_shifters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "delta_time_per_shifter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "no_shift_reason"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shifter_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "non_shifter_employees"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nb_car_parking_saved"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "number_of_shifters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employees_count_per_profile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shifters_count_per_current_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shifters_travel_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "costs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zfe_impact_calendar"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["spec", "average_travel_time", "mean_time_for_shifters", "mean_time_for_shifters_before_shift", "carbon_emission", "diff_emissions_percent", "shifters_original_emissions", "shifters_emissions_after_shift", "annual_distance_for_shifters", "annual_distance_by_car_for_car_shifters", "delta_time_per_shifter", "no_shift_reason", "shifter_employees", "non_shifter_employees", "nb_car_parking_saved", "number_of_shifters", "employees_count_per_mode", "employees_count_per_profile", "shifters_count_per_current_mode", "shifters_travel_time", "costs", "zfe_impact_calendar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["spec", "average_travel_time", "mean_time_for_shifters", "mean_time_for_shifters_before_shift", "carbon_emission", "diff_emissions_percent", "shifters_original_emissions", "shifters_emissions_after_shift", "annual_distance_for_shifters", "annual_distance_by_car_for_car_shifters", "delta_time_per_shifter", "no_shift_reason", "shifter_employees", "non_shifter_employees", "nb_car_parking_saved", "number_of_shifters", "employees_count_per_mode", "employees_count_per_profile", "shifters_count_per_current_mode", "shifters_travel_time", "costs", "zfe_impact_calendar"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.quantity.Quantity", "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModeShiftScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["spec", "average_travel_time", "mean_time_for_shifters", "mean_time_for_shifters_before_shift", "carbon_emission", "diff_emissions_percent", "shifters_original_emissions", "shifters_emissions_after_shift", "annual_distance_for_shifters", "annual_distance_by_car_for_car_shifters", "delta_time_per_shifter", "no_shift_reason", "shifter_employees", "non_shifter_employees", "nb_car_parking_saved", "number_of_shifters", "employees_count_per_mode", "employees_count_per_profile", "shifters_count_per_current_mode", "shifters_travel_time", "costs", "zfe_impact_calendar"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, "mobility.quantity.Quantity", "mobility.quantity.Quantity", {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.quantity.Quantity", "builtins.int", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "mobility.ir.cost.IndividualCosts", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModeShiftScenarioIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "annual_distance_by_car_for_car_shifters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.annual_distance_by_car_for_car_shifters", "name": "annual_distance_by_car_for_car_shifters", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "annual_distance_for_shifters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.annual_distance_for_shifters", "name": "annual_distance_for_shifters", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "average_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.average_travel_time", "name": "average_travel_time", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "carbon_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.carbon_emission", "name": "carbon_emission", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "costs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.costs", "name": "costs", "setter_type": null, "type": "mobility.ir.cost.IndividualCosts"}}, "delta_time_per_shifter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.delta_time_per_shifter", "name": "delta_time_per_shifter", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "diff_emissions_percent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.diff_emissions_percent", "name": "diff_emissions_percent", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "employees_count_per_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.employees_count_per_mode", "name": "employees_count_per_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "employees_count_per_profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.employees_count_per_profile", "name": "employees_count_per_profile", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.CommuterProfile", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "mean_time_for_shifters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.mean_time_for_shifters", "name": "mean_time_for_shifters", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mean_time_for_shifters_before_shift": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.mean_time_for_shifters_before_shift", "name": "mean_time_for_shifters_before_shift", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "nb_car_parking_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.nb_car_parking_saved", "name": "nb_car_parking_saved", "setter_type": null, "type": "mobility.quantity.Quantity"}}, "no_shift_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.no_shift_reason", "name": "no_shift_reason", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "non_shifter_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.non_shifter_employees", "name": "non_shifter_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "number_of_shifters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.number_of_shifters", "name": "number_of_shifters", "setter_type": null, "type": "builtins.int"}}, "shifter_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.shifter_employees", "name": "shifter_employees", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "shifters_count_per_current_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.shifters_count_per_current_mode", "name": "shifters_count_per_current_mode", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", "builtins.int"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "shifters_emissions_after_shift": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.shifters_emissions_after_shift", "name": "shifters_emissions_after_shift", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "shifters_original_emissions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.shifters_original_emissions", "name": "shifters_original_emissions", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "shifters_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.shifters_travel_time", "name": "shifters_travel_time", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.employee.GeoEmployee", "mobility.quantity.Quantity"], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}}}, "spec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.spec", "name": "spec", "setter_type": null, "type": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification"}}, "zfe_impact_calendar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.zfe_impact_calendar", "name": "zfe_impact_calendar", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftScenarioIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModeShiftSpecification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "name": "ModeShiftSpecification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "nickname", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 15, "name": "modes_to_shift_from", "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 16, "name": "mode_to_shift_to", "type": "mobility.ir.transport.TransportMode"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 17, "name": "max_resulting_time", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "max_delta_time", "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "modes_to_shift_from", "mode_to_shift_to", "max_resulting_time", "max_delta_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "nickname", "modes_to_shift_from", "mode_to_shift_to", "max_resulting_time", "max_delta_time"], "arg_types": ["mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "builtins.str", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModeShiftSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nickname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "modes_to_shift_from"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode_to_shift_to"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_resulting_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_delta_time"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["nickname", "modes_to_shift_from", "mode_to_shift_to", "max_resulting_time", "max_delta_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["nickname", "modes_to_shift_from", "mode_to_shift_to", "max_resulting_time", "max_delta_time"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModeShiftSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["nickname", "modes_to_shift_from", "mode_to_shift_to", "max_resulting_time", "max_delta_time"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModeShiftSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "max_delta_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.max_delta_time", "name": "max_delta_time", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_resulting_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.max_resulting_time", "name": "max_resulting_time", "setter_type": null, "type": {".class": "UnionType", "items": ["mobility.quantity.Quantity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mode_to_shift_to": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.mode_to_shift_to", "name": "mode_to_shift_to", "setter_type": null, "type": "mobility.ir.transport.TransportMode"}}, "modes_to_shift_from": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.modes_to_shift_from", "name": "modes_to_shift_from", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "nickname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.nickname", "name": "nickname", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.ModeShiftSpecification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoModeShiftReason": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "name": "NoModeShiftReason", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NoModeShiftReason", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NoModeShiftReason", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoShiftTimeAvailable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable", "name": "NoShiftTimeAvailable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [], "frozen": false}, "dataclass_tag": {}}, "module_name": "mobility.ir.indicators.mode_shift_scenario_indicators", "mro": ["mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable", "mobility.ir.indicators.mode_shift_scenario_indicators.NoModeShiftReason", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NoShiftTimeAvailable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of NoShiftTimeAvailable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.mode_shift_scenario_indicators.NoShiftTimeAvailable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Quantity": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.Quantity", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "ZFEImpactCalendar": {".class": "SymbolTableNode", "cross_ref": "mobility.workers.zfe_impact_computer.ZFEImpactCalendar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.mode_shift_scenario_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}}, "path": "mobility\\ir\\indicators\\mode_shift_scenario_indicators.py"}
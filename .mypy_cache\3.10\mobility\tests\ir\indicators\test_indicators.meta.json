{"data_mtime": 1752154448, "dep_lines": [8, 9, 10, 11, 6, 7, 12, 13, 14, 15, 5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.indicators.poi_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.indicators.study_indicators", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.ir.transport", "mobility.funky", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.python_api", "_typeshed", "abc", "contextlib", "enum", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.poi", "mobility.ir.study", "re", "types", "typing_extensions"], "hash": "2d65010d5696976a4e5630a64f3e136423359c4f", "id": "mobility.tests.ir.indicators.test_indicators", "ignore_all": false, "interface_hash": "8cb3c0e63a286491524ee83e697b8507c15d73f6", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\ir\\indicators\\test_indicators.py", "plugin_data": null, "size": 98011, "suppressed": [], "version_id": "1.16.1"}
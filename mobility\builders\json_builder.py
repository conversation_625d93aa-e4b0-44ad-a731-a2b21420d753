import json
import os
from enum import IntEnum
from typing import Any, Dict, FrozenSet, List, Optional, Type, Union

from jsonschema import RefResolver, SchemaError, ValidationError, validate

from mobility.builders.crit_air_computer import CritAirComputer
from mobility.builders.exceptions import WrongJsonFormatError
from mobility.constants import JSON_SCHEMA_DIR, JSON_VERSION
from mobility.funky import ImmutableDict
from mobility.ir.commute_data import ModalCommuteData, TimedCommuteData
from mobility.ir.employee import FailedGeoEmployee, GeoEmployee
from mobility.ir.infrastructure import (
    BicycleAmenity,
    BicycleLine,
    CarAmenity,
    CarWay,
    PublicTransportLine,
    PublicTransportStop,
)
from mobility.ir.poi import PointOfInterest
from mobility.ir.scenario_data import ScenarioData
from mobility.ir.site import CoworkingSite, FailedGeoSite, GeoSite
from mobility.ir.study import (
    BicycleAmenitiesScenario,
    CarAmenitiesScenario,
    ConsolidatedStudy,
    ModalStudy,
    PublicTransportStationsScenario,
    SiteIsochrone,
    StudyData,
    TimedBicycleCommute,
    TimedCarCommute,
    TimedInfrastructure,
    TimedStudy,
    TimedTransportStopCommute,
)
from mobility.ir.study_types import Commute, Scenario, Scenarios
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import seconds
from mobility.workers.compute_poi_scenario import compute_poi_scenario
from mobility.workers.territory_computer import find_closest_city_center


class JSONVersion(IntEnum):
    V1 = 1
    V2 = 2
    V3 = 3
    V4 = 4
    V5 = 5
    V6 = 6
    V7 = 7
    V8 = 8
    V9 = 9
    V10 = 10
    V11 = 11
    V12 = 12
    V13 = 13
    V14 = 14
    V15 = 15
    V16 = 16
    V17 = 17
    V18 = 18

    @classmethod
    def _support_old_version(cls) -> Dict[str, "JSONVersion"]:
        return {
            "1_0": cls.V1,
            "2_0": cls.V2,
            "3_0": cls.V3,
            "4_0": cls.V4,
            "5_0": cls.V5,
            "6_0": cls.V6,
            "7_0": cls.V7,
            "8_0": cls.V8,
            "9_0": cls.V9,
            "10_0": cls.V10,
            "10_1": cls.V11,
            "10_2": cls.V12,
            "11_0": cls.V13,
            "12_0": cls.V14,
            "13_0": cls.V15,
            "14_0": cls.V16,
        }

    @classmethod
    def from_string(cls, version_str: str) -> "JSONVersion":
        """
        JSON schema versions follow the format vMAJOR only like v1, v2, etc.
        The old version format, MAJOR_MINOR like 1_0, 2_0, etc. is still supported
        up to version 14_0.
        """
        if version_str.startswith("v"):
            try:
                version_number = int(version_str[1:])
                version_mapping = {v.value: v for v in cls}
                if version_number in version_mapping:
                    return version_mapping[version_number]
                raise ValueError(f"Invalid version number: {version_number}")
            except ValueError:
                raise ValueError(f"Invalid version string: {version_str}")
        old_version_mapping = cls._support_old_version()
        if version_str in old_version_mapping:
            return old_version_mapping[version_str]
        raise ValueError(f"Invalid version string format: {version_str}")

    def to_string(self) -> str:
        return f"v{self.value}"


class StudyJsonBuilder:
    def __init__(self, file_name: str) -> None:
        self.file_name = file_name
        self.json_study = self._load_json(file_name)
        self.version = self._extract_json_version()
        self._normalize_old_json_study_version_format()

    def _load_json(self, file_name: str) -> ImmutableDict:
        def cast_immutable_dict(obj: Any) -> Any:
            if isinstance(obj, dict):
                return ImmutableDict(obj)
            return obj

        with open(file_name, "r", encoding="utf-8") as f:
            return json.load(f, object_hook=cast_immutable_dict)

    def _normalize_old_json_study_version_format(self) -> None:
        """
        Normalize old version format (e.g., '14_0') to new format (e.g., 'v16').
        """
        current_version_str = self.json_study["version"]
        if not current_version_str.startswith("v"):
            normalized_version_str = self.version.to_string()
            updated_json = dict(self.json_study)
            updated_json["version"] = normalized_version_str
            self.json_study = ImmutableDict(updated_json)

    def _extract_json_version(self) -> JSONVersion:
        try:
            version = JSONVersion.from_string(self.json_study["version"])
        except ValueError:
            raise WrongJsonFormatError("Invalid version in json file.")
        except KeyError:
            raise WrongJsonFormatError("Version was not found in json file.")
        return version

    def build_consolidated_study(self) -> ConsolidatedStudy:
        promoters: Dict[JSONVersion, Type[JsonStudyPromoter]] = {
            JSONVersion.V1: ConvertJsonV1,
            JSONVersion.V2: ConvertJsonV2,
            JSONVersion.V3: ConvertJsonV3,
            JSONVersion.V4: ConvertJsonV4,
            JSONVersion.V5: ConvertJsonV5,
            JSONVersion.V6: ConvertJsonV6,
            JSONVersion.V7: ConvertJsonV7,
            JSONVersion.V8: ConvertJsonV8,
            JSONVersion.V9: ConvertJsonV9,
            JSONVersion.V10: ConvertJsonV10,
            JSONVersion.V11: ConvertJsonV11,
            JSONVersion.V12: ConvertJsonV12,
            JSONVersion.V13: ConvertJsonV13,
            JSONVersion.V14: ConvertJsonV14,
            JSONVersion.V15: ConvertJsonV15,
            JSONVersion.V16: ConvertJsonV16,
            JSONVersion.V17: ConvertJsonV17,
        }
        while self.version in promoters:
            promoter = promoters[self.version]
            self.json_study = promoter().convert(self.json_study)
            self.version = self._extract_json_version()
        if self.version != JSONVersion.from_string(JSON_VERSION):
            raise WrongJsonFormatError(
                f"Json version {self.version} was not upgraded to latest version {JSON_VERSION}."
            )
        validate_latest_json_schema(self.json_study)
        return ConsolidatedJsonBuilder(self.json_study).build()

    def build_timed_study(self) -> TimedStudy:
        study = self.build_consolidated_study()
        if len(list(study.poi_scenarios)) == 0:
            study = self.add_poi_scenarios(study)
        return ConsolidatedStudyConverter(study).convert_to_timed_study()

    def build_modal_study(self) -> ModalStudy:
        study = self.build_consolidated_study()
        if len(list(study.poi_scenarios)) == 0:
            study = self.add_poi_scenarios(study)
        return ConsolidatedStudyConverter(study).convert_to_modal_study()

    @staticmethod
    def add_poi_scenarios(study: ConsolidatedStudy) -> ConsolidatedStudy:
        scenario_without_data = compute_poi_scenario(study.scenarios, study.get_pois())
        commute_data = TimedCommuteData(
            duration=ImmutableDict(),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
            alternative_arrival_time=ImmutableDict(),
        )
        poi_scenarios = scenario_without_data.update_commutes(lambda x: commute_data)
        return ConsolidatedStudy(
            scenarios=study.scenarios,
            poi_scenarios=poi_scenarios,
            infrastructure=study.infrastructure,
            data=study.data,
            time_failed=study.time_failed,
            geocode_failed_sites=study.geocode_failed_sites,
            geocode_failed_employees=study.geocode_failed_employees,
            geocode_failed_both=study.geocode_failed_both,
            isochrones=study.isochrones,
            coworking_scenario=study.coworking_scenario,
            coworking_failed_scenario=study.coworking_failed_scenario,
        )


def get_schema(uri: str) -> Optional[Dict]:
    prefix = "file:///schemas/"
    if isinstance(uri, str) and uri.startswith(prefix):
        schema_name = uri[len(prefix) :]
        schema_dir = os.path.join(JSON_SCHEMA_DIR, schema_name)
        with open(schema_dir, "r") as f:
            schema = json.load(f)
        return schema
    return None


def validate_latest_json_schema(
    json_study: ImmutableDict, study_schema_name: str = "consolidated_study.json"
) -> None:
    json_schema_path = os.path.join(JSON_SCHEMA_DIR, "study", study_schema_name)
    try:
        with open(json_schema_path, "r") as f:
            schema = json.load(f)
            ref_resolver = RefResolver.from_schema(
                schema, handlers={"file": get_schema}
            )
            try:
                validate(json_study, schema, resolver=ref_resolver)
            except (ValidationError, SchemaError) as e:
                raise WrongJsonFormatError(e)
    except FileNotFoundError as e:
        raise FileNotFoundError(
            f"Json Schema file {json_schema_path} not available. Verify the version number of the Json. \n{e}"
        )


class JsonPromoter:
    def convert(self, json_dict: ImmutableDict) -> ImmutableDict:
        return self._iterate_on_node([], json_dict)

    def _iterate_on_node(
        self, parents: List[str], sub_dict: Union[ImmutableDict, dict]
    ) -> Any:
        new_dict = {}
        for key, element in sub_dict.items():
            if isinstance(element, ImmutableDict) or isinstance(element, dict):
                new_dict[key] = self._iterate_on_node(parents + [key], element)
            elif isinstance(element, list):
                new_dict[key] = [
                    (
                        self._iterate_on_node(parents + [key], list_item)
                        if isinstance(list_item, ImmutableDict)
                        or isinstance(list_item, dict)
                        else self.convert_element(parents + [key], list_item)
                    )
                    for list_item in element
                ]
            else:
                new_dict[key] = self.convert_element(parents + [key], element)
        new_element = self.convert_element(parents, new_dict)
        if isinstance(new_element, dict):
            return ImmutableDict(new_element)
        else:
            return new_element

    def convert_element(self, parents: List[str], element: Any) -> Any:
        return element


class JsonStudyPromoter(JsonPromoter):
    next_version = JSONVersion.V1

    def convert_element(self, parents: List[str], element: Any) -> Any:
        element = self.convert_study_element(parents, element)
        if parents == []:
            try:
                _ = JSONVersion.from_string(element["version"])
                element["version"] = self.next_version.to_string()
            except ValueError:
                raise ValueError(f"Invalid version in json file: {element['version']}")
        return element

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        return element


class ConvertJsonV1(JsonStudyPromoter):
    next_version = JSONVersion.V2

    def __init__(self) -> None:
        self.scenario_fields = [
            "scenarios",
            "time_failed",
            "geocode_failed_sites",
            "geocode_failed_employees",
            "geocode_failed_both",
        ]

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == []:
            company = element["company"]
            territory = self._compute_territory(element)
            element["data"] = {
                "company": company,
                "territory": territory.name,
            }
            del element["company"]
            element["poi_scenarios"] = []
        elif parents[0] in self.scenario_fields:
            if parents[1:] == ["commutes"]:
                element["origin"] = element["employee"]
                element["destination"] = element["site"]
                del element["employee"]
                del element["site"]
        return element

    def _compute_territory(self, json_study: ImmutableDict) -> Territory:
        for site in json_study["sites"].values():
            coord = (site["coordinates"]["latitude"], site["coordinates"]["longitude"])
            return find_closest_city_center(coord)
        raise ValueError(f"No site found in Json.\n{json_study}")


class ConvertJsonV2(JsonStudyPromoter):
    next_version = JSONVersion.V3

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == []:
            element["isochrones"] = {}
        return element


class ConvertJsonV3(JsonStudyPromoter):
    next_version = JSONVersion.V4

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if len(parents) > 0 and parents[0] in [
            "scenarios",
            "poi_scenarios",
            "time_failed",
        ]:
            if parents[1:] == ["commutes", "commute_data"]:
                element["distance"] = ImmutableDict()
                element["emission"] = ImmutableDict()
        return element


class ConvertJsonV4(JsonStudyPromoter):
    next_version = JSONVersion.V5

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == ["time_failed", "commutes", "commute_data"]:
            element["best_mode"] = "WALK"
        return element


class ConvertJsonV5(JsonStudyPromoter):
    next_version = JSONVersion.V6

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if len(parents) == 2 and parents[0] == "employees":
            element["remote"] = False
        return element


class ConvertJsonV6(JsonStudyPromoter):
    next_version = JSONVersion.V7

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if len(parents) > 0 and parents[0] in [
            "scenarios",
            "poi_scenarios",
            "time_failed",
        ]:
            if parents[1:] == ["commutes", "commute_data"]:
                element["alternative_arrival_time"] = ImmutableDict()
        return element


class ConvertJsonV7(JsonStudyPromoter):
    next_version = JSONVersion.V8

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == []:
            element["public_transport_stations_scenario"] = {
                "data": None,
                "commutes": [],
            }
            element["public_transport_lines"] = []
            element["public_transport_stops"] = []
            element["bicycle_amenity_scenario"] = {
                "data": None,
                "commutes": [],
            }
            element["bicycle_amenities"] = []
            element["bicycle_lines"] = []
            element["car_amenity_scenario"] = {
                "data": None,
                "commutes": [],
            }
            element["car_amenities"] = []
            element["car_ways"] = []
        if parents == ["isochrones"]:
            isochrones = {}
            for site_id, pt_isochrones in element.items():
                pt_isochrone_1800, pt_isochrone_3600 = pt_isochrones
                isochrones[site_id] = {
                    "PUBLIC_TRANSPORT": {
                        "1800": pt_isochrone_1800,
                        "3600": pt_isochrone_3600,
                    }
                }
            element = isochrones
        return element


class ConvertJsonV8(JsonStudyPromoter):
    next_version = JSONVersion.V9

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if len(parents) == 2 and parents[0] in ["employees", "sites"]:
            element["address_details"] = ImmutableDict(
                {
                    "full": element["address"],
                    "normalized": "",
                    "city": "",
                    "postcode": "",
                    "citycode": "",
                    "coordinates": element["coordinates"],
                }
            )
        return element


class ConvertJsonV9(JsonStudyPromoter):
    next_version = JSONVersion.V10

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == []:
            element["coworking_scenario"] = {"data": None, "commutes": []}
            element["coworking_failed_scenario"] = {"data": None, "commutes": []}
        elif len(parents) > 0 and parents[0] in [
            "public_transport_stations_scenario",
            "car_amenity_scenario",
            "bicycle_amenity_scenario",
        ]:
            if parents[1:] == ["commutes", "commute_data"]:
                element["alternative_arrival_time"] = ImmutableDict()
        return element


class ConvertJsonV10(JsonStudyPromoter):
    next_version = JSONVersion.V11

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == ["bicycle_lines"]:
            element["is_lane"] = False
        return element


class ConvertJsonV11(JsonStudyPromoter):
    next_version = JSONVersion.V12

    def __init__(self) -> None:
        self.crit_air_computer = CritAirComputer()

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if len(parents) == 2 and parents[0] == "employees":
            element["crit_air"] = str(
                self.crit_air_computer.get_random_crit_air_for_citycode(
                    element["address_details"]["citycode"]
                )
            )
        return element


class ConvertJsonV12(JsonStudyPromoter):
    next_version = JSONVersion.V13

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == ["data"]:
            element["mission_id"] = "YYMXXX.V"
        return element


class ConvertJsonV13(JsonStudyPromoter):
    next_version = JSONVersion.V14


class ConvertJsonV14(JsonStudyPromoter):
    next_version = JSONVersion.V15

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == ["data"]:
            element["arrival_time"] = "(8, 30)"
            element["agency"] = ""
        return element


class ConvertJsonV15(JsonStudyPromoter):
    next_version = JSONVersion.V16


class ConvertJsonV16(JsonStudyPromoter):
    next_version = JSONVersion.V17

    def convert_study_element(self, parents: List[str], element: Any) -> Any:
        if parents == ["data"]:
            element["constraints"] = {"duration": {}}
        return element


class ConvertJsonV17(JsonStudyPromoter):
    next_version = JSONVersion.V18


class ConsolidatedJsonBuilder:
    def __init__(self, json_study: ImmutableDict) -> None:
        self.json_study = json_study
        self.employees_map = json_study["employees"]
        self.sites_map = json_study["sites"]
        self.scenarios_data_map = json_study["scenarios_data"]

    def build(self) -> ConsolidatedStudy:
        return ConsolidatedStudy(
            data=StudyData.from_json(self.json_study["data"]),
            scenarios=self.construct_scenarios(),
            poi_scenarios=self.construct_poi_scenarios(),
            infrastructure=self.construct_timed_infrastructure(),
            time_failed=self.construct_time_failed_scenarios(),
            geocode_failed_sites=self.construct_geocode_failed_sites_scenarios(),
            geocode_failed_employees=self.construct_geocode_failed_employees_scenarios(),
            geocode_failed_both=self.construct_geocode_failed_both_scenarios(),
            isochrones=self.construct_isochrones(),
            coworking_scenario=self.construct_coworking_scenario(
                self.json_study["coworking_scenario"]
            ),
            coworking_failed_scenario=self.construct_coworking_scenario(
                self.json_study["coworking_failed_scenario"]
            ),
        )

    def construct_coworking_scenario(
        self, json_coworking_scenario: Dict
    ) -> Scenario[None, GeoEmployee, CoworkingSite, ModalCommuteData]:
        return (
            self.construct_raw_id_scenario(json_coworking_scenario)
            .mutate_origins(self.employees_map)
            .update_origins(GeoEmployee.from_json)
            .update_destinations(CoworkingSite.from_json)
            .update_commutes(ModalCommuteData.from_json)
        )

    def construct_timed_infrastructure(self) -> TimedInfrastructure:
        return TimedInfrastructure(
            public_transport_stations_scenario=self.construct_public_transport_stations_scenario(),
            public_transport_stops=self.construct_public_transport_stops(),
            public_transport_lines=self.construct_public_transport_lines(),
            bicycle_amenity_scenario=self.construct_bicycle_amenity_scenario(),
            bicycle_amenity=self.construct_bicycle_amenity(),
            bicycle_lines=self.construct_bicycle_lines(),
            car_amenity_scenario=self.construct_car_amenity_scenario(),
            car_amenity=self.construct_car_amenity(),
            car_ways=self.construct_car_ways(),
        )

    def construct_scenarios(
        self,
    ) -> Scenarios[ScenarioData, GeoEmployee, GeoSite, ModalCommuteData]:
        return (
            self.construct_raw_scenarios(self.json_study["scenarios"])
            .update_origins(GeoEmployee.from_json)
            .update_destinations(GeoSite.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(ModalCommuteData.from_json)
        )

    def construct_poi_scenarios(
        self,
    ) -> Scenarios[ScenarioData, GeoSite, PointOfInterest, TimedCommuteData]:
        return (
            self.construct_raw_poi_scenarios(self.json_study["poi_scenarios"])
            .update_origins(GeoSite.from_json)
            .update_destinations(PointOfInterest.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(TimedCommuteData.from_json)
        )

    def construct_time_failed_scenarios(
        self,
    ) -> Scenarios[ScenarioData, GeoEmployee, GeoSite, ModalCommuteData]:
        return (
            self.construct_raw_scenarios(self.json_study["time_failed"])
            .update_origins(GeoEmployee.from_json)
            .update_destinations(GeoSite.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(ModalCommuteData.from_json)
        )

    def construct_geocode_failed_sites_scenarios(
        self,
    ) -> Scenarios[ScenarioData, GeoEmployee, FailedGeoSite, None]:
        return (
            self.construct_raw_scenarios(self.json_study["geocode_failed_sites"])
            .update_origins(GeoEmployee.from_json)
            .update_destinations(FailedGeoSite.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(lambda _: None)
        )

    def construct_geocode_failed_employees_scenarios(
        self,
    ) -> Scenarios[ScenarioData, FailedGeoEmployee, GeoSite, None]:
        return (
            self.construct_raw_scenarios(self.json_study["geocode_failed_employees"])
            .update_origins(FailedGeoEmployee.from_json)
            .update_destinations(GeoSite.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(lambda _: None)
        )

    def construct_geocode_failed_both_scenarios(
        self,
    ) -> Scenarios[ScenarioData, FailedGeoEmployee, FailedGeoSite, None]:
        return (
            self.construct_raw_scenarios(self.json_study["geocode_failed_both"])
            .update_origins(FailedGeoEmployee.from_json)
            .update_destinations(FailedGeoSite.from_json)
            .update_scenarios(ScenarioData.from_json)
            .update_commutes(lambda _: None)
        )

    def construct_car_amenity_scenario(
        self,
    ) -> CarAmenitiesScenario:
        scenario = self.json_study["car_amenity_scenario"]
        commutes = []
        for commute in scenario["commutes"]:
            if commute["origin"] not in self.sites_map:
                continue
            origin = GeoSite.from_json(self.sites_map[commute["origin"]])
            destination = CarAmenity.from_json(commute["destination"])
            commute_data = TimedCommuteData.from_json(commute["commute_data"])
            commutes.append(TimedCarCommute(origin, destination, commute_data))
        return Scenario(data=None, commutes=frozenset(commutes))

    def construct_bicycle_amenity_scenario(
        self,
    ) -> BicycleAmenitiesScenario:
        scenario = self.json_study["bicycle_amenity_scenario"]
        commutes = []
        for commute in scenario["commutes"]:
            if commute["origin"] not in self.sites_map:
                continue
            origin = GeoSite.from_json(self.sites_map[commute["origin"]])
            destination = BicycleAmenity.from_json(commute["destination"])
            commute_data = TimedCommuteData.from_json(commute["commute_data"])
            commutes.append(TimedBicycleCommute(origin, destination, commute_data))
        return Scenario(data=None, commutes=frozenset(commutes))

    def construct_public_transport_stations_scenario(
        self,
    ) -> PublicTransportStationsScenario:
        scenario = self.json_study["public_transport_stations_scenario"]
        commutes = []
        for commute in scenario["commutes"]:
            if commute["origin"] not in self.sites_map:
                continue
            origin = GeoSite.from_json(self.sites_map[commute["origin"]])
            destination = PublicTransportStop.from_json(commute["destination"])
            commute_data = TimedCommuteData.from_json(commute["commute_data"])
            commutes.append(
                TimedTransportStopCommute(origin, destination, commute_data)
            )
        return Scenario(data=None, commutes=frozenset(commutes))

    def construct_public_transport_stops(self) -> FrozenSet[PublicTransportStop]:
        return frozenset(
            [
                PublicTransportStop.from_json(s)
                for s in self.json_study["public_transport_stops"]
            ]
        )

    def construct_bicycle_amenity(self) -> FrozenSet[BicycleAmenity]:
        return frozenset(
            [BicycleAmenity.from_json(s) for s in self.json_study["bicycle_amenities"]]
        )

    def construct_car_amenity(self) -> FrozenSet[CarAmenity]:
        return frozenset(
            [CarAmenity.from_json(s) for s in self.json_study["car_amenities"]]
        )

    def construct_public_transport_lines(self) -> FrozenSet[PublicTransportLine]:
        return frozenset(
            [
                PublicTransportLine.from_json(line)
                for line in self.json_study["public_transport_lines"]
            ]
        )

    def construct_bicycle_lines(self) -> FrozenSet[BicycleLine]:
        return frozenset(
            [BicycleLine.from_json(line) for line in self.json_study["bicycle_lines"]]
        )

    def construct_car_ways(self) -> FrozenSet[CarWay]:
        return frozenset(
            [CarWay.from_json(line) for line in self.json_study["car_ways"]]
        )

    def construct_isochrones(self) -> SiteIsochrone:
        isochrones: SiteIsochrone = {}
        for site_id, json_modal_isochrones in self.json_study["isochrones"].items():
            if site_id not in self.sites_map:
                continue
            site = GeoSite.from_json(self.sites_map[site_id])
            modal_isochrones = {}
            for mode_str, json_timed_isochrones in json_modal_isochrones.items():
                mode = TransportMode.from_string(mode_str)
                timed_isochrones = {}
                for time_in_s, isochrone in json_timed_isochrones.items():
                    timed_isochrones[int(time_in_s) * seconds] = isochrone
                modal_isochrones[mode] = timed_isochrones
            isochrones[site] = modal_isochrones
        return isochrones

    def construct_raw_scenarios(
        self, scenarios: List
    ) -> Scenarios[ImmutableDict, ImmutableDict, ImmutableDict, ImmutableDict]:
        return (
            self.construct_raw_id_scenarios(scenarios)
            .mutate_destinations(self.sites_map)
            .mutate_origins(self.employees_map)
            .mutate_scenarios(self.scenarios_data_map)
        )

    def construct_raw_poi_scenarios(self, scenarios: List) -> Scenarios:
        return (
            self.construct_raw_id_scenarios(scenarios)
            .mutate_origins(self.sites_map)
            .mutate_scenarios(self.scenarios_data_map)
        )

    def construct_raw_id_scenarios(
        self, scenarios: List
    ) -> Scenarios[int, int, Union[int, ImmutableDict], ImmutableDict]:
        return Scenarios(
            frozenset([self.construct_raw_id_scenario(s) for s in scenarios])
        )

    def construct_raw_id_scenario(self, scenario: Dict) -> Scenario:
        return Scenario(
            data=scenario["data"],
            commutes=frozenset(
                [
                    Commute(c["origin"], c["destination"], c["commute_data"])
                    for c in scenario["commutes"]
                ]
            ),
        )


class ConsolidatedStudyConverter:
    def __init__(self, study: ConsolidatedStudy) -> None:
        self.study = study

    def convert_to_modal_study(self) -> ModalStudy:
        return ModalStudy(
            scenarios=Scenarios.merge(self.study.scenarios, self.study.time_failed),
            poi_scenarios=self.study.poi_scenarios,
            infrastructure=self.study.infrastructure,
            data=self.study.data,
            geocode_failed_sites=self.study.geocode_failed_sites,
            geocode_failed_employees=self.study.geocode_failed_employees,
            geocode_failed_both=self.study.geocode_failed_both,
            isochrones=self.study.isochrones,
            coworking_scenario=Scenario.merge(
                self.study.coworking_scenario, self.study.coworking_failed_scenario
            ),
        )

    def convert_to_timed_study(self) -> TimedStudy:
        scenarios = self.convert_consolidated_to_timed_scenarios(self.study.scenarios)
        time_failed = self.convert_consolidated_to_timed_scenarios(
            self.study.time_failed
        )
        coworking_scenario = self.study.coworking_scenario.update_commutes(
            convert_consolidated_to_timed_commute_data
        )
        coworking_failed_scenario = (
            self.study.coworking_failed_scenario.update_commutes(
                convert_consolidated_to_timed_commute_data
            )
        )
        return TimedStudy(
            scenarios=Scenarios.merge(scenarios, time_failed),
            poi_scenarios=self.study.poi_scenarios,
            infrastructure=self.study.infrastructure,
            data=self.study.data,
            geocode_failed_sites=self.study.geocode_failed_sites,
            geocode_failed_employees=self.study.geocode_failed_employees,
            geocode_failed_both=self.study.geocode_failed_both,
            isochrones=self.study.isochrones,
            coworking_scenario=Scenario.merge(
                coworking_scenario, coworking_failed_scenario
            ),
        )

    def convert_consolidated_to_timed_scenarios(
        self,
        scenarios: Scenarios[ScenarioData, GeoEmployee, GeoSite, ModalCommuteData],
    ) -> Scenarios[ScenarioData, GeoEmployee, GeoSite, TimedCommuteData]:
        return scenarios.update_commutes(convert_consolidated_to_timed_commute_data)


def convert_consolidated_to_timed_commute_data(
    data: ModalCommuteData,
) -> TimedCommuteData:
    return TimedCommuteData(
        duration=data.duration,
        distance=data.distance,
        emission=data.emission,
        alternative_arrival_time=data.alternative_arrival_time,
    )

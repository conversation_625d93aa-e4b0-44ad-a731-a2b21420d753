import dataclasses
from enum import Enum, unique
from typing import Dict, List

from mobility.funky import ImmutableDict, normalize_string


@unique
class TransportType(str, Enum):
    BUS = "bus"
    TRAM = "tram"
    SUBWAY = "subway"
    TRAIN = "train"
    MONORAIL = "monorail"
    FERRY = "ferry"
    AERIALWAY = "aerialway"

    @classmethod
    def from_string(cls, data: str) -> "TransportType":
        mapping = cls._get_string_map()
        try:
            return mapping[normalize_string(data)]
        except KeyError:
            raise ValueError(f"{data} is not a valid transport type.")

    @classmethod
    def _get_string_map(cls) -> Dict[str, "TransportType"]:
        return {
            "bus": cls.BUS,
            "tram": cls.TRAM,
            "subway": cls.SUBWAY,
            "train": cls.TRAIN,
            "monorail": cls.MONORAIL,
            "ferry": cls.FERRY,
            "aerialway": cls.AERIALWAY,
        }

    def translate_to_string(self) -> str:
        codes = {
            TransportType.BUS: "bus",
            TransportType.TRAM: "tram",
            TransportType.SUBWAY: "métro",
            TransportType.TRAIN: "train",
            TransportType.MONORAIL: "monorail",
            TransportType.FERRY: "ferry",
            TransportType.AERIALWAY: "aerialway",
        }
        return codes[self]

    @staticmethod
    def list_by_priority() -> List["TransportType"]:
        return [
            TransportType.AERIALWAY,
            TransportType.TRAIN,
            TransportType.FERRY,
            TransportType.MONORAIL,
            TransportType.SUBWAY,
            TransportType.TRAM,
            TransportType.BUS,
        ]


@unique
class TransportMode(str, Enum):
    WALK = "WALK"
    PUBLIC_TRANSPORT = "PUBLIC_TRANSPORT"
    CAR = "CAR"
    BICYCLE = "BICYCLE"
    CARPOOLING = "CARPOOLING"
    ELECTRIC_BICYCLE = "ELECTRIC_BICYCLE"
    ELECTRIC_CAR = "ELECTRIC_CAR"
    MOTORCYCLE = "MOTORCYCLE"
    AIRPLANE = "AIRPLANE"
    ELECTRIC_MOTORCYCLE = "ELECTRIC_MOTORCYCLE"
    FAST_BICYCLE = "FAST_BICYCLE"
    CAR_PUBLIC_TRANSPORT = "CAR_PUBLIC_TRANSPORT"
    BICYCLE_PUBLIC_TRANSPORT = "BICYCLE_PUBLIC_TRANSPORT"

    def __repr__(self) -> str:
        return self

    def __str__(self) -> str:
        return self.value

    @classmethod
    def _get_string_map(cls) -> Dict[str, "TransportMode"]:
        return {
            "walk": cls.WALK,
            "marche": cls.WALK,
            "marche a pied": cls.WALK,
            "public_transport": cls.PUBLIC_TRANSPORT,
            "public transport": cls.PUBLIC_TRANSPORT,
            "transport en commun": cls.PUBLIC_TRANSPORT,
            "transports en commun": cls.PUBLIC_TRANSPORT,
            "car": cls.CAR,
            "voiture": cls.CAR,
            "bicycle": cls.BICYCLE,
            "velo": cls.BICYCLE,
            "carpooling": cls.CARPOOLING,
            "covoiturage": cls.CARPOOLING,
            "electric_bicycle": cls.ELECTRIC_BICYCLE,
            "electric bicycle": cls.ELECTRIC_BICYCLE,
            "ebike": cls.ELECTRIC_BICYCLE,
            "velo electrique": cls.ELECTRIC_BICYCLE,
            "electric_car": cls.ELECTRIC_CAR,
            "electric car": cls.ELECTRIC_CAR,
            "voiture electrique": cls.ELECTRIC_CAR,
            "motorcycle": cls.MOTORCYCLE,
            "moto": cls.MOTORCYCLE,
            "plane": cls.AIRPLANE,
            "airplane": cls.AIRPLANE,
            "avion": cls.AIRPLANE,
            "electric_motorcycle": cls.ELECTRIC_MOTORCYCLE,
            "electric motorcycle": cls.ELECTRIC_MOTORCYCLE,
            "moto electrique": cls.ELECTRIC_MOTORCYCLE,
            "fast_bicycle": cls.FAST_BICYCLE,
            "fast bicycle": cls.FAST_BICYCLE,
            "velo electrique 45km/h": cls.FAST_BICYCLE,
            "car_public_transport": cls.CAR_PUBLIC_TRANSPORT,
            "car public transport": cls.CAR_PUBLIC_TRANSPORT,
            "voiture + tc": cls.CAR_PUBLIC_TRANSPORT,
            "bicycle_public_transport": cls.BICYCLE_PUBLIC_TRANSPORT,
            "bicycle public transport": cls.BICYCLE_PUBLIC_TRANSPORT,
            "velo + tc": cls.BICYCLE_PUBLIC_TRANSPORT,
        }

    @classmethod
    def from_string(cls, data: str) -> "TransportMode":
        mapping = cls._get_string_map()
        try:
            return mapping[normalize_string(data)]
        except KeyError:
            raise ValueError(f"{data} is not a valid transport mode.")

    @classmethod
    def ground(cls) -> List["TransportMode"]:
        return [mode for mode in cls if mode != cls.AIRPLANE]

    def charcode(self) -> str:
        codes = {
            TransportMode.WALK: "W",
            TransportMode.PUBLIC_TRANSPORT: "T",
            TransportMode.CAR: "C",
            TransportMode.BICYCLE: "B",
            TransportMode.CARPOOLING: "P",
            TransportMode.ELECTRIC_BICYCLE: "b",
            TransportMode.ELECTRIC_CAR: "c",
            TransportMode.MOTORCYCLE: "M",
            TransportMode.AIRPLANE: "A",
            TransportMode.ELECTRIC_MOTORCYCLE: "m",
            TransportMode.FAST_BICYCLE: "F",
            TransportMode.CAR_PUBLIC_TRANSPORT: "I",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "i",
        }
        return codes[self]

    def translate_to_string(self) -> str:
        codes = {
            TransportMode.WALK: "marche à pied",
            TransportMode.PUBLIC_TRANSPORT: "transports en commun",
            TransportMode.CAR: "voiture",
            TransportMode.BICYCLE: "vélo",
            TransportMode.CARPOOLING: "covoiturage",
            TransportMode.ELECTRIC_BICYCLE: "vélo électrique",
            TransportMode.ELECTRIC_CAR: "voiture électrique",
            TransportMode.MOTORCYCLE: "moto",
            TransportMode.AIRPLANE: "avion",
            TransportMode.ELECTRIC_MOTORCYCLE: "moto électrique",
            TransportMode.FAST_BICYCLE: "vélo électrique rapide",
            TransportMode.CAR_PUBLIC_TRANSPORT: "intermodal voiture/TC",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "intermodal vélo/TC",
        }
        return codes[self]

    def translate_to_article(self) -> str:
        codes = {
            TransportMode.WALK: "la marche à pied",
            TransportMode.PUBLIC_TRANSPORT: "les transports en commun",
            TransportMode.CAR: "la voiture",
            TransportMode.BICYCLE: "le vélo",
            TransportMode.CARPOOLING: "le covoiturage",
            TransportMode.ELECTRIC_BICYCLE: "le vélo électrique",
            TransportMode.ELECTRIC_CAR: "la voiture électrique",
            TransportMode.MOTORCYCLE: "la moto",
            TransportMode.AIRPLANE: "l’avion",
            TransportMode.ELECTRIC_MOTORCYCLE: "la moto électrique",
            TransportMode.FAST_BICYCLE: "le vélo électrique rapide",
            TransportMode.CAR_PUBLIC_TRANSPORT: "l’intermodal voiture/TC",
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "l’intermodal vélo/TC",
        }
        return codes[self]


Duration = ImmutableDict[TransportMode, int]
Distance = ImmutableDict[TransportMode, int]
Emission = ImmutableDict[TransportMode, int]


class DetailedTransportMode:
    pass


@dataclasses.dataclass(eq=True, frozen=True)
class CarpoolingMode(DetailedTransportMode):
    is_driver: bool
    nb_passengers: int


@dataclasses.dataclass(eq=True, frozen=True)
class BaseTransportMode(DetailedTransportMode):
    mode: TransportMode


class TransportModeLattice:
    @staticmethod
    def union(mode_a: TransportMode, mode_b: TransportMode) -> TransportMode:
        if mode_a == TransportMode.CAR or mode_b == TransportMode.CAR:
            return TransportMode.CAR
        elif (
            mode_a == TransportMode.PUBLIC_TRANSPORT
            or mode_b == TransportMode.PUBLIC_TRANSPORT
        ):
            return TransportMode.PUBLIC_TRANSPORT
        elif mode_a == TransportMode.BICYCLE or mode_b == TransportMode.BICYCLE:
            return TransportMode.BICYCLE
        elif mode_a == TransportMode.WALK or mode_b == TransportMode.WALK:
            return TransportMode.WALK
        raise NotImplementedError(
            "Transport mode union <{mode_a} U {mode_b}> is not supported"
        )

    @staticmethod
    def abstract(mode: DetailedTransportMode) -> TransportMode:
        match mode:
            case CarpoolingMode():
                return TransportMode.CARPOOLING
            case BaseTransportMode(base_mode):
                return base_mode
        raise NotImplementedError(f"Mode {mode} has no defined abstraction")


@unique
class CommuterProfile(str, Enum):
    CYCLIST = "CYCLIST"
    PT_USER = "PT_USER"
    CAR_USER = "CAR_USER"
    WALKER = "WALKER"
    CARPOOLER = "CARPOOLER"
    REMOTE_WORKER = "REMOTE_WORKER"
    COWORKER = "COWORKER"

    def translate_to_string(self) -> str:
        codes = {
            CommuterProfile.CYCLIST: "Cycliste",
            CommuterProfile.PT_USER: "Usager TC",
            CommuterProfile.CAR_USER: "Usager voiture",
            CommuterProfile.WALKER: "Piéton",
            CommuterProfile.CARPOOLER: "Covoitureur",
            CommuterProfile.REMOTE_WORKER: "Télétravailleur",
            CommuterProfile.COWORKER: "Coworker",
        }
        return codes[self]

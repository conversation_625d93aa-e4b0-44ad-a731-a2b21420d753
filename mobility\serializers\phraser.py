from mobility import config
from mobility.funky import join_endlink_strings, sorted_dict_by_value
from mobility.ir.indicators.scenario_indicators import ScenarioIndicators
from mobility.ir.transport import (
    BaseTransportMode,
    CarpoolingMode,
    DetailedTransportMode,
    TransportMode,
)
from mobility.ir.work_mode import WorkMode
from mobility.workers.ideal_scenario import IdealScenarioComputer
from mobility.workers.zfe_impact_computer import ZFEImpactComputer


class Phraser:
    def __init__(self) -> None:
        self.zfe_computer = ZFEImpactComputer(
            config["mobility"].get("territories_dir", ".")
        )

    def describe_ideal_plan(self, base_scenario: ScenarioIndicators) -> str:
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        ideal_scenario_summary_indicators = ideal_computer.compute_summary_indicator(
            employees_plan
        )
        modes_description = []
        first_bit = True
        sorted_transport_mode = sorted_dict_by_value(
            ideal_scenario_summary_indicators.nb_employees_by_transport_mode,
            lambda v: v,
            reverse=True,
        )
        for mode, nb_employees in sorted_transport_mode.items():
            if nb_employees > 0:
                if first_bit:
                    first_bit = False
                    description = f"{nb_employees} personnes se rendent au travail {self.phrase_detailed_transport_mode_usage(mode)}"
                else:
                    description = f"{nb_employees} {self.phrase_detailed_transport_mode_usage(mode)}"
                modes_description.append(description)
        nb_coworkers = ideal_scenario_summary_indicators.nb_employees_by_work_mode.get(
            WorkMode.COWORKING, 0
        )
        nb_home_office = (
            ideal_scenario_summary_indicators.nb_employees_by_work_mode.get(
                WorkMode.HOME_OFFICE, 0
            )
        )
        nb_on_site = ideal_scenario_summary_indicators.nb_employees_by_work_mode.get(
            WorkMode.ON_SITE_FULL_TIME, 0
        )
        if nb_coworkers > 0 and nb_on_site > 0:
            if nb_on_site >= nb_coworkers:
                work_phrase = f"La plupart des salariés se rend toujours sur le même site, mais {nb_coworkers} travaillent depuis un site de coworking alternatif"
            else:
                work_phrase = f"La plupart des salariés se rend sur un site de coworking alternatif, mais {nb_on_site} travaillent toujours sur le même site"
        elif nb_coworkers > 0:
            work_phrase = "Tous les salariés se rendent sur un site de coworking"
        else:
            work_phrase = (
                "Tous les salariés se rendent sur le même site qu'actuellement"
            )
        if nb_home_office > 0:
            work_phrase += (
                f" et {nb_home_office} personnes ont une journée de télétravail."
            )
        else:
            work_phrase += "."
        return (
            "Dans ce plan, "
            + join_endlink_strings(modes_description, " et ")
            + ". "
            + work_phrase
        )

    def detail_ideal_plan_consequences(self, base_scenario: ScenarioIndicators) -> str:
        ideal_computer = IdealScenarioComputer(base_scenario, self.zfe_computer)
        employees_plan = ideal_computer.compute_ideal_plan_for_employees(priority=None)
        ideal_scenario_summary_indicators = ideal_computer.compute_summary_indicator(
            employees_plan
        )
        phrases = []
        base_parking = base_scenario.parking_spots
        if base_parking > 0:
            ideal_parking = ideal_scenario_summary_indicators.parking_spots
            parking_need_pct = (ideal_parking - base_parking) / base_parking * 100
            parking_phrase = f"les besoins en parking réduisent de {abs(parking_need_pct)} % ({ideal_parking})"
            phrases.append(parking_phrase)
        base_emissions = base_scenario.get_carbon_emission()
        if base_emissions > 0:
            ideal_emissions = ideal_scenario_summary_indicators.emissions
            carbon_emission_pct = (
                (ideal_emissions - base_emissions) / base_emissions * 100
            )
            emissions_phrase = f"les émissions carbone réduisent de {abs(carbon_emission_pct)} % ({ideal_emissions})"
            phrases.append(emissions_phrase)
        base_costs = base_scenario.costs.total
        if base_costs > 0:
            ideal_costs = ideal_scenario_summary_indicators.total_cost
            cost_pct = (ideal_costs - base_costs) / base_costs * 100
            cost_phrase = f"les coûts liés à la mobilité diminuent de {abs(cost_pct)} % ({ideal_costs})"
            phrases.append(cost_phrase)
        return "Dans cette situation " + join_endlink_strings(phrases, " et ") + "."

    def phrase_detailed_transport_mode_usage(self, mode: DetailedTransportMode) -> str:
        if isinstance(mode, CarpoolingMode):
            return "en covoiturage"
        elif isinstance(mode, BaseTransportMode):
            return {
                TransportMode.WALK: "à pied",
                TransportMode.PUBLIC_TRANSPORT: "en transports en commun",
                TransportMode.CAR: "en voiture",
                TransportMode.BICYCLE: "à vélo",
                TransportMode.CARPOOLING: "en covoiturage",
                TransportMode.ELECTRIC_BICYCLE: "en vélo électrique",
                TransportMode.ELECTRIC_CAR: "en voiture électrique",
                TransportMode.MOTORCYCLE: "à moto",
                TransportMode.AIRPLANE: "en avion",
                TransportMode.ELECTRIC_MOTORCYCLE: "en moto électrique",
                TransportMode.FAST_BICYCLE: "en vélo électrique rapide",
                TransportMode.CAR_PUBLIC_TRANSPORT: "en intermodal voiture/TC",
                TransportMode.BICYCLE_PUBLIC_TRANSPORT: "en intermodal vélo/TC",
            }[mode.mode]
        raise NotImplementedError(f"No detail phrasing for mode {mode}")

    def describe_site_locations(self, indicators: ScenarioIndicators) -> str:
        sites = indicators.get_sites()
        top_cities_with_employee_count = indicators.get_cities_with_most_employees()
        top_city = list(top_cities_with_employee_count.keys())[0]
        top_city_employee_count = indicators.get_cities_with_most_employees()[top_city]
        top_city_employee_count_text = f"({top_city_employee_count} personne{'s' if top_city_employee_count != 1 else ''})"

        if len(sites) == 1:
            site = sites[0]
            site_city = site.address_details.city
            text = f"Le site <em>{site.nickname}</em> est implanté dans la commune de {site_city}. "
            if site_city == top_city:
                text += f"C'est également la commune où résident le plus de salariés {top_city_employee_count_text}."
            else:
                text += f"C'est dans la commune de {top_city} que réside le plus grand nombre de salariés {top_city_employee_count_text}."
            return text

        sites_by_commune = indicators.get_sites_by_commune()
        site_ranks = {
            commune: rank
            for rank, commune in enumerate(
                list(top_cities_with_employee_count.keys()), start=1
            )
        }
        parts = []
        for commune, site_names in sites_by_commune.items():
            site_list = (
                ", ".join(site_names[:-1]) + " et " + site_names[-1]
                if len(site_names) > 1
                else site_names[0]
            )
            site_rank = site_ranks.get(commune, None)
            if site_rank is not None:
                site_rank_str = f"{site_rank}-ième " if site_rank > 1 else ""
                employee_count = top_cities_with_employee_count[commune]
                rank_text = (
                    f"Il s'agit de la {site_rank_str}commune où se trouve le plus grand nombre de salariés "
                    f"({employee_count} personne{'s' if employee_count != 1 else ''})"
                )
            else:
                rank_text = "Cette commune ne fait pas partie des communes où réside le plus de salariés"

            if len(site_names) == 1:
                parts.append(
                    f"Le site <em>{site_list}</em> est implanté dans la commune de {commune}. {rank_text}"
                )
            else:
                parts.append(
                    f"Les sites <em>{site_list}</em> sont implantés dans la commune de {commune}. {rank_text}"
                )

        return ". ".join(parts) + "."

{"data_mtime": 1751444475, "dep_lines": [5, 6, 7, 9, 10, 11, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.geo_study", "mobility.ir.journey", "mobility.ir.transport", "mobility.repositories.journey", "mobility.use_cases.journey", "webservice.assembler.flask_db", "mobility.quantity", "typing", "flask", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "flask.blueprints", "flask.globals", "flask.helpers", "flask.templating", "flask.wrappers", "mobility", "mobility.ir", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "types", "typing_extensions", "werkzeug", "werkzeug.datastructures", "werkzeug.utils", "werkzeug.wrappers"], "hash": "646f133a4250e892d35d0946e87655d6989942ec", "id": "webservice.assembler.routes.journey", "ignore_all": false, "interface_hash": "fac5c28891854e948eaeb167f18b38df2d7e2414", "mtime": 1722327437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\assembler\\routes\\journey.py", "plugin_data": null, "size": 3283, "suppressed": [], "version_id": "1.16.1"}
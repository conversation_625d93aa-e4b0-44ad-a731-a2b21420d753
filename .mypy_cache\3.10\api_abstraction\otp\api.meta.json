{"data_mtime": 1753176774, "dep_lines": [12, 13, 14, 15, 16, 18, 19, 20, 22, 17, 21, 1, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 7, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.date_iterators", "api_abstraction.api.event_reporter", "api_abstraction.api.hypotheses", "api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.constants", "mobility.quantity", "datetime", "json", "logging", "itertools", "typing", "requests", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "enum", "http", "http.cookiejar", "json.decoder", "mobility", "mobility.ir", "requests.auth", "requests.exceptions", "requests.models", "types", "typing_extensions"], "hash": "1fa9b1559741d8a8957bd771ad6636060cda8afe", "id": "api_abstraction.otp.api", "ignore_all": false, "interface_hash": "cf364f8246a0ea6eb4af0c2928340e01cffc36e5", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\otp\\api.py", "plugin_data": null, "size": 18210, "suppressed": ["shapely.geometry", "geopandas", "shapely"], "version_id": "1.16.1"}
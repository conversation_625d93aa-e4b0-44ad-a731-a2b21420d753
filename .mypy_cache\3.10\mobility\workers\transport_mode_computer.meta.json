{"data_mtime": 1751444410, "dep_lines": [7, 10, 11, 16, 17, 27, 28, 30, 31, 8, 9, 29, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.builders.territory_database", "mobility.ir.geo_study", "mobility.ir.mode_constraints", "mobility.ir.mode_share_rules", "mobility.ir.study", "mobility.ir.study_types", "mobility.ir.transport", "mobility.repositories.abstract_repositories", "mobility.workers.distance_computer", "mobility.constants", "mobility.funky", "mobility.modal_shift", "json", "collections", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "mobility.builders", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.quantity", "mobility.repositories", "numpy._typing", "numpy._typing._ufunc", "os", "typing_extensions"], "hash": "b9f8f92c0a59a06ae4e39093ad5f4b730ef9f89b", "id": "mobility.workers.transport_mode_computer", "ignore_all": false, "interface_hash": "2b2867dd2ac59fae83c36f679d30f4e5e9191759", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\transport_mode_computer.py", "plugin_data": null, "size": 42759, "suppressed": [], "version_id": "1.16.1"}
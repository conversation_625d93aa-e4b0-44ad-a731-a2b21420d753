{"data_mtime": 1752154445, "dep_lines": [3, 4, 5, 6, 8, 9, 10, 11, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["api_abstraction.otp.api", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.serializers.chart_writer", "mobility.serializers.plotly_map_maker", "mobility.serializers.plotly_theme", "mobility.workers.color_picker", "mobility.quantity", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.otp", "enum", "mobility", "mobility.ir", "mobility.ir.map_elements", "mobility.ir.territory", "mobility.serializers", "mobility.serializers.base_map_maker", "mobility.serializers.charters", "mobility.serializers.charters.svg_map_addons", "mobility.workers", "typing", "typing_extensions"], "hash": "8c2ebd2772b42eec0efb3126e3c8df94c7f8f120", "id": "audit.sequence_isochrones", "ignore_all": false, "interface_hash": "38b50e8578d04d0240ed99af173ad845a5ac9048", "mtime": 1726151661, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "audit\\sequence_isochrones.py", "plugin_data": null, "size": 2669, "suppressed": ["shapely.geometry"], "version_id": "1.16.1"}
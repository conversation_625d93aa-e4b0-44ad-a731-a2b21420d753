from typing import Any

import pytest

from mobility.funky import ImmutableDict
from mobility.ir.study import TimedCommuteData
from mobility.ir.study_types import Sc<PERSON><PERSON>, Scenarios
from mobility.ir.transport import TransportMode
from mobility.workers.emission_computer import (
    compute_missing_emissions_from_distance,
    compute_missing_emissions_from_distance_with_alternatives,
    compute_mode_emission_from_distance,
    fill_missing_emissions,
)


class TestStudyFilling:
    def test_should_fill_missing_emissions_in_study(
        self,
        timed_study_factory: Any,
        geo_employee: Any,
        geo_site: Any,
        scenario_data_factory: Any,
        point_of_interest: Any,
        timed_infrastructure,
    ) -> None:
        commute = TimedCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 5000,
                    TransportMode.PUBLIC_TRANSPORT: 3000,
                    TransportMode.CAR: 1000,
                }
            ),
            distance=ImmutableDict({TransportMode.CAR: 10000}),
            emission=ImmutableDict(
                {TransportMode.WALK: 0, TransportMode.PUBLIC_TRANSPORT: 300}
            ),
            alternative_arrival_time=ImmutableDict(),
        )
        scenario = Scenario.from_commutes(
            scenario_data_factory(id=1), {(geo_employee, geo_site, commute)}
        )
        poi_scenario = Scenario.from_commutes(
            scenario_data_factory(id=1), {(geo_site, point_of_interest, commute)}
        )
        study = timed_study_factory(
            scenarios=Scenarios(frozenset({scenario})),
            poi_scenarios=Scenarios(frozenset({poi_scenario})),
            infrastructure=timed_infrastructure(),
        )

        filled_study = fill_missing_emissions(study)

        expected_commute = TimedCommuteData(
            duration=ImmutableDict(
                {
                    TransportMode.WALK: 5000,
                    TransportMode.PUBLIC_TRANSPORT: 3000,
                    TransportMode.CAR: 1000,
                }
            ),
            distance=ImmutableDict({TransportMode.CAR: 10000}),
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 0,
                    TransportMode.PUBLIC_TRANSPORT: 300,
                    TransportMode.CAR: 1932,
                }
            ),
            alternative_arrival_time=ImmutableDict(),
        )
        expected_scenario = Scenario.from_commutes(
            scenario_data_factory(id=1), {(geo_employee, geo_site, expected_commute)}
        )
        expected_poi_scenario = Scenario.from_commutes(
            scenario_data_factory(id=1),
            {(geo_site, point_of_interest, expected_commute)},
        )
        expected_study = timed_study_factory(
            scenarios=Scenarios(frozenset({expected_scenario})),
            poi_scenarios=Scenarios(frozenset({expected_poi_scenario})),
            infrastructure=timed_infrastructure(),
        )
        assert filled_study == expected_study


class TestEmissionComputation:
    @pytest.mark.parametrize("mode", TransportMode)
    def test_should_compute_emission_for_any_mode(self, mode: TransportMode) -> None:
        computed_emission = compute_mode_emission_from_distance(mode, 10)

        assert computed_emission >= 0

    @pytest.mark.parametrize(
        "mode,emission",
        [
            (TransportMode.WALK, 0),
            (TransportMode.CAR, 1932),
            (TransportMode.BICYCLE, 0),
            (TransportMode.PUBLIC_TRANSPORT, 290),
            (TransportMode.MOTORCYCLE, 1650),
            (TransportMode.CARPOOLING, 960),
            (TransportMode.ELECTRIC_BICYCLE, 22),
            (TransportMode.ELECTRIC_CAR, 198),
            (TransportMode.ELECTRIC_MOTORCYCLE, 198),
            (TransportMode.AIRPLANE, 2300),
            (TransportMode.FAST_BICYCLE, 22),
            (TransportMode.CAR_PUBLIC_TRANSPORT, 618),
            (TransportMode.BICYCLE_PUBLIC_TRANSPORT, 253),
        ],
    )
    def test_should_compute_emission_for_10km_in_mode(
        self, mode: Any, emission: Any
    ) -> None:
        computed_emission = compute_mode_emission_from_distance(mode, 10 * 1000)

        assert computed_emission == emission

    @pytest.mark.parametrize("mode", [TransportMode.WALK, TransportMode.BICYCLE])
    def test_should_set_emissions_to_zero(
        self,
        timed_commute_data_factory: Any,
        mode: TransportMode,
    ) -> None:
        commute_data = timed_commute_data_factory(
            duration=ImmutableDict({mode: 300}),
            distance=ImmutableDict({mode: 500}),
            emission=ImmutableDict(),
        )

        filled_commute_data = compute_missing_emissions_from_distance(commute_data)

        assert filled_commute_data.emission == ImmutableDict({mode: 0})

    @pytest.mark.parametrize("mode", [TransportMode.WALK, TransportMode.BICYCLE])
    def test_should_set_emissions_to_zero_even_without_distance(
        self,
        timed_commute_data_factory: Any,
        mode: TransportMode,
    ) -> None:
        commute_data = timed_commute_data_factory(
            duration=ImmutableDict({mode: 300}),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
        )

        filled_commute_data = compute_missing_emissions_from_distance(commute_data)

        assert filled_commute_data.emission == ImmutableDict({mode: 0})

    @pytest.mark.parametrize(
        "mode",
        [
            TransportMode.CAR,
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.CARPOOLING,
            TransportMode.MOTORCYCLE,
            TransportMode.ELECTRIC_CAR,
            TransportMode.ELECTRIC_BICYCLE,
        ],
    )
    def test_should_fail_to_compute_emission_if_distance_missing(
        self,
        timed_commute_data_factory: Any,
        mode: TransportMode,
    ) -> None:
        commute_data = timed_commute_data_factory(
            duration=ImmutableDict({mode: 300}),
            distance=ImmutableDict(),
            emission=ImmutableDict(),
        )

        with pytest.raises(ValueError):
            compute_missing_emissions_from_distance(commute_data)

    def test_should_not_compute_emission_if_duration_missing(
        self, timed_commute_data_factory: Any
    ) -> None:
        commute_data = timed_commute_data_factory(
            duration=ImmutableDict(),
            distance=ImmutableDict(
                {
                    TransportMode.WALK: 300,
                    TransportMode.CAR: 300,
                    TransportMode.BICYCLE: 300,
                    TransportMode.PUBLIC_TRANSPORT: 300,
                }
            ),
            emission=ImmutableDict(),
        )

        unchanged_data = compute_missing_emissions_from_distance_with_alternatives(
            commute_data
        )

        assert unchanged_data == commute_data

    def test_should_compute_emission_in_alternative_arrival_time(
        self, timed_commute_data_factory: Any, base_commute_data: Any
    ) -> None:
        dummy_dict = ImmutableDict(
            {
                TransportMode.WALK: 300,
                TransportMode.CAR: 300,
                TransportMode.BICYCLE: 300,
                TransportMode.PUBLIC_TRANSPORT: 300,
            }
        )
        commute_data = timed_commute_data_factory(
            duration=dummy_dict,
            distance=dummy_dict,
            emission=ImmutableDict(),
            alternative_arrival_time=ImmutableDict(
                {
                    (10, 0): base_commute_data(
                        duration=dummy_dict,
                        distance=dummy_dict,
                        emission=ImmutableDict(),
                    )
                }
            ),
        )

        filled_data = compute_missing_emissions_from_distance_with_alternatives(
            commute_data
        )

        expected_commute_data = timed_commute_data_factory(
            duration=dummy_dict,
            distance=dummy_dict,
            emission=ImmutableDict(
                {
                    TransportMode.WALK: 0,
                    TransportMode.CAR: 57,
                    TransportMode.BICYCLE: 0,
                    TransportMode.PUBLIC_TRANSPORT: 8,
                }
            ),
            alternative_arrival_time=ImmutableDict(
                {
                    (10, 0): base_commute_data(
                        duration=dummy_dict,
                        distance=dummy_dict,
                        emission=ImmutableDict(
                            {
                                TransportMode.WALK: 0,
                                TransportMode.CAR: 57,
                                TransportMode.BICYCLE: 0,
                                TransportMode.PUBLIC_TRANSPORT: 8,
                            }
                        ),
                    )
                }
            ),
        )
        assert filled_data == expected_commute_data

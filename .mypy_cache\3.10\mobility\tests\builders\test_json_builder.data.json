{".class": "MypyFile", "_fullname": "mobility.tests.builders.test_json_builder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Address": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Address", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyOf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.AnyOf", "name": "AnyOf", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.AnyOf", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.AnyOf", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of AnyOf", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__hash__ of AnyOf", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "choices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "choices"], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AnyOf", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of AnyOf", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__req__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__req__", "name": "__req__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__req__ of AnyOf", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["mobility.tests.builders.test_json_builder.AnyOf"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of AnyOf", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "choices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.AnyOf.choices", "name": "choices", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.AnyOf.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.AnyOf", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConsolidatedJsonBuilder": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.ConsolidatedJsonBuilder", "kind": "Gdef"}, "ConsolidatedStudyConverter": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.ConsolidatedStudyConverter", "kind": "Gdef"}, "CritAirCategory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.crit_air.CritAirCategory", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "JSONVersion": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.JSONVersion", "kind": "Gdef"}, "JsonPromoter": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.JsonPromoter", "kind": "Gdef"}, "JsonStudyPromoter": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.JsonStudyPromoter", "kind": "Gdef"}, "PoiScenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.PoiScenarios", "kind": "Gdef"}, "Promoter@1392": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.Promoter@1392", "name": "Promoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.Promoter@1392", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.Promoter@1392", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.Promoter@1392.convert_element", "name": "convert_element", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.Promoter@1392", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Promoter@1428": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mobility.builders.json_builder.JsonStudyPromoter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.Promoter@1428", "name": "Promoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.Promoter@1428", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.Promoter@1428", "mobility.builders.json_builder.JsonStudyPromoter", "mobility.builders.json_builder.JsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "convert_study_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parents", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.Promoter@1428.convert_study_element", "name": "convert_study_element", "type": null}}, "next_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mobility.tests.builders.test_json_builder.Promoter@1428.next_version", "name": "next_version", "setter_type": null, "type": "mobility.builders.json_builder.JSONVersion"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.Promoter@1428", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PublicTransportLine": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportLine", "kind": "Gdef"}, "PublicTransportStationsScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.PublicTransportStationsScenario", "kind": "Gdef"}, "PublicTransportStop": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.infrastructure.PublicTransportStop", "kind": "Gdef"}, "Scenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenario", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "StudyJsonBuilder": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.StudyJsonBuilder", "kind": "Gdef"}, "StudyJsonSerializer": {".class": "SymbolTableNode", "cross_ref": "mobility.serializers.json_serializer.StudyJsonSerializer", "kind": "Gdef"}, "TestConsolidatedStudyConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", "name": "TestConsolidatedStudyConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", "builtins.object"], "names": {".class": "SymbolTable", "test_move_failed_time_commute_in_every_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "modal_commute_data_factory", "timed_commute_data_factory", "geo_employee_factory", "scenario_data_factory", "geo_site", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_move_failed_time_commute_in_every_scenarios", "name": "test_move_failed_time_commute_in_every_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "modal_commute_data_factory", "timed_commute_data_factory", "geo_employee_factory", "scenario_data_factory", "geo_site", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_move_failed_time_commute_in_every_scenarios of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_copy_poi_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "timed_commute_data", "point_of_interest", "geo_site", "scenario_data", "timed_infrastructure", "consolidated_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_should_copy_poi_scenario", "name": "test_should_copy_poi_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "timed_commute_data", "point_of_interest", "geo_site", "scenario_data", "timed_infrastructure", "consolidated_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_copy_poi_scenario of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_do_nothing_if_no_time_error_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenario_data", "geo_site", "timed_commute_data", "geo_employee", "modal_commute_data", "timed_infrastructure", "consolidated_study_factory", "timed_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_should_do_nothing_if_no_time_error_commute", "name": "test_should_do_nothing_if_no_time_error_commute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scenario_data", "geo_site", "timed_commute_data", "geo_employee", "modal_commute_data", "timed_infrastructure", "consolidated_study_factory", "timed_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_do_nothing_if_no_time_error_commute of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_gather_geocode_failures_in_multiple_studies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "failed_geo_site", "modal_commute_data", "scenario_data_factory", "geo_employee", "geo_site", "timed_commute_data", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_should_gather_geocode_failures_in_multiple_studies", "name": "test_should_gather_geocode_failures_in_multiple_studies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "failed_geo_site", "modal_commute_data", "scenario_data_factory", "geo_employee", "geo_site", "timed_commute_data", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_gather_geocode_failures_in_multiple_studies of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_gather_geocode_failures_in_only_failed_geocode_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "modal_commute_data", "failed_geo_employee", "scenario_data", "geo_employee", "geo_site", "timed_commute_data", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_should_gather_geocode_failures_in_only_failed_geocode_study", "name": "test_should_gather_geocode_failures_in_only_failed_geocode_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "modal_commute_data", "failed_geo_employee", "scenario_data", "geo_employee", "geo_site", "timed_commute_data", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_gather_geocode_failures_in_only_failed_geocode_study of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_move_time_failed_commute_to_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "geo_employee_factory", "timed_commute_data_factory", "scenario_data", "geo_site", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.test_should_move_time_failed_commute_to_scenarios", "name": "test_should_move_time_failed_commute_to_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "geo_employee_factory", "timed_commute_data_factory", "scenario_data", "geo_site", "timed_infrastructure", "timed_study_factory", "consolidated_study_factory", "study_data"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_move_time_failed_commute_to_scenarios of TestConsolidatedStudyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestConsolidatedStudyJsonBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", "name": "TestConsolidatedStudyJsonBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", "builtins.object"], "names": {".class": "SymbolTable", "test_should_parse_json_into_consolidated_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "consolidated_study_factory", "timed_infrastructure", "poi_scenario", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.test_should_parse_json_into_consolidated_study", "name": "test_should_parse_json_into_consolidated_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "consolidated_study_factory", "timed_infrastructure", "poi_scenario", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_into_consolidated_study of TestConsolidatedStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_parse_json_isochrones": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "geo_site_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.test_should_parse_json_isochrones", "name": "test_should_parse_json_isochrones", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "geo_site_factory"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_isochrones of TestConsolidatedStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_parse_json_public_transport_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.test_should_parse_json_public_transport_lines", "name": "test_should_parse_json_public_transport_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_public_transport_lines of TestConsolidatedStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_parse_json_public_transport_stations_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "geo_site_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.test_should_parse_json_public_transport_stations_scenario", "name": "test_should_parse_json_public_transport_stations_scenario", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "geo_site_factory"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_public_transport_stations_scenario of TestConsolidatedStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_parse_json_public_transport_stops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.test_should_parse_json_public_transport_stops", "name": "test_should_parse_json_public_transport_stops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_public_transport_stops of TestConsolidatedStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestConsolidatedStudyJsonBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestJSONVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion", "name": "TestJSONVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestJSONVersion", "builtins.object"], "names": {".class": "SymbolTable", "test_from_string_invalid_format_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "invalid_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_invalid_format_should_raise", "name": "test_from_string_invalid_format_should_raise", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_invalid_format_should_raise", "name": "test_from_string_invalid_format_should_raise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_from_string_invalid_version_number_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "non_existent_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_invalid_version_number_should_raise", "name": "test_from_string_invalid_version_number_should_raise", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_invalid_version_number_should_raise", "name": "test_from_string_invalid_version_number_should_raise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_from_string_with_non_integer_version_number_should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "non_integer_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_with_non_integer_version_number_should_raise", "name": "test_from_string_with_non_integer_version_number_should_raise", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_from_string_with_non_integer_version_number_should_raise", "name": "test_from_string_with_non_integer_version_number_should_raise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_return_string_for_each_json_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "version_enum"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_should_return_string_for_each_json_version", "name": "test_should_return_string_for_each_json_version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.test_should_return_string_for_each_json_version", "name": "test_should_return_string_for_each_json_version", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestJSONVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestJSONVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestJsonPromoter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestJsonPromoter", "name": "TestJsonPromoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestJsonPromoter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestJsonPromoter", "builtins.object"], "names": {".class": "SymbolTable", "test_should_copy_dict_by_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestJsonPromoter.test_should_copy_dict_by_default", "name": "test_should_copy_dict_by_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestJsonPromoter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_copy_dict_by_default of TestJsonPromoter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_modify_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestJsonPromoter.test_should_modify_path", "name": "test_should_modify_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestJsonPromoter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_modify_path of TestJsonPromoter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestJsonPromoter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestJsonPromoter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestJsonStudyPromoter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestJsonStudyPromoter", "name": "TestJsonStudyPromoter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestJsonStudyPromoter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestJsonStudyPromoter", "builtins.object"], "names": {".class": "SymbolTable", "test_should_convert_values_and_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestJsonStudyPromoter.test_should_convert_values_and_version", "name": "test_should_convert_values_and_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestJsonStudyPromoter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_convert_values_and_version of TestJsonStudyPromoter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestJsonStudyPromoter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestJsonStudyPromoter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestRaiseIfJsonIsInvalid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid", "name": "TestRaiseIfJsonIsInvalid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid", "builtins.object"], "names": {".class": "SymbolTable", "test_should_raise_an_error_when_json_is_invalid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid.test_should_raise_an_error_when_json_is_invalid", "name": "test_should_raise_an_error_when_json_is_invalid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_raise_an_error_when_json_is_invalid of TestRaiseIfJsonIsInvalid", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestRaiseIfJsonIsInvalid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestStudyJsonBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", "name": "TestStudyJsonBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.tests.builders.test_json_builder", "mro": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", "builtins.object"], "names": {".class": "SymbolTable", "test_add_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_compute_poi", "consolidated_study_factory", "timed_poi_commute_factory", "point_of_interest_factory", "poi_scenario_factory", "bicycle_line", "public_transport_line", "timed_infrastructure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_add_poi_scenarios", "name": "test_add_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_compute_poi", "consolidated_study_factory", "timed_poi_commute_factory", "point_of_interest_factory", "poi_scenario_factory", "bicycle_line", "public_transport_line", "timed_infrastructure"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_add_poi_scenarios of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_add_poi_scenarios", "name": "test_add_poi_scenarios", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_add_poi_scenarios of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_normalize_json_study_version_format_should_use_proper_version_in_json_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_json_version", "expected_json_version", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_normalize_json_study_version_format_should_use_proper_version_in_json_study", "name": "test_normalize_json_study_version_format_should_use_proper_version_in_json_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_json_version", "expected_json_version", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_normalize_json_study_version_format_should_use_proper_version_in_json_study of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_normalize_json_study_version_format_should_use_proper_version_in_json_study", "name": "test_normalize_json_study_version_format_should_use_proper_version_in_json_study", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_should_build_consolidated_study_from_dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "consolidated_study", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_build_consolidated_study_from_dump", "name": "test_should_build_consolidated_study_from_dump", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "consolidated_study", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_build_consolidated_study_from_dump of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_parse_json_into_timed_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_critair_computer", "timed_infrastructure", "poi_scenario", "timed_study_factory", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_parse_json_into_timed_study", "name": "test_should_parse_json_into_timed_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_critair_computer", "timed_infrastructure", "poi_scenario", "timed_study_factory", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_json_into_timed_study of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_parse_json_into_timed_study", "name": "test_should_parse_json_into_timed_study", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_parse_json_into_timed_study of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_parse_version_v1_json_into_timed_study": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_critair_computer", "scenario_data", "geo_site_factory", "timed_study_factory", "tmp_path", "geo_employee_factory", "timed_commute_data_factory", "timed_infrastructure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_parse_version_v1_json_into_timed_study", "name": "test_should_parse_version_v1_json_into_timed_study", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "mock_critair_computer", "scenario_data", "geo_site_factory", "timed_study_factory", "tmp_path", "geo_employee_factory", "timed_commute_data_factory", "timed_infrastructure"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_parse_version_v1_json_into_timed_study of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_parse_version_v1_json_into_timed_study", "name": "test_should_parse_version_v1_json_into_timed_study", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "test_should_parse_version_v1_json_into_timed_study of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_should_raise_an_error_when_json_has_not_the_right_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_raise_an_error_when_json_has_not_the_right_format", "name": "test_should_raise_an_error_when_json_has_not_the_right_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_raise_an_error_when_json_has_not_the_right_format of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_raise_an_error_when_no_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_raise_an_error_when_no_version", "name": "test_should_raise_an_error_when_no_version", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_raise_an_error_when_no_version of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_should_raise_an_error_when_version_is_invalid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.test_should_raise_an_error_when_version_is_invalid", "name": "test_should_raise_an_error_when_version_is_invalid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tmp_path"], "arg_types": ["mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_should_raise_an_error_when_version_is_invalid of TestStudyJsonBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.tests.builders.test_json_builder.TestStudyJsonBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "TimedTransportStopCommute": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.TimedTransportStopCommute", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TransportType": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "WrongJsonFormatError": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.exceptions.WrongJsonFormatError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.tests.builders.test_json_builder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "seconds": {".class": "SymbolTableNode", "cross_ref": "mobility.quantity.seconds", "kind": "Gdef"}, "validate_latest_json_schema": {".class": "SymbolTableNode", "cross_ref": "mobility.builders.json_builder.validate_latest_json_schema", "kind": "Gdef"}}, "path": "mobility\\tests\\builders\\test_json_builder.py"}
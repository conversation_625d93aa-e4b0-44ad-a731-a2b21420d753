{"data_mtime": 1752154444, "dep_lines": [4, 7, 2, 1, 2, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["mobility.repositories.abstract_repositories", "mobility.use_cases.draw_mode_shift_readiness_statistics", "unittest.mock", "typing", "unittest", "builtins", "_frozen_importlib", "abc", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case"], "hash": "c0c46fdd1d95032eeb6bba4f832328f50dd1cb48", "id": "mobility.tests.use_cases.test_draw_mode_shift_readiness_statistics", "ignore_all": false, "interface_hash": "4a786933b6343632b20ac220fdb29079fd102df0", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_draw_mode_shift_readiness_statistics.py", "plugin_data": null, "size": 1547, "suppressed": [], "version_id": "1.16.1"}
{".class": "MypyFile", "_fullname": "mobility.ir.excel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Address": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.Address", "kind": "Gdef"}, "Distance": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.Distance", "kind": "Gdef"}, "Duration": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.Duration", "kind": "Gdef"}, "Emission": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.Emission", "kind": "Gdef"}, "Error": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.error.Error", "kind": "Gdef"}, "ExcelOutCommute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.excel.ExcelOutCommute", "name": "ExcelOutCommute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.excel.ExcelOutCommute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 100, "name": "employee_and_scenario_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 101, "name": "scenario_id", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 102, "name": "scenario_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 103, "name": "employee_id", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 104, "name": "employee_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 105, "name": "employee_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 106, "name": "employee_geo_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 107, "name": "transport_mode_supplied", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 108, "name": "transport_mode_selected", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 109, "name": "time_transport_mode_supplied", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 110, "name": "time_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 111, "name": "time_walk", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 112, "name": "time_pt", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 113, "name": "time_car", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 114, "name": "time_bicycle", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 115, "name": "site_id", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 116, "name": "site_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 117, "name": "site_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 118, "name": "site_geo_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 119, "name": "critical_cases", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 120, "name": "error_code", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 121, "name": "distance_transport_mode_supplied", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 122, "name": "distance_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 123, "name": "distance_walk", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 124, "name": "distance_pt", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 125, "name": "distance_car", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 126, "name": "distance_bicycle", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 127, "name": "emission_transport_mode_supplied", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 128, "name": "emission_yearly_transport_mode_supplied", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 129, "name": "emission_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 130, "name": "emission_yearly_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 131, "name": "emission_walk", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 132, "name": "emission_yearly_walk", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 133, "name": "emission_pt", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 134, "name": "emission_yearly_pt", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 135, "name": "emission_car", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 136, "name": "emission_yearly_car", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 137, "name": "emission_bicycle", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 138, "name": "emission_yearly_bicycle", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 139, "name": "bicycle_modal_shift_scenario_1", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 140, "name": "bicycle_modal_shift_scenario_2", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 141, "name": "pt_modal_shift_scenario_1", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 142, "name": "pt_modal_shift_scenario_2", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 143, "name": "remote_worker_scenario_1", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 144, "name": "remote_worker_scenario_2", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 145, "name": "remote_worker_scenario_3", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 146, "name": "remote_worker_scenario_4", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 147, "name": "time_8_00_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 148, "name": "time_9_00_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 149, "name": "time_9_30_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 150, "name": "time_10_00_transport_mode_selected", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 151, "name": "carpool_cluster_id", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 152, "name": "cluster_id", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 153, "name": "cluster_center_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 154, "name": "coworking_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 155, "name": "coworking_address", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 156, "name": "coworking_transport_mode", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 157, "name": "coworking_time", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 158, "name": "coworking_emission", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 159, "name": "coworking_yearly_emission", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 160, "name": "coworking_distance", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "mobility.ir.excel", "mro": ["mobility.ir.excel.ExcelOutCommute", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "employee_and_scenario_id", "scenario_id", "scenario_name", "employee_id", "employee_name", "employee_address", "employee_geo_address", "transport_mode_supplied", "transport_mode_selected", "time_transport_mode_supplied", "time_transport_mode_selected", "time_walk", "time_pt", "time_car", "time_bicycle", "site_id", "site_name", "site_address", "site_geo_address", "critical_cases", "error_code", "distance_transport_mode_supplied", "distance_transport_mode_selected", "distance_walk", "distance_pt", "distance_car", "distance_bicycle", "emission_transport_mode_supplied", "emission_yearly_transport_mode_supplied", "emission_transport_mode_selected", "emission_yearly_transport_mode_selected", "emission_walk", "emission_yearly_walk", "emission_pt", "emission_yearly_pt", "emission_car", "emission_yearly_car", "emission_bicycle", "emission_yearly_bicycle", "bicycle_modal_shift_scenario_1", "bicycle_modal_shift_scenario_2", "pt_modal_shift_scenario_1", "pt_modal_shift_scenario_2", "remote_worker_scenario_1", "remote_worker_scenario_2", "remote_worker_scenario_3", "remote_worker_scenario_4", "time_8_00_transport_mode_selected", "time_9_00_transport_mode_selected", "time_9_30_transport_mode_selected", "time_10_00_transport_mode_selected", "carpool_cluster_id", "cluster_id", "cluster_center_address", "coworking_name", "coworking_address", "coworking_transport_mode", "coworking_time", "coworking_emission", "coworking_yearly_emission", "coworking_distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel.ExcelOutCommute.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "employee_and_scenario_id", "scenario_id", "scenario_name", "employee_id", "employee_name", "employee_address", "employee_geo_address", "transport_mode_supplied", "transport_mode_selected", "time_transport_mode_supplied", "time_transport_mode_selected", "time_walk", "time_pt", "time_car", "time_bicycle", "site_id", "site_name", "site_address", "site_geo_address", "critical_cases", "error_code", "distance_transport_mode_supplied", "distance_transport_mode_selected", "distance_walk", "distance_pt", "distance_car", "distance_bicycle", "emission_transport_mode_supplied", "emission_yearly_transport_mode_supplied", "emission_transport_mode_selected", "emission_yearly_transport_mode_selected", "emission_walk", "emission_yearly_walk", "emission_pt", "emission_yearly_pt", "emission_car", "emission_yearly_car", "emission_bicycle", "emission_yearly_bicycle", "bicycle_modal_shift_scenario_1", "bicycle_modal_shift_scenario_2", "pt_modal_shift_scenario_1", "pt_modal_shift_scenario_2", "remote_worker_scenario_1", "remote_worker_scenario_2", "remote_worker_scenario_3", "remote_worker_scenario_4", "time_8_00_transport_mode_selected", "time_9_00_transport_mode_selected", "time_9_30_transport_mode_selected", "time_10_00_transport_mode_selected", "carpool_cluster_id", "cluster_id", "cluster_center_address", "coworking_name", "coworking_address", "coworking_transport_mode", "coworking_time", "coworking_emission", "coworking_yearly_emission", "coworking_distance"], "arg_types": ["mobility.ir.excel.ExcelOutCommute", "builtins.str", "builtins.int", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ExcelOutCommute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "mobility.ir.excel.ExcelOutCommute.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "employee_and_scenario_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scenario_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scenario_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employee_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employee_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employee_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "employee_geo_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transport_mode_supplied"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_transport_mode_supplied"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_walk"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_pt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_car"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_bicycle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "site_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "site_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "site_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "site_geo_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "critical_cases"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error_code"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_transport_mode_supplied"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_walk"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_pt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_car"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distance_bicycle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_transport_mode_supplied"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_transport_mode_supplied"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_walk"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_walk"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_pt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_pt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_car"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_car"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_bicycle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "emission_yearly_bicycle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_modal_shift_scenario_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bicycle_modal_shift_scenario_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pt_modal_shift_scenario_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pt_modal_shift_scenario_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_worker_scenario_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_worker_scenario_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_worker_scenario_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remote_worker_scenario_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_8_00_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_9_00_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_9_30_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_10_00_transport_mode_selected"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carpool_cluster_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cluster_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cluster_center_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_transport_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_yearly_emission"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coworking_distance"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["employee_and_scenario_id", "scenario_id", "scenario_name", "employee_id", "employee_name", "employee_address", "employee_geo_address", "transport_mode_supplied", "transport_mode_selected", "time_transport_mode_supplied", "time_transport_mode_selected", "time_walk", "time_pt", "time_car", "time_bicycle", "site_id", "site_name", "site_address", "site_geo_address", "critical_cases", "error_code", "distance_transport_mode_supplied", "distance_transport_mode_selected", "distance_walk", "distance_pt", "distance_car", "distance_bicycle", "emission_transport_mode_supplied", "emission_yearly_transport_mode_supplied", "emission_transport_mode_selected", "emission_yearly_transport_mode_selected", "emission_walk", "emission_yearly_walk", "emission_pt", "emission_yearly_pt", "emission_car", "emission_yearly_car", "emission_bicycle", "emission_yearly_bicycle", "bicycle_modal_shift_scenario_1", "bicycle_modal_shift_scenario_2", "pt_modal_shift_scenario_1", "pt_modal_shift_scenario_2", "remote_worker_scenario_1", "remote_worker_scenario_2", "remote_worker_scenario_3", "remote_worker_scenario_4", "time_8_00_transport_mode_selected", "time_9_00_transport_mode_selected", "time_9_30_transport_mode_selected", "time_10_00_transport_mode_selected", "carpool_cluster_id", "cluster_id", "cluster_center_address", "coworking_name", "coworking_address", "coworking_transport_mode", "coworking_time", "coworking_emission", "coworking_yearly_emission", "coworking_distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.excel.ExcelOutCommute.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["employee_and_scenario_id", "scenario_id", "scenario_name", "employee_id", "employee_name", "employee_address", "employee_geo_address", "transport_mode_supplied", "transport_mode_selected", "time_transport_mode_supplied", "time_transport_mode_selected", "time_walk", "time_pt", "time_car", "time_bicycle", "site_id", "site_name", "site_address", "site_geo_address", "critical_cases", "error_code", "distance_transport_mode_supplied", "distance_transport_mode_selected", "distance_walk", "distance_pt", "distance_car", "distance_bicycle", "emission_transport_mode_supplied", "emission_yearly_transport_mode_supplied", "emission_transport_mode_selected", "emission_yearly_transport_mode_selected", "emission_walk", "emission_yearly_walk", "emission_pt", "emission_yearly_pt", "emission_car", "emission_yearly_car", "emission_bicycle", "emission_yearly_bicycle", "bicycle_modal_shift_scenario_1", "bicycle_modal_shift_scenario_2", "pt_modal_shift_scenario_1", "pt_modal_shift_scenario_2", "remote_worker_scenario_1", "remote_worker_scenario_2", "remote_worker_scenario_3", "remote_worker_scenario_4", "time_8_00_transport_mode_selected", "time_9_00_transport_mode_selected", "time_9_30_transport_mode_selected", "time_10_00_transport_mode_selected", "carpool_cluster_id", "cluster_id", "cluster_center_address", "coworking_name", "coworking_address", "coworking_transport_mode", "coworking_time", "coworking_emission", "coworking_yearly_emission", "coworking_distance"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExcelOutCommute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["employee_and_scenario_id", "scenario_id", "scenario_name", "employee_id", "employee_name", "employee_address", "employee_geo_address", "transport_mode_supplied", "transport_mode_selected", "time_transport_mode_supplied", "time_transport_mode_selected", "time_walk", "time_pt", "time_car", "time_bicycle", "site_id", "site_name", "site_address", "site_geo_address", "critical_cases", "error_code", "distance_transport_mode_supplied", "distance_transport_mode_selected", "distance_walk", "distance_pt", "distance_car", "distance_bicycle", "emission_transport_mode_supplied", "emission_yearly_transport_mode_supplied", "emission_transport_mode_selected", "emission_yearly_transport_mode_selected", "emission_walk", "emission_yearly_walk", "emission_pt", "emission_yearly_pt", "emission_car", "emission_yearly_car", "emission_bicycle", "emission_yearly_bicycle", "bicycle_modal_shift_scenario_1", "bicycle_modal_shift_scenario_2", "pt_modal_shift_scenario_1", "pt_modal_shift_scenario_2", "remote_worker_scenario_1", "remote_worker_scenario_2", "remote_worker_scenario_3", "remote_worker_scenario_4", "time_8_00_transport_mode_selected", "time_9_00_transport_mode_selected", "time_9_30_transport_mode_selected", "time_10_00_transport_mode_selected", "carpool_cluster_id", "cluster_id", "cluster_center_address", "coworking_name", "coworking_address", "coworking_transport_mode", "coworking_time", "coworking_emission", "coworking_yearly_emission", "coworking_distance"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExcelOutCommute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bicycle_modal_shift_scenario_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.bicycle_modal_shift_scenario_1", "name": "bicycle_modal_shift_scenario_1", "setter_type": null, "type": "builtins.str"}}, "bicycle_modal_shift_scenario_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.bicycle_modal_shift_scenario_2", "name": "bicycle_modal_shift_scenario_2", "setter_type": null, "type": "builtins.str"}}, "carpool_cluster_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.carpool_cluster_id", "name": "carpool_cluster_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "cluster_center_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.cluster_center_address", "name": "cluster_center_address", "setter_type": null, "type": "builtins.str"}}, "cluster_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.cluster_id", "name": "cluster_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "coworking_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_address", "name": "coworking_address", "setter_type": null, "type": "builtins.str"}}, "coworking_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_distance", "name": "coworking_distance", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "coworking_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_emission", "name": "coworking_emission", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "coworking_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_name", "name": "coworking_name", "setter_type": null, "type": "builtins.str"}}, "coworking_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_time", "name": "coworking_time", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "coworking_transport_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_transport_mode", "name": "coworking_transport_mode", "setter_type": null, "type": "builtins.str"}}, "coworking_yearly_emission": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.coworking_yearly_emission", "name": "coworking_yearly_emission", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "critical_cases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.critical_cases", "name": "critical_cases", "setter_type": null, "type": "builtins.int"}}, "distance_bicycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_bicycle", "name": "distance_bicycle", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "distance_car": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_car", "name": "distance_car", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "distance_pt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_pt", "name": "distance_pt", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "distance_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_transport_mode_selected", "name": "distance_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "distance_transport_mode_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_transport_mode_supplied", "name": "distance_transport_mode_supplied", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "distance_walk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.distance_walk", "name": "distance_walk", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_bicycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_bicycle", "name": "emission_bicycle", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_car": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_car", "name": "emission_car", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_pt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_pt", "name": "emission_pt", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_transport_mode_selected", "name": "emission_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_transport_mode_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_transport_mode_supplied", "name": "emission_transport_mode_supplied", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_walk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_walk", "name": "emission_walk", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_bicycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_bicycle", "name": "emission_yearly_bicycle", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_car": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_car", "name": "emission_yearly_car", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_pt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_pt", "name": "emission_yearly_pt", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_transport_mode_selected", "name": "emission_yearly_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_transport_mode_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_transport_mode_supplied", "name": "emission_yearly_transport_mode_supplied", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "emission_yearly_walk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.emission_yearly_walk", "name": "emission_yearly_walk", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}}}, "employee_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.employee_address", "name": "employee_address", "setter_type": null, "type": "builtins.str"}}, "employee_and_scenario_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.employee_and_scenario_id", "name": "employee_and_scenario_id", "setter_type": null, "type": "builtins.str"}}, "employee_geo_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.employee_geo_address", "name": "employee_geo_address", "setter_type": null, "type": "builtins.str"}}, "employee_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.employee_id", "name": "employee_id", "setter_type": null, "type": "builtins.int"}}, "employee_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.employee_name", "name": "employee_name", "setter_type": null, "type": "builtins.str"}}, "error_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.error_code", "name": "error_code", "setter_type": null, "type": "builtins.int"}}, "from_geocode_failed_both": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_both", "name": "from_geocode_failed_both", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_both of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_both", "name": "from_geocode_failed_both", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_both of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_geocode_failed_employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_employee", "name": "from_geocode_failed_employee", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_employee of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_employee", "name": "from_geocode_failed_employee", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.FailedGeoEmployee", "mobility.ir.site.GeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_employee of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_geocode_failed_site": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_site", "name": "from_geocode_failed_site", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_site of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_geocode_failed_site", "name": "from_geocode_failed_site", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.FailedGeoSite", {".class": "NoneType"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_geocode_failed_site of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_modal_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data", "critical_case", "indicators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_modal_commute", "name": "from_modal_commute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data", "critical_case", "indicators"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData", "builtins.int", "mobility.ir.indicators.indicators.Indicators"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_modal_commute of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_modal_commute", "name": "from_modal_commute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data", "critical_case", "indicators"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData", "builtins.int", "mobility.ir.indicators.indicators.Indicators"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_modal_commute of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_time_failed_commute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_time_failed_commute", "name": "from_time_failed_commute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_time_failed_commute of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.from_time_failed_commute", "name": "from_time_failed_commute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "scenario_data", "employee", "site", "commute_data"], "arg_types": [{".class": "TypeType", "item": "mobility.ir.excel.ExcelOutCommute"}, "mobility.ir.scenario_data.ScenarioData", "mobility.ir.employee.GeoEmployee", "mobility.ir.site.GeoSite", "mobility.ir.commute_data.ModalCommuteData"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_time_failed_commute of ExcelOutCommute", "ret_type": "mobility.ir.excel.ExcelOutCommute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_commute_alternative_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["commute_data", "employee", "hour", "minute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mobility.ir.excel.ExcelOutCommute.get_commute_alternative_duration", "name": "get_commute_alternative_duration", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["commute_data", "employee", "hour", "minute"], "arg_types": ["mobility.ir.commute_data.ModalCommuteData", "mobility.ir.employee.GeoEmployee", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_commute_alternative_duration of ExcelOutCommute", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mobility.ir.excel.ExcelOutCommute.get_commute_alternative_duration", "name": "get_commute_alternative_duration", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["commute_data", "employee", "hour", "minute"], "arg_types": ["mobility.ir.commute_data.ModalCommuteData", "mobility.ir.employee.GeoEmployee", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_commute_alternative_duration of ExcelOutCommute", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mobility.funky.ImmutableDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pt_modal_shift_scenario_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.pt_modal_shift_scenario_1", "name": "pt_modal_shift_scenario_1", "setter_type": null, "type": "builtins.str"}}, "pt_modal_shift_scenario_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.pt_modal_shift_scenario_2", "name": "pt_modal_shift_scenario_2", "setter_type": null, "type": "builtins.str"}}, "remote_worker_scenario_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.remote_worker_scenario_1", "name": "remote_worker_scenario_1", "setter_type": null, "type": "builtins.str"}}, "remote_worker_scenario_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.remote_worker_scenario_2", "name": "remote_worker_scenario_2", "setter_type": null, "type": "builtins.str"}}, "remote_worker_scenario_3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.remote_worker_scenario_3", "name": "remote_worker_scenario_3", "setter_type": null, "type": "builtins.str"}}, "remote_worker_scenario_4": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.remote_worker_scenario_4", "name": "remote_worker_scenario_4", "setter_type": null, "type": "builtins.str"}}, "scenario_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.scenario_id", "name": "scenario_id", "setter_type": null, "type": "builtins.int"}}, "scenario_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.scenario_name", "name": "scenario_name", "setter_type": null, "type": "builtins.str"}}, "site_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.site_address", "name": "site_address", "setter_type": null, "type": "builtins.str"}}, "site_geo_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.site_geo_address", "name": "site_geo_address", "setter_type": null, "type": "builtins.str"}}, "site_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.site_id", "name": "site_id", "setter_type": null, "type": "builtins.int"}}, "site_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.site_name", "name": "site_name", "setter_type": null, "type": "builtins.str"}}, "time_10_00_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_10_00_transport_mode_selected", "name": "time_10_00_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_8_00_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_8_00_transport_mode_selected", "name": "time_8_00_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_9_00_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_9_00_transport_mode_selected", "name": "time_9_00_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_9_30_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_9_30_transport_mode_selected", "name": "time_9_30_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_bicycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_bicycle", "name": "time_bicycle", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_car": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_car", "name": "time_car", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_pt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_pt", "name": "time_pt", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_transport_mode_selected", "name": "time_transport_mode_selected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_transport_mode_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_transport_mode_supplied", "name": "time_transport_mode_supplied", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "time_walk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.time_walk", "name": "time_walk", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}}}, "transport_mode_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.transport_mode_selected", "name": "transport_mode_selected", "setter_type": null, "type": "builtins.str"}}, "transport_mode_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "mobility.ir.excel.ExcelOutCommute.transport_mode_supplied", "name": "transport_mode_supplied", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.excel.ExcelOutCommute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.excel.ExcelOutCommute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FailedGeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.FailedGeoEmployee", "kind": "Gdef"}, "FailedGeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.FailedGeoSite", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "mobility.funky.ImmutableDict", "kind": "Gdef"}, "Indicators": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.indicators.indicators.Indicators", "kind": "Gdef"}, "ModalCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.ModalCommuteData", "kind": "Gdef"}, "NOT_APPLICABLE": {".class": "SymbolTableNode", "cross_ref": "mobility.constants.NOT_APPLICABLE", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.excel.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_serialize_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_address", "name": "_serialize_address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", "mobility.ir.geo_study.Address"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_address", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_cluster_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cluster_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_cluster_id", "name": "_serialize_cluster_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cluster_id"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_cluster_id", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_complete_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_complete_address", "name": "_serialize_complete_address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", "mobility.ir.geo_study.Address"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_complete_address", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_distance", "name": "_serialize_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "distance"], "arg_types": [{".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.transport.Distance"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_distance", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_duration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_duration", "name": "_serialize_duration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "duration"], "arg_types": [{".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.transport.Duration"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_duration", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_emission": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "emission"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_emission", "name": "_serialize_emission", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "emission"], "arg_types": [{".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.transport.Emission"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_emission", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_error", "name": "_serialize_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["error"], "arg_types": ["mobility.ir.error.Error"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_error", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_scenario_belonging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["belong_to_scenario"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_scenario_belonging", "name": "_serialize_scenario_belonging", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["belong_to_scenario"], "arg_types": ["builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_scenario_belonging", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_transport_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_transport_mode", "name": "_serialize_transport_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_transport_mode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_yearly_emission": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mode", "emission"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.excel._serialize_yearly_emission", "name": "_serialize_yearly_emission", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mode", "emission"], "arg_types": [{".class": "UnionType", "items": ["mobility.ir.transport.TransportMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.transport.Emission"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_serialize_yearly_emission", "ret_type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_carbon_emission_gec_per_commute_to_teq_per_year": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.indicators.scenario_indicators.convert_carbon_emission_gec_per_commute_to_teq_per_year", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}}, "path": "mobility\\ir\\excel.py"}
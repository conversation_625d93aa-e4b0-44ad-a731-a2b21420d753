{".class": "MypyFile", "_fullname": "mobility.ir.indicators.study_indicators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyCommuteData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.indicators.study_indicators.AnyCommuteData", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["mobility.ir.commute_data.ModalCommuteData", "mobility.ir.commute_data.TimedCommuteData", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ConsolidatedStudy": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.ConsolidatedStudy", "kind": "Gdef"}, "EmployeeWithId": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.indicators.study_indicators.EmployeeWithId", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["mobility.ir.employee.GeoEmployee", "mobility.ir.employee.FailedGeoEmployee"], "uses_pep604_syntax": false}}}, "FailedGeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.FailedGeoEmployee", "kind": "Gdef"}, "FailedGeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.FailedGeoSite", "kind": "Gdef"}, "GeoEmployee": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.employee.GeoEmployee", "kind": "Gdef"}, "GeoSite": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.site.GeoSite", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModalCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.ModalCommuteData", "kind": "Gdef"}, "PoiScenario": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study.PoiScenario", "kind": "Gdef"}, "PointOfInterest": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.poi.PointOfInterest", "kind": "Gdef"}, "ScenarioData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.scenario_data.ScenarioData", "kind": "Gdef"}, "Scenarios": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.study_types.Scenarios", "kind": "Gdef"}, "ScenariosWithId": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.indicators.study_indicators.ScenariosWithId", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["mobility.ir.scenario_data.ScenarioData", {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.indicators.study_indicators.EmployeeWithId"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.indicators.study_indicators.SiteWithId"}, {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.indicators.study_indicators.AnyCommuteData"}], "extra_attrs": null, "type_ref": "mobility.ir.study_types.Scenarios"}}}, "SiteWithId": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mobility.ir.indicators.study_indicators.SiteWithId", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["mobility.ir.site.GeoSite", "mobility.ir.site.FailedGeoSite"], "uses_pep604_syntax": false}}}, "StudyIndicators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators", "name": "StudyIndicators", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mobility.ir.indicators.study_indicators", "mro": ["mobility.ir.indicators.study_indicators.StudyIndicators", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "consolidated_study"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "consolidated_study"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators", "mobility.ir.study.ConsolidatedStudy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StudyIndicators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "company": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.company", "name": "company", "setter_type": null, "type": "builtins.str"}}, "consolidated_study": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.consolidated_study", "name": "consolidated_study", "setter_type": null, "type": "mobility.ir.study.ConsolidatedStudy"}}, "construct_nb_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.construct_nb_employees", "name": "construct_nb_employees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_nb_employees of StudyIndicators", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_nb_failed_geoloc_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.construct_nb_failed_geoloc_employees", "name": "construct_nb_failed_geoloc_employees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_nb_failed_geoloc_employees of StudyIndicators", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_nb_failed_times_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.construct_nb_failed_times_employees", "name": "construct_nb_failed_times_employees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_nb_failed_times_employees of StudyIndicators", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_nb_success_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.construct_nb_success_employees", "name": "construct_nb_success_employees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct_nb_success_employees of StudyIndicators", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_alternative_poi_scenarios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.get_alternative_poi_scenarios", "name": "get_alternative_poi_scenarios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_alternative_poi_scenarios of StudyIndicators", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenario"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_nb_sites": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.get_nb_sites", "name": "get_nb_sites", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_nb_sites of StudyIndicators", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pois": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.get_pois", "name": "get_pois", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_pois of StudyIndicators", "ret_type": {".class": "Instance", "args": ["mobility.ir.poi.PointOfInterest"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_present_poi_scenario": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.get_present_poi_scenario", "name": "get_present_poi_scenario", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mobility.ir.indicators.study_indicators.StudyIndicators"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_present_poi_scenario of StudyIndicators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mobility.ir.study.PoiScenario"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nb_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.nb_employees", "name": "nb_employees", "setter_type": null, "type": "builtins.int"}}, "nb_failed_geoloc_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.nb_failed_geoloc_employees", "name": "nb_failed_geoloc_employees", "setter_type": null, "type": "builtins.int"}}, "nb_failed_times_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.nb_failed_times_employees", "name": "nb_failed_times_employees", "setter_type": null, "type": "builtins.int"}}, "nb_success_employees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.nb_success_employees", "name": "nb_success_employees", "setter_type": null, "type": "builtins.int"}}, "territory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.territory", "name": "territory", "setter_type": null, "type": "mobility.ir.territory.Territory"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mobility.ir.indicators.study_indicators.StudyIndicators.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mobility.ir.indicators.study_indicators.StudyIndicators", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimedCommuteData": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.commute_data.TimedCommuteData", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mobility.ir.indicators.study_indicators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "shorten_nickname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nickname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mobility.ir.indicators.study_indicators.shorten_nickname", "name": "shorten_nickname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["nickname"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shorten_nickname", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "mobility\\ir\\indicators\\study_indicators.py"}
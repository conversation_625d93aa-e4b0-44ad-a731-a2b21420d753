import json
from collections import defaultdict, namedtuple
from typing import Callable, Dict, Iterable, List, Optional, Tuple

import numpy

from mobility.builders.territory_database import TerritoryDatabase
from mobility.constants import DEFAULT_MODE_WHEN_NO_AVAILABLE_DURATIONS
from mobility.funky import ImmutableDict, discretize_repartition
from mobility.ir.geo_study import GeoCoordinates, Localised
from mobility.ir.mode_constraints import (
    Constraints,
    DurationConstraint,
    DurationConstraints,
)
from mobility.ir.mode_share_rules import TransportModalShareRules
from mobility.ir.study import (
    GeoEmployee,
    GeoSite,
    ModalCommuteData,
    ModalStudy,
    ScenarioData,
    TimedCommuteData,
    TimedScenario,
    TimedStudy,
)
from mobility.ir.study_types import Commute
from mobility.ir.transport import Duration, TransportMode
from mobility.modal_shift import ModalShift
from mobility.repositories.abstract_repositories import AbstractTerritoriesRepository
from mobility.workers.distance_computer import compute_distance


class TransportModeAssigner:
    def compute_employees_mode_in_scenario(
        self, scenario: TimedScenario, constraints: Constraints
    ) -> Dict[GeoEmployee, TransportMode]:
        employees_modes: Dict[GeoEmployee, TransportMode] = {}
        for employee, commutes in scenario.get_origins().items():
            if employee.transport_mode is None:
                if len(commutes) > 1:
                    raise ValueError(
                        f"More than one commute for employee {employee}:\n"
                        "  {commutes}"
                        f"in scenario {scenario}"
                    )
                commute, *_ = commutes
                _, data = commute
                filtered_duration = self._filter_unacceptable_modes(
                    data.duration, constraints
                )
                employees_modes[employee] = self.guess_best_mode(filtered_duration)
            else:
                employees_modes[employee] = employee.transport_mode
        return employees_modes

    def _filter_unacceptable_modes(
        self, duration: Duration, constraints: Constraints
    ) -> Duration:
        filtered_duration = Duration(
            {
                mode: time
                for mode, time in duration.items()
                if constraints.duration.check_mode_duration_against_constraint(
                    mode, time
                )
            }
        )
        if TransportMode.WALK in filtered_duration:
            print(
                f"Warning: Walk mode is available for {len(filtered_duration)} employees."
            )

        return filtered_duration

    def guess_best_mode(self, duration: Duration) -> TransportMode:
        raise NotImplementedError


class FastestModeAssigner(TransportModeAssigner):
    def guess_best_mode(self, duration: Duration) -> TransportMode:
        if len(duration) == 0:
            return DEFAULT_MODE_WHEN_NO_AVAILABLE_DURATIONS
        return min(duration.keys(), key=lambda mode: duration[mode])


class TravelScoreComputer(TransportModeAssigner):
    score_model_params = namedtuple(
        "score_model_params",
        ["slope", "zero_time_threshold", "step", "delta_step", "step_slope"],
    )

    def __init__(self) -> None:
        self.mode_score_params = {
            TransportMode.WALK: self.score_model_params(
                6.997799464366554,
                -57.98432314500623,
                -98.41313409693642,
                40.56792503729574,
                5.154114460912312,
            ),
            TransportMode.BICYCLE: self.score_model_params(
                3.747621483322363,
                -1.79858661617483,
                62.68117856715531,
                -33.85551744321653,
                8.303877530548792,
            ),
            TransportMode.CAR: self.score_model_params(
                3.9548585672447416,
                25.044601350749797,
                81.01399471084007,
                33.57636321837144,
                -37.42291566596743,
            ),
            TransportMode.PUBLIC_TRANSPORT: self.score_model_params(
                3.7632560752271975,
                -20.93038106928186,
                26.213789177574462,
                -2.4182180768200485,
                53.60765151412823,
            ),
        }
        self._reset_computers()

    def _reset_computers(self) -> None:
        self.mode_score_computer = {
            mode: self._setup_score_computer(
                params.slope,
                params.zero_time_threshold,
                params.step,
                params.delta_step,
                params.step_slope,
            )
            for mode, params in self.mode_score_params.items()
        }

    def _setup_score_computer(
        self,
        slope: float,
        zero_time_threshold: float,
        step: float,
        delta_step: float,
        step_slope: float,
    ) -> Callable[[float], float]:
        def score_computer(time: float) -> float:
            return (
                zero_time_threshold
                + slope * time
                + step * (1.0 / (1.0 + numpy.exp((delta_step - time) / step_slope)))
            )

        return score_computer

    def _travel_score(self, mode: TransportMode, time: float) -> float:
        return self.mode_score_computer[mode](time)

    def guess_best_mode(self, duration: Duration) -> TransportMode:
        if len(duration) == 0:
            return DEFAULT_MODE_WHEN_NO_AVAILABLE_DURATIONS
        return min(
            duration, key=lambda mode: self._travel_score(mode, duration[mode] / 60.0)
        )


class StatisticsModeAssigner(TransportModeAssigner):
    def __init__(
        self,
        get_mode_repartition: Callable[[str, str], Dict[TransportMode, float]],
    ) -> None:
        self.get_mode_repartition = get_mode_repartition
        self.default_assigner = FastestModeAssigner()

    def compute_employees_mode_in_scenario(
        self, scenario: TimedScenario, constraints: Constraints
    ) -> Dict[GeoEmployee, TransportMode]:
        grouped_commutes = self.group_commutes_by_citycode(scenario.commutes)
        employees_commutes = self.get_employees_commutes(scenario.commutes)
        all_modes = {}
        for city_codes, employees in grouped_commutes.items():
            mode_repartition = self.get_mode_repartition(*city_codes)
            group_modes = self.find_employees_most_probable_mode(
                employees,
                mode_repartition,
                employees_commutes,
                constraints,
            )
            for employee in employees:
                if employee.transport_mode is None:
                    all_modes[employee] = group_modes[employee]
                else:
                    all_modes[employee] = employee.transport_mode
        return all_modes

    def find_employees_most_probable_mode(
        self,
        employees: List[GeoEmployee],
        modes_repartition: Dict[TransportMode, float],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
        constraints: Constraints,
    ) -> Dict[GeoEmployee, TransportMode]:
        statistics_population = sum(modes_repartition.values())
        if statistics_population < 30.0:
            modes_repartition = {
                TransportMode.WALK: 1.0,
                TransportMode.BICYCLE: 1.0,
                TransportMode.CAR: 80.0,
                TransportMode.PUBLIC_TRANSPORT: 20.0,
            }
            statistics_population = sum(modes_repartition.values())
        nb_employees = len(employees)
        target_population_per_mode = {
            mode: round(stat * nb_employees / statistics_population)
            for mode, stat in modes_repartition.items()
        }
        employees_mode = {}
        for mode in [
            TransportMode.WALK,
            TransportMode.BICYCLE,
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.CAR,
        ]:
            preferred_employees_for_mode = sorted(
                [
                    e
                    for e in employees
                    if e not in employees_mode
                    and mode in employees_commutes[e].duration
                    and (
                        constraints.duration.check_mode_duration_against_constraint(
                            mode, employees_commutes[e].duration[mode]
                        )
                    )
                ],
                key=lambda e: employees_commutes[e].duration[mode],
            )
            target_number = target_population_per_mode.get(mode, 0)
            for employee in preferred_employees_for_mode[:target_number]:
                employees_mode[employee] = mode
        remaining_employees = [e for e in employees if e not in employees_mode]
        remaining_modes = self.compute_default_modes(
            remaining_employees, employees_commutes
        )
        for employee, mode in remaining_modes.items():
            employees_mode[employee] = mode
        return employees_mode

    def compute_default_modes(
        self,
        employees: List[GeoEmployee],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
    ) -> Dict[GeoEmployee, TransportMode]:
        employees_mode = {}
        for employee in employees:
            data = employees_commutes[employee]
            employees_mode[employee] = self.default_assigner.guess_best_mode(
                data.duration
            )
        return employees_mode

    def group_commutes_by_citycode(
        self, commutes: Iterable[Commute[GeoEmployee, GeoSite, TimedCommuteData]]
    ) -> Dict[Tuple[str, str], List[GeoEmployee]]:
        groups: Dict[Tuple[str, str], List[GeoEmployee]] = defaultdict(list)
        for commute in commutes:
            employee_citycode = commute.origin.address_details.citycode
            site_citycode = commute.destination.address_details.citycode
            groups[(employee_citycode, site_citycode)].append(commute.origin)
        return groups

    def get_employees_commutes(
        self, commutes: Iterable[Commute[GeoEmployee, GeoSite, TimedCommuteData]]
    ) -> Dict[GeoEmployee, TimedCommuteData]:
        employees_commute = {}
        for commute in commutes:
            if commute.origin not in employees_commute:
                employees_commute[commute.origin] = commute.data
            else:
                raise ValueError(
                    f"Several commutes for a single employee {commute.origin}.\n"
                    f"Recorded commute data {employees_commute[commute.origin]};\n"
                    f"Finding other commute {commute}."
                )
        return employees_commute


ODModeMatrix = Dict[str, Dict[str, Dict[TransportMode, int]]]


class FormStatsModeAssigner(TransportModeAssigner):
    """
    od_matrix: represents stats e.g. coming from a form of number of people in
    the study using a specific transport mode. It is represented as a mapping
    from employee address postcode to destination (site) id to the number of
    people by mode:
    {
        <origin_post_code>: {
            <site_id>: {
                <transport_mode>: <nb_people>
            }
        }
    }

    For example:
    {
        "69001": {
            2: {
                TransportMode.BICYCLE: 12,
                TransportMode.CAR: 2,
            },
            1: {
                TransportMode.PUBLIC_TRANSPORT: 1,
            }
        }
    }
    This matrix is expected to be incomplete and inaccurate i.e. :
    - incomplete : the number of persons recorded for an OD (postcode, site_id)
    may be lower than the number of people for that OD in the study
    - inaccurate : the number of persons recorded for an OD (postcode, site_id)
    may be higher than the number of people for that OD in the study

    postcode_coordinates: gives a representative point for each postcode present
    in the od_matrix mapping. This point is used to determine the distance between
    two postcodes.
    """

    def __init__(
        self,
        od_matrix: ODModeMatrix,
        postcode_coordinates_getter: Callable[[str], Optional[GeoCoordinates]],
    ) -> None:
        self.od_matrix = od_matrix
        self.postcode_coordinates_getter = postcode_coordinates_getter
        self.default_assigner = FastestModeAssigner()
        self.ordered_modes = [
            TransportMode.WALK,
            TransportMode.BICYCLE,
            TransportMode.ELECTRIC_BICYCLE,
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.CAR,
            TransportMode.ELECTRIC_CAR,
            TransportMode.MOTORCYCLE,
            TransportMode.CARPOOLING,
            TransportMode.ELECTRIC_MOTORCYCLE,
            TransportMode.FAST_BICYCLE,
            TransportMode.CAR_PUBLIC_TRANSPORT,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT,
            TransportMode.AIRPLANE,
        ]

    def compute_global_mode_repartition(
        self, nb_modes: int
    ) -> Dict[TransportMode, int]:
        global_observed_modes = {m: 0 for m in TransportMode}
        for mode_by_pc in self.od_matrix.values():
            for mode_by_site in mode_by_pc.values():
                for mode, nb in mode_by_site.items():
                    global_observed_modes[mode] += nb
        if sum(global_observed_modes.values()) <= 0:
            return global_observed_modes
        global_observed_modes = self.discretize_mode_repartition(
            global_observed_modes, nb_modes
        )
        return global_observed_modes

    def get_raw_observed_repartition(
        self,
        postcode: str,
        site: GeoSite,
    ) -> Dict[TransportMode, int]:
        if postcode in self.od_matrix and site.nickname in self.od_matrix[postcode]:
            return {
                mode: self.od_matrix[postcode][site.nickname].get(mode, 0)
                for mode in TransportMode
            }
        else:
            return {mode: 0 for mode in TransportMode}

    def get_observed_repartition_scaled_down_on_workforce(
        self, postcode: str, site: GeoSite, workforce: int
    ) -> Tuple[Dict[TransportMode, int], Dict[TransportMode, int]]:
        observed_mode_repartition = self.get_raw_observed_repartition(postcode, site)
        observed_population = sum(observed_mode_repartition.values())
        if observed_population > workforce:
            scaled_down_observed_mode_repartition = self.discretize_mode_repartition(
                observed_mode_repartition, workforce
            )
            leftovers = {
                mode: observed_mode_repartition[mode]
                - scaled_down_observed_mode_repartition[mode]
                for mode in TransportMode
            }
        else:
            scaled_down_observed_mode_repartition = observed_mode_repartition
            leftovers = {mode: 0 for mode in TransportMode}
        return scaled_down_observed_mode_repartition, leftovers

    def compute_closest_postcodes_in_od_matrix(self, postcode: str) -> List[str]:
        available_postcodes = list(self.od_matrix)
        origin_coords = self.postcode_coordinates_getter(postcode)
        if origin_coords is None:
            return []
        distances = {}
        computed_postcodes = []
        for pc in available_postcodes:
            destination_coords = self.postcode_coordinates_getter(pc)
            if destination_coords is None:
                continue
            distances[pc] = compute_distance(
                origin=origin_coords,
                destination=destination_coords,
            )
            computed_postcodes.append(pc)
        closest_postcodes = sorted(computed_postcodes, key=lambda pc: distances[pc])
        return closest_postcodes

    def compute_statistical_mode_repartition(
        self, nb_modes_to_compute: int, postcode: str, site: GeoSite, sample_size: int
    ) -> Dict[TransportMode, int]:
        statistical_mode_repartition = {mode: 0 for mode in self.ordered_modes}
        if nb_modes_to_compute <= 0:
            return statistical_mode_repartition
        closest_postcodes = self.compute_closest_postcodes_in_od_matrix(postcode)
        statistics_population = 0
        for closest_postcode in closest_postcodes:
            if statistics_population >= sample_size:
                break
            if closest_postcode in self.od_matrix:
                for site_name in self.od_matrix[closest_postcode]:
                    if site_name == site.nickname:
                        for mode, nb in self.od_matrix[closest_postcode][
                            site_name
                        ].items():
                            statistical_mode_repartition[mode] += nb
                            statistics_population += nb
        if statistics_population > 0:
            return self.discretize_mode_repartition(
                statistical_mode_repartition, nb_modes_to_compute
            )
        else:
            return statistical_mode_repartition

    def discretize_mode_repartition(
        self, obnoxious_repartition: Dict[TransportMode, int], nb_modes: int
    ) -> Dict[TransportMode, int]:
        discretized_stat_nb = discretize_repartition(
            [obnoxious_repartition[mode] for mode in self.ordered_modes],
            nb_modes,
        )
        discretized_stat = {
            mode: pop for mode, pop in zip(self.ordered_modes, discretized_stat_nb)
        }
        return discretized_stat

    def get_mode_repartition(
        self,
        postcode: str,
        site: GeoSite,
        employees: List[GeoEmployee],
        sample_size: int,
    ) -> Tuple[
        Dict[TransportMode, int], Dict[TransportMode, int], Dict[TransportMode, int]
    ]:
        actual_population = len(employees)
        observed_mode_repartition, leftovers = (
            self.get_observed_repartition_scaled_down_on_workforce(
                postcode, site, actual_population
            )
        )
        observed_population = sum(observed_mode_repartition.values())
        nb_modes_to_compute = actual_population - observed_population
        statistical_mode_repartition = self.compute_statistical_mode_repartition(
            nb_modes_to_compute, postcode, site, sample_size
        )
        return observed_mode_repartition, leftovers, statistical_mode_repartition

    def group_commutes_by_od(
        self, commutes: Iterable[Commute[GeoEmployee, GeoSite, TimedCommuteData]]
    ) -> Dict[Tuple[str, GeoSite], List[GeoEmployee]]:
        groups: Dict[Tuple[str, GeoSite], List[GeoEmployee]] = defaultdict(list)
        for commute in commutes:
            employee_postcode = commute.origin.address_details.postcode
            site = commute.destination
            groups[(employee_postcode, site)].append(commute.origin)
        return groups

    def compute_employees_mode_in_scenario(
        self, scenario: TimedScenario, constraints: Constraints
    ) -> Dict[GeoEmployee, TransportMode]:
        grouped_commutes = self.group_commutes_by_od(scenario.commutes)
        employees_commutes = self.get_employees_commutes(scenario.commutes)
        workforce_by_site = self.compute_workforce_by_site(scenario)
        all_modes = {}
        for (postcode, site), employees in grouped_commutes.items():
            sample_size = numpy.ceil(0.1 * workforce_by_site[site])
            observed_mode_repartition, leftovers, statistical_mode_repartition = (
                self.get_mode_repartition(postcode, site, employees, sample_size)
            )
            first_modes_assigned = {
                mode: observed_mode_repartition[mode]
                + statistical_mode_repartition[mode]
                for mode in TransportMode
            }
            group_modes = self.find_employees_most_probable_mode(
                {},
                employees,
                first_modes_assigned,
                employees_commutes,
                constraints,
            )
            remaining_employees = [
                e for e in employees_commutes if e not in group_modes
            ]
            if len(remaining_employees) > 0:
                group_modes = self.find_employees_most_probable_mode(
                    group_modes,
                    remaining_employees,
                    leftovers,
                    employees_commutes,
                    constraints,
                )
            for employee in employees:
                if employee.transport_mode is not None:
                    all_modes[employee] = employee.transport_mode
                elif employee in group_modes:
                    all_modes[employee] = group_modes[employee]
        remaining_employees = [e for e in employees_commutes if e not in all_modes]
        global_stats = self.compute_global_mode_repartition(len(remaining_employees))
        all_modes = self.find_employees_most_probable_mode(
            all_modes,
            remaining_employees,
            global_stats,
            employees_commutes,
            constraints,
        )
        remaining_employees = [e for e in employees_commutes if e not in all_modes]
        remaining_modes = self.compute_default_modes(
            remaining_employees, employees_commutes
        )
        for employee, mode in remaining_modes.items():
            all_modes[employee] = mode
        return all_modes

    def find_employees_most_probable_mode(
        self,
        already_assigned_modes: Dict[GeoEmployee, TransportMode],
        employees: List[GeoEmployee],
        modes_repartition: Dict[TransportMode, int],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
        constraints: Constraints,
    ) -> Dict[GeoEmployee, TransportMode]:
        employees_mode = {e: m for e, m in already_assigned_modes.items()}
        for mode in self.ordered_modes:
            if mode not in modes_repartition:
                continue
            preferred_employees_for_mode = sorted(
                [
                    e
                    for e in employees
                    if e not in employees_mode
                    and mode in employees_commutes[e].duration
                    and (
                        constraints.duration.check_mode_duration_against_constraint(
                            mode, employees_commutes[e].duration[mode]
                        )
                    )
                ],
                key=lambda e: employees_commutes[e].duration[mode],
            )
            target_number = int(modes_repartition[mode])
            for employee in preferred_employees_for_mode[:target_number]:
                employees_mode[employee] = mode
        return employees_mode

    def compute_default_modes(
        self,
        employees: List[GeoEmployee],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
    ) -> Dict[GeoEmployee, TransportMode]:
        employees_mode = {}
        for employee in employees:
            data = employees_commutes[employee]
            employees_mode[employee] = self.default_assigner.guess_best_mode(
                data.duration
            )
        return employees_mode

    def get_employees_commutes(
        self, commutes: Iterable[Commute[GeoEmployee, GeoSite, TimedCommuteData]]
    ) -> Dict[GeoEmployee, TimedCommuteData]:
        employees_commute = {}
        for commute in commutes:
            if commute.origin not in employees_commute:
                employees_commute[commute.origin] = commute.data
            else:
                raise ValueError(
                    f"Several commutes for a single employee {commute.origin}.\n"
                    f"Recorded commute data {employees_commute[commute.origin]};\n"
                    f"Finding other commute {commute}."
                )
        return employees_commute

    def compute_workforce_by_site(self, scenario: TimedScenario) -> Dict[GeoSite, int]:
        workforce_by_site = {}
        for site, commutes in scenario.get_destinations().items():
            workforce_by_site[site] = len(commutes)
        return workforce_by_site


class RuleBasedTransportModeAssigner(TransportModeAssigner):
    def __init__(
        self,
        modal_share_rules: TransportModalShareRules,
    ) -> None:
        self.rules = modal_share_rules
        self.default_assigner = FastestModeAssigner()
        self.soft_mobility_modes = [
            TransportMode.WALK,
            TransportMode.BICYCLE,
        ]

    def compute_employees_mode_in_scenario(
        self, scenario: TimedScenario, constraints: Constraints
    ) -> Dict[GeoEmployee, TransportMode]:
        employees_commutes = self.get_employees_commutes(scenario.commutes)
        all_modes: Dict[GeoEmployee, TransportMode] = {}

        # Assign modes defined by the user
        for employee, commute_data in employees_commutes.items():
            if employee.transport_mode is not None:
                all_modes[employee] = employee.transport_mode

        remaining_employees = [e for e in employees_commutes if e not in all_modes]

        # Assign airplane mode
        for employee in remaining_employees:
            mode = self._apply_distance_constraints(
                employees_commutes[employee], constraints
            )
            if mode:
                all_modes[employee] = mode

        remaining_employees = [e for e in employees_commutes if e not in all_modes]

        eligible_for_soft_mobility = [
            e
            for e in remaining_employees
            if self._is_eligible_for_soft_mobility(employees_commutes[e], constraints)
        ]

        eligible_for_soft_mobility = sorted(
            eligible_for_soft_mobility,
            key=lambda e: employees_commutes[e].duration[TransportMode.BICYCLE],
        )

        remaining_employees = [
            e for e in remaining_employees if e not in eligible_for_soft_mobility
        ]

        self._assign_modes_by_share(
            eligible_for_soft_mobility,
            remaining_employees,
            all_modes,
            employees_commutes,
        )

        return all_modes

    def _apply_distance_constraints(
        self, commute_data: TimedCommuteData, constraints: Constraints
    ) -> Optional[TransportMode]:
        if not commute_data.distance or not constraints.distance:
            return None

        for mode, constraint in constraints.distance.constraints.items():
            if mode in commute_data.distance:
                mode_distance = commute_data.distance[mode]
                if constraint.is_satisfied(mode_distance):
                    return mode
        return None

    def _is_eligible_for_soft_mobility(
        self, commute_data: TimedCommuteData, constraints: Constraints
    ) -> bool:
        for mode in self.soft_mobility_modes:
            if mode not in commute_data.duration:
                continue

            if mode not in constraints.duration.constraints:
                return True

            constraint = constraints.duration.constraints[mode]
            if constraint.is_satisfied(commute_data.duration[mode]):
                return True

        return False

    def _assign_modes_by_share(
        self,
        soft_mobility_employees: List[GeoEmployee],
        other_employees: List[GeoEmployee],
        all_modes: Dict[GeoEmployee, TransportMode],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
    ) -> None:
        total_unassigned = len(soft_mobility_employees) + len(other_employees)
        mode_counts = {}
        for mode in TransportMode:
            mode_counts[mode] = sum(1 for m in all_modes.values() if m == mode)

        shares = self.rules.mode_shares
        soft_split = self.rules.soft_mobility_split

        total_employees = total_unassigned + len(all_modes)

        soft_target = int(total_employees * shares.soft_mobility)
        soft_count = max(
            0, soft_target - sum(mode_counts[m] for m in self.soft_mobility_modes)
        )

        pt_target = int(total_employees * shares.public_transport)
        pt_count = max(0, pt_target - mode_counts[TransportMode.PUBLIC_TRANSPORT])

        plane_target = int(total_employees * shares.airplane)
        plane_count = max(0, plane_target - mode_counts[TransportMode.AIRPLANE])

        car_target = int(total_employees * shares.car)
        car_count = max(0, car_target - mode_counts[TransportMode.CAR])

        selected_soft_mobility = soft_mobility_employees[:soft_count]
        for employee in selected_soft_mobility:
            soft_mode = self._select_best_soft_mode(
                employees_commutes[employee], soft_split
            )
            all_modes[employee] = soft_mode
        assigned_set = set(selected_soft_mobility)
        if soft_count - len(selected_soft_mobility) > 0:
            print(
                f"{soft_count - len(selected_soft_mobility)} employees"
                f"could not be assigned a soft mobility mode."
            )

        unassigned = soft_mobility_employees[soft_count:] + other_employees

        eligible_for_pt = [
            e
            for e in unassigned
            if TransportMode.PUBLIC_TRANSPORT in employees_commutes[e].duration
            and e not in assigned_set
        ]
        sorted_for_pt = sorted(
            eligible_for_pt,
            key=lambda e: employees_commutes[e].duration[
                TransportMode.PUBLIC_TRANSPORT
            ],
        )
        selected_pt = sorted_for_pt[:pt_count]
        for employee in selected_pt:
            all_modes[employee] = TransportMode.PUBLIC_TRANSPORT
        assigned_set.update(selected_pt)

        eligible_for_plane = [
            e
            for e in unassigned
            if TransportMode.AIRPLANE in employees_commutes[e].duration
            and e not in assigned_set
        ]
        selected_plane = self._select_plane_candidates(
            eligible_for_plane, employees_commutes, plane_count
        )
        for employee in selected_plane:
            all_modes[employee] = TransportMode.AIRPLANE
        assigned_set.update(selected_plane)

        eligible_for_car = [
            e
            for e in unassigned
            if TransportMode.CAR in employees_commutes[e].duration
            and e not in assigned_set
        ]
        sorted_for_car = sorted(
            eligible_for_car,
            key=lambda e: employees_commutes[e].duration[TransportMode.CAR],
        )
        selected_car = sorted_for_car[:car_count]
        for employee in selected_car:
            all_modes[employee] = TransportMode.CAR
        assigned_set.update(selected_car)
        remaining = [emp for emp in unassigned if emp not in assigned_set]

        if remaining:
            print(f"{len(remaining)} employees could not be assigned a mode.")
            for employee in remaining:
                fastest_mode = self.default_assigner.guess_best_mode(
                    employees_commutes[employee].duration
                )
                all_modes[employee] = fastest_mode
                print(
                    f"Assigning {fastest_mode} to {employee.nickname} "
                    f"because no mode could be assigned."
                )

    def _select_best_soft_mode(
        self, commute_data: TimedCommuteData, soft_split: Dict[TransportMode, float]
    ) -> TransportMode:
        available_modes = [
            mode for mode in self.soft_mobility_modes if mode in commute_data.duration
        ]
        if len(available_modes) == 0:
            return TransportMode.WALK

        return max(available_modes, key=lambda m: soft_split[m])

    def _select_plane_candidates(
        self,
        employees: List[GeoEmployee],
        employees_commutes: Dict[GeoEmployee, TimedCommuteData],
        plane_count: int,
    ) -> List[GeoEmployee]:
        sorted_for_plane = sorted(
            employees,
            key=lambda e: (
                employees_commutes[e].distance.get(TransportMode.AIRPLANE, 0),
                employees_commutes[e].duration.get(TransportMode.AIRPLANE, 0),
            ),
            reverse=True,
        )
        return sorted_for_plane[:plane_count]

    def get_employees_commutes(
        self, commutes: Iterable[Commute[GeoEmployee, GeoSite, TimedCommuteData]]
    ) -> Dict[GeoEmployee, TimedCommuteData]:
        employees_commute = {}
        for commute in commutes:
            employees_commute[commute.origin] = commute.data
        return employees_commute


class TransportModeAssignerMaker:
    def __init__(
        self,
        territories_dir: str,
        od_matrix: Optional[str] = None,
        mode_shares: Optional[str] = None,
    ) -> None:
        self.territory_db = TerritoryDatabase(territories_dir)
        self.od_matrix = od_matrix
        self.mode_shares = mode_shares

    @staticmethod
    def init_from_repository(
        repository: AbstractTerritoriesRepository,
    ) -> "TransportModeAssignerMaker":
        return TransportModeAssignerMaker(repository.get_territories_dir())

    def make(self, kind: str) -> TransportModeAssigner:
        if kind == "fastest":
            return FastestModeAssigner()
        elif kind == "travel_score":
            return TravelScoreComputer()
        elif kind == "stats":
            return self.make_statistics_mode_assigner()
        elif kind == "form_stats":
            return self.make_form_stats_mode_assigner()
        elif kind == "default":
            return self.make_statistics_mode_assigner()
        elif kind == "rule_based":
            return self.make_rule_based_mode_assigner()
        raise ValueError(f"Invalid transport mode assigner kind: {kind}")

    def make_statistics_mode_assigner(self) -> TransportModeAssigner:
        return StatisticsModeAssigner(
            get_mode_repartition=self.make_mode_repartition_getter(),
        )

    def make_rule_based_mode_assigner(self) -> TransportModeAssigner:
        if self.mode_shares is None:
            raise ValueError(
                "Rule based mode assigner requires mode shares to be provided."
            )
        modal_share_rules = self.parse_mode_shares_config(self.mode_shares)
        return RuleBasedTransportModeAssigner(modal_share_rules)

    def parse_mode_shares_config(
        self, mode_shares_file: str
    ) -> TransportModalShareRules:
        return TransportModalShareRules.from_json(mode_shares_file)

    def parse_od_matrix(self, od_matrix_file: str) -> ODModeMatrix:
        with open(od_matrix_file) as f:
            json_od_matrix = json.load(f)
        od_matrix: ODModeMatrix = {}
        for pc, site_matrix in json_od_matrix.items():
            pc_matrix = {}
            for site_id, mode_stat in site_matrix.items():
                mode_matrix = {}
                for mode_str, nb in mode_stat.items():
                    mode_matrix[TransportMode.from_string(mode_str)] = nb
                pc_matrix[site_id] = mode_matrix
            od_matrix[pc] = pc_matrix
        return od_matrix

    def make_form_stats_mode_assigner(self) -> TransportModeAssigner:
        if self.od_matrix is None:
            raise ValueError(
                "Form based stats mode assigner requires"
                " an od matrix to be provided."
            )
        return FormStatsModeAssigner(
            od_matrix=self.parse_od_matrix(self.od_matrix),
            postcode_coordinates_getter=self.territory_db.get_postcode_coordinates,
        )

    def make_mode_repartition_getter(
        self,
    ) -> Callable[[str, str], Dict[TransportMode, float]]:
        map_codes_to_transport_mode = {
            "CAR": TransportMode.CAR,
            "MOTO": TransportMode.CAR,
            "PT": TransportMode.PUBLIC_TRANSPORT,
            "BIKE": TransportMode.BICYCLE,
            "MAP": TransportMode.WALK,
        }

        def mode_repartition_getter(
            citycode_from: str, citycode_to: str
        ) -> Dict[TransportMode, float]:
            mode_repartition: Dict[TransportMode, float] = defaultdict(lambda: 0)
            workers_by_modes = self.territory_db.get_workers_by_transport_modes(
                citycode_from, citycode_to
            )
            for mode, nb_workers in workers_by_modes.items():
                try:
                    transport_mode = map_codes_to_transport_mode[mode]
                except KeyError:
                    continue
                mode_repartition[transport_mode] += nb_workers
            return mode_repartition

        return mode_repartition_getter


def make_commute_mode_shifter(
    employees_mode: Dict[GeoEmployee, TransportMode],
) -> Callable[
    [Commute[GeoEmployee, Localised, TimedCommuteData], None], ModalCommuteData
]:
    def _perform_modal_shift(
        commute: Commute[GeoEmployee, Localised, TimedCommuteData],
        scenario_data: None,
    ) -> ModalCommuteData:
        employee_mode = employees_mode[commute.origin]
        commute_mode = ModalShift(
            employee_mode, commute.data.duration
        ).guess_best_mode()
        return ModalCommuteData(
            best_mode=commute_mode,
            duration=commute.data.duration,
            distance=commute.data.distance,
            emission=commute.data.emission,
            alternative_arrival_time=commute.data.alternative_arrival_time,
        )

    return _perform_modal_shift


class TransportModeComputer:
    def __init__(self, model: TransportModeAssigner) -> None:
        self.mode_assigner_model = model
        self.employees_mode: Dict[GeoEmployee, TransportMode] = {}

    def compute_transport_mode(self, study: TimedStudy) -> ModalStudy:
        self.employees_mode = self.compute_employees_mode(study)
        scenarios = study.scenarios.update_commutes_with_context(
            self.compute_commute_best_mode
        )
        coworking_scenario = study.coworking_scenario.update_commutes_with_context(
            make_commute_mode_shifter(self.employees_mode)
        )
        return ModalStudy(
            scenarios=scenarios,
            poi_scenarios=study.poi_scenarios,
            infrastructure=study.infrastructure,
            data=study.data,
            geocode_failed_sites=study.geocode_failed_sites,
            geocode_failed_employees=study.geocode_failed_employees,
            geocode_failed_both=study.geocode_failed_both,
            isochrones=study.isochrones,
            coworking_scenario=coworking_scenario,
        )

    def compute_employees_mode(
        self, study: TimedStudy
    ) -> Dict[GeoEmployee, TransportMode]:
        constraints = _get_constraints(study, self.mode_assigner_model)
        present_scenario = _get_present_scenario(study)
        if present_scenario is not None:
            return self.mode_assigner_model.compute_employees_mode_in_scenario(
                present_scenario, constraints
            )
        else:
            already_set_modes = {}
            for employee, _ in study.scenarios.iterate_on_origins():
                if employee.transport_mode is None:
                    raise ValueError(
                        "Impossible to determine employee favourite mode,"
                        " employee with None mode and no present scenario.\n"
                        f"{employee}"
                    )
                else:
                    already_set_modes[employee] = employee.transport_mode
            return already_set_modes

    def compute_commute_best_mode(
        self,
        commute: Commute[GeoEmployee, GeoSite, TimedCommuteData],
        scenario_data: ScenarioData,
    ) -> ModalCommuteData:
        employee_mode = self.employees_mode[commute.origin]
        if scenario_data.is_present:
            commute_mode = employee_mode
        else:
            commute_mode = ModalShift(
                employee_mode, commute.data.duration
            ).guess_best_mode()
        return ModalCommuteData(
            best_mode=commute_mode,
            duration=commute.data.duration,
            distance=commute.data.distance,
            emission=commute.data.emission,
            alternative_arrival_time=commute.data.alternative_arrival_time,
        )


def _get_constraints(study: TimedStudy, model: TransportModeAssigner) -> Constraints:
    if not study.data.constraints.empty:
        return study.data.constraints

    match model:
        case StatisticsModeAssigner():
            return Constraints(
                duration=DurationConstraints(
                    constraints=ImmutableDict(
                        {
                            TransportMode.WALK: DurationConstraint(
                                TransportMode.WALK, 30
                            ),
                            TransportMode.BICYCLE: DurationConstraint(
                                TransportMode.BICYCLE, 40
                            ),
                            TransportMode.PUBLIC_TRANSPORT: DurationConstraint(
                                TransportMode.PUBLIC_TRANSPORT, 90
                            ),
                        }
                    )
                )
            )
        case FormStatsModeAssigner():
            return Constraints(
                duration=DurationConstraints(
                    constraints=ImmutableDict(
                        {
                            TransportMode.WALK: DurationConstraint(
                                TransportMode.WALK, 40
                            ),
                            TransportMode.BICYCLE: DurationConstraint(
                                TransportMode.BICYCLE, 40
                            ),
                        }
                    )
                )
            )
        case _:
            return Constraints(
                duration=DurationConstraints(
                    constraints=ImmutableDict(
                        {
                            TransportMode.WALK: DurationConstraint(
                                TransportMode.WALK, 30
                            ),
                            TransportMode.BICYCLE: DurationConstraint(
                                TransportMode.BICYCLE, 40
                            ),
                            TransportMode.PUBLIC_TRANSPORT: DurationConstraint(
                                TransportMode.PUBLIC_TRANSPORT, 90
                            ),
                        }
                    )
                )
            )


def _get_present_scenario(study: TimedStudy) -> Optional[TimedScenario]:
    return next((s for s in study.scenarios if s.data.is_present), None)


def compute_transport_mode(
    study: TimedStudy, model: TransportModeAssigner
) -> ModalStudy:
    return TransportModeComputer(model).compute_transport_mode(study)

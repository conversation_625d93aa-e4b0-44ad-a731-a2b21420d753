{"data_mtime": 1752154446, "dep_lines": [3, 4, 5, 8, 9, 2, 10, 1, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.executive_summary", "mobility.ir.transport", "mobility.funky", "mobility.quantity", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "mobility.ir.cost", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.workers.zfe_impact_computer", "typing", "typing_extensions"], "hash": "67b3e71b9a6c66a0ea9c4d6f0a77b16f3de6a7a5", "id": "mobility.workers.executive_summary_computer", "ignore_all": false, "interface_hash": "00cec94a1450aefc6fb5c687a0cf59b2e6d45971", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\executive_summary_computer.py", "plugin_data": null, "size": 4851, "suppressed": [], "version_id": "1.16.1"}
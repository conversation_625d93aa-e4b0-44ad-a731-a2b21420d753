{"data_mtime": 1753176775, "dep_lines": [4, 5, 6, 7, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "functools", "typing", "builtins", "_frozen_importlib", "abc", "api_abstraction.api.api", "enum", "mobility", "mobility.ir", "typing_extensions"], "hash": "1871cfebd9800585f27d04e4843284199f2607e8", "id": "api_abstraction.api.modal_travel_time_api", "ignore_all": false, "interface_hash": "e3325c3203a1ad78bbdee595e3f894aef85bd05c", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\api\\modal_travel_time_api.py", "plugin_data": null, "size": 2404, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1751444474, "dep_lines": [9, 10, 12, 13, 14, 1, 2, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 7, 4, 5], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10], "dependencies": ["api_abstraction.google.api", "api_abstraction.otp.api", "mobility.ir.geo_study", "mobility.ir.transport", "mobility.quantity", "os", "itertools", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "api_abstraction.google", "api_abstraction.otp", "configparser", "enum", "functools", "mobility.ir", "ntpath", "typing", "typing_extensions"], "hash": "92dbfef8acbea84c18a0776b710920ddb6383329", "id": "scripts.compare_otp_to_google", "ignore_all": false, "interface_hash": "269c312c160c3af0e200261e61e3ff561408fd25", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\compare_otp_to_google.py", "plugin_data": null, "size": 3347, "suppressed": ["fiona.crs", "shapely.geometry", "fiona", "shapely"], "version_id": "1.16.1"}
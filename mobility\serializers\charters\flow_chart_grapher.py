import dataclasses
import os
from collections import defaultdict
from typing import Dict, <PERSON><PERSON>, <PERSON>, Tuple, TypeVar
import pandas as pd

from mobility import config
from mobility.constants import FONTS_DIR
from mobility.serializers.chart_writer import ChartWriter
from mobility.serializers.charters.svg_interface import (
    HorizontalAlignment,
    SVGFigure,
    SVGFont,
    SVGLabel,
    SVGLinearGradient,
    SVGNode,
    SVGNodes,
    SVGPath,
    SVGPathCurve,
    SVGPathSegment,
    SVGPoint,
    SVGRect,
    SVGTextBlock,
    VerticalAlignment,
)
from mobility.serializers.charters.svg_library import SVGLibrary
from mobility.workers.color_picker import ColorPicker

T = TypeVar("T")


@dataclasses.dataclass
class Flow(Generic[T]):
    height_factor: float
    total_from: Dict[T, int]
    total_to: Dict[T, int]
    flow_from_to: Dict[T, Dict[T, int]]


class FlowChartGrapher(Generic[T]):
    def __init__(
        self,
        output_dir: str,
        main_title_font_size_px: int = 40,
        title_font_size_px: int = 35,
        line_header_font_size_px: int = 30,
        number_font_size_px: int = 20,
        font_path: str = None,
        columns_height: int = 600,
        column_width: int = 80,
        categories_padding: int = 20,
        flow_width: int = 200,
        icon_size: int = 60,
        icon_label_space: int = 10,
        node_text_color: str = "#fff",
        default_font_color: str = "#000000",
        flow_opacity: float = 0.4,
        main_title_color: str = None,
    ) -> None:
        self.output_dir = output_dir
        self.writer = ChartWriter(output_dir)
        self.colorer = ColorPicker()
        self.main_title_font_size_px = main_title_font_size_px
        self.title_font_size_px = title_font_size_px
        self.line_header_font_size_px = line_header_font_size_px
        self.number_font_size_px = number_font_size_px
        self.font_path = font_path or config["mobility"].get(
            "regular_font", os.path.join(FONTS_DIR, "Lato-Regular.ttf")
        )
        self.main_title_font = SVGFont(self.font_path, self.main_title_font_size_px)
        self.title_font = SVGFont(self.font_path, self.title_font_size_px)
        self.line_header_font = SVGFont(self.font_path, self.line_header_font_size_px)
        self.number_font = SVGFont(self.font_path, self.number_font_size_px)
        self.columns_height = columns_height
        self.column_width = column_width
        self.categories_padding = categories_padding
        self.flow_width = flow_width
        self.icon_size = icon_size
        self.icon_label_space = icon_label_space
        self.node_text_color = node_text_color
        self.default_font_color = default_font_color
        self.flow_opacity = flow_opacity
        self.main_title_color = main_title_color or self.colorer.pick("light_blue")

    def make_chart(
        self,
        file_name: str,
        title: str,
        flows: List[Tuple[T, T]],
        titles: Tuple[str, str],
        colors: Dict[T, str],
        categories_order: List[T],
        categories_labels: Dict[T, str],
        categories_icons: Dict[T, str],
    ) -> str:
        prepared_flows = self.compute_flows(flows, categories_order)
        svg_figure = self.make_svg_figure(
            title,
            prepared_flows,
            titles,
            colors,
            categories_order,
            categories_labels,
            categories_icons,
        )
        file_name = f"{file_name}.svg"
        return self.writer.write_svg(svg_figure, file_name)

    def make_multilevel_chart(
        self,
        file_name: str,
        title: str,
        flows: List[Tuple[T, ...]],
        level_titles: List[str],
        colors: Dict[T, str],
        categories_order: List[T],
        categories_labels: Dict[T, str],
        categories_icons: Dict[T, str],
        level_spacing: float = None,
        main_title_font_size_px: int = None,
        title_font_size_px: int = None,
        line_header_font_size_px: int = None,
        number_font_size_px: int = None,
        font_colors: Dict[T, str] = None,
    ) -> str:
        if level_spacing is None:
            level_spacing = self.column_width + self.flow_width

        # Create custom fonts if font sizes are provided
        if main_title_font_size_px is not None:
            main_title_font = SVGFont(self.font_path, main_title_font_size_px)
        else:
            main_title_font = self.main_title_font

        if title_font_size_px is not None:
            title_font = SVGFont(self.font_path, title_font_size_px)
        else:
            title_font = self.title_font

        if line_header_font_size_px is not None:
            line_header_font = SVGFont(self.font_path, line_header_font_size_px)
        else:
            line_header_font = self.line_header_font

        if number_font_size_px is not None:
            number_font = SVGFont(self.font_path, number_font_size_px)
        else:
            number_font = self.number_font

        all_columns, all_flows, all_labels, all_category_labels = [], [], [], []

        for level_idx in range(len(level_titles) - 1):
            pair_flows = [(flow[level_idx], flow[level_idx + 1]) for flow in flows]
            flow_data = self.compute_flows(pair_flows, categories_order)

            x_from = level_idx * level_spacing
            x_to = x_from + level_spacing

            anchors_from = self.compute_flow_anchors(
                flow_data.total_from, x_from, flow_data.height_factor, categories_order
            )
            anchors_to = self.compute_flow_anchors(
                flow_data.total_to, x_to, flow_data.height_factor, categories_order
            )

            if level_idx == 0:
                all_columns.append(self.make_totals_column(anchors_from, colors))
                all_labels.append(
                    self.make_totals_labels(flow_data.total_from, anchors_from, colors)
                )
                categories_labels_from = self.make_title_labels(
                    categories_labels,
                    categories_icons,
                    anchors_from,
                    colors,
                    line_header_font,
                    font_colors,
                )
                placed_categories_labels_from = categories_labels_from.translate(
                    dx=-self.column_width / 2
                    - categories_labels_from.width / 2
                    - self.categories_padding,
                    dy=0.0,
                )
                all_category_labels.append(placed_categories_labels_from)

            all_columns.append(self.make_totals_column(anchors_to, colors))
            all_labels.append(
                self.make_totals_labels(flow_data.total_to, anchors_to, colors)
            )
            all_flows.append(
                self.make_flow_shapes(
                    flow_data, anchors_from, anchors_to, colors, categories_order
                )
            )

            categories_labels_to = self.make_title_labels(
                categories_labels,
                categories_icons,
                anchors_to,
                colors,
                line_header_font,
                font_colors,
            )
            placed_categories_labels_to = categories_labels_to.translate(
                dx=self.column_width / 2
                + categories_labels_to.width / 2
                + self.categories_padding,
                dy=0.0,
            )
            all_category_labels.append(placed_categories_labels_to)

        title_labels = self.make_multilevel_titles(
            level_titles, level_spacing, title_font
        )

        all_nodes = all_columns + all_flows + all_labels + all_category_labels
        flows_node = SVGNodes(tuple(all_flows))

        title_node = SVGLabel(title, main_title_font, color=self.main_title_color).move(
            x=flows_node.x + flows_node.width / 2,
            y=title_labels.y - title_labels.height,
            vertical_align=VerticalAlignment.BOTTOM,
            horizontal_align=HorizontalAlignment.CENTER,
        )

        nodes = SVGNodes(tuple([title_node] + all_nodes + [title_labels]))
        svg_figure = SVGFigure.from_node(nodes, padding=0)
        file_name = f"{file_name}.svg"
        return self.writer.write_svg(svg_figure, file_name)

    def compute_flows(
        self,
        flows: List[Tuple[T, T]],
        categories_order: List[T],
    ) -> Flow[T]:
        total_from: Dict[T, int] = defaultdict(int)
        total_to: Dict[T, int] = defaultdict(int)
        flow: Dict[T, Dict[T, int]] = defaultdict(lambda: defaultdict(int))
        for origin, destination in flows:
            total_from[origin] += 1
            total_to[destination] += 1
            flow[origin][destination] += 1
        height_factor = self.columns_height / sum(v for v in total_from.values())
        flow = {f: dict(flow[f]) for f in flow}
        return Flow(
            height_factor=height_factor,
            total_from=total_from,
            total_to=total_to,
            flow_from_to=dict(flow),
        )

    def make_svg_figure(
        self,
        title: str,
        flow: Flow[T],
        titles: Tuple[str, str],
        colors: Dict[T, str],
        categories_order: List[T],
        categories_labels: Dict[T, str],
        categories_icons: Dict[T, str],
    ) -> SVGFigure:
        anchors_from = self.compute_flow_anchors(
            flow.total_from, 0, flow.height_factor, categories_order
        )
        anchors_to = self.compute_flow_anchors(
            flow.total_to,
            self.flow_width + self.column_width,
            flow.height_factor,
            categories_order,
        )
        totals_from = self.make_totals_column(anchors_from, colors)
        total_labels_from = self.make_totals_labels(
            flow.total_from,
            anchors_from,
            colors=colors,
        )
        categories_labels_from = self.make_title_labels(
            categories_labels, categories_icons, anchors_from, colors
        )
        placed_categories_labels_from = categories_labels_from.translate(
            dx=-self.column_width / 2
            - categories_labels_from.width / 2
            - self.categories_padding,
            dy=0.0,
        )
        totals_to = self.make_totals_column(anchors_to, colors)
        total_labels_to = self.make_totals_labels(
            flow.total_to,
            anchors_to,
            colors=colors,
        )
        categories_labels_to = self.make_title_labels(
            categories_labels, categories_icons, anchors_to, colors
        )
        placed_categories_labels_to = categories_labels_to.translate(
            dx=self.column_width / 2
            + categories_labels_to.width / 2
            + self.categories_padding,
            dy=0.0,
        )
        title_labels = self.make_titles(titles)
        flows = self.make_flow_shapes(
            flow, anchors_from, anchors_to, colors, categories_order
        )
        title_node = SVGLabel(
            title, self.main_title_font, color=self.main_title_color
        ).move(
            x=flows.x + flows.width / 2,
            y=title_labels.y - title_labels.height,
            vertical_align=VerticalAlignment.BOTTOM,
            horizontal_align=HorizontalAlignment.CENTER,
        )
        nodes = SVGNodes(
            (
                title_node,
                totals_from,
                totals_to,
                flows,
                total_labels_from,
                total_labels_to,
                placed_categories_labels_from,
                placed_categories_labels_to,
                title_labels,
            )
        )
        return SVGFigure.from_node(nodes, padding=0)

    def make_titles(self, titles: Tuple[str, str]) -> SVGNode:
        nodes = []
        for i, title in enumerate(titles):
            node = SVGLabel(
                title,
                self.title_font,
                i * (self.flow_width + self.column_width) + self.column_width / 2,
                -self.categories_padding,
                VerticalAlignment.BOTTOM,
                HorizontalAlignment.CENTER,
            )
            nodes.append(node)
        return SVGNodes(tuple(nodes))

    def make_multilevel_titles(
        self, level_titles: List[str], level_spacing: float, title_font: SVGFont = None
    ) -> SVGNode:
        if title_font is None:
            title_font = self.title_font

        nodes = []
        for i, title in enumerate(level_titles):
            node = SVGLabel(
                title,
                title_font,
                i * level_spacing + self.column_width / 2,
                -self.categories_padding,
                VerticalAlignment.BOTTOM,
                HorizontalAlignment.CENTER,
            )
            nodes.append(node)
        return SVGNodes(tuple(nodes))

    def compute_flow_anchors(
        self,
        totals: Dict[T, int],
        x: float,
        height_factor: float,
        categories_order: List[T],
    ) -> Dict[T, Tuple[SVGPoint, float]]:
        anchors = {}
        current_anchor_y = 0.0
        for category in categories_order:
            if category not in totals:
                continue
            total = totals[category]
            anchor_point = SVGPoint(x, current_anchor_y)
            column_height = total * height_factor
            anchors[category] = (anchor_point, column_height)
            current_anchor_y = anchor_point.y + column_height + self.categories_padding
        return anchors

    def make_totals_column(
        self,
        anchors: Dict[T, Tuple[SVGPoint, float]],
        colors: Dict[T, str],
    ) -> SVGNode:
        nodes = []
        for category, anchor_height in anchors.items():
            anchor, height = anchor_height
            color = (
                colors[category] if category in colors else self.colorer.pick(category)
            )
            nodes.append(SVGRect(anchor.x, anchor.y, self.column_width, height, color))
        return SVGNodes(tuple(nodes))

    def make_totals_labels(
        self,
        totals: Dict[T, int],
        anchors: Dict[T, Tuple[SVGPoint, float]],
        colors: Dict[T, str],
    ) -> SVGNode:
        total = sum(totals.values())
        nodes = []
        for category, anchor_height in anchors.items():
            if category not in totals:
                continue
            anchor, height = anchor_height

            text_to_write = (
                f"{100 * totals[category] / total:.1f}% ({totals[category]})"
            )
            text_node = SVGTextBlock(
                0,
                0,
                text_to_write,
                self.number_font,
                self.column_width,
                self.node_text_color,
                alignment=HorizontalAlignment.CENTER,
            ).move(
                x=anchor.x + self.column_width / 2,
                y=anchor.y + height / 2,
                horizontal_align=HorizontalAlignment.CENTER,
                vertical_align=VerticalAlignment.MIDDLE,
            )
            nodes.append(text_node)
        return SVGNodes(tuple(nodes))

    def make_title_labels(
        self,
        categories_labels: Dict[T, str],
        categories_icons: Dict[T, str],
        anchors: Dict[T, Tuple[SVGPoint, float]],
        colors: Dict[T, str],
        line_header_font: SVGFont = None,
        font_colors: Dict[T, str] = None,
    ) -> SVGNode:
        if line_header_font is None:
            line_header_font = self.line_header_font

        nodes = []
        for category, anchor_height in anchors.items():
            anchor, height = anchor_height

            # Determine font color
            font_color = self.default_font_color
            if font_colors and category in font_colors:
                font_color = font_colors[category]

            label = SVGLabel(
                categories_labels[category],
                line_header_font,
                color=font_color,
            )
            color = (
                colors[category] if category in colors else self.colorer.pick(category)
            )
            # Only show icons if categories_icons is provided and not empty
            if categories_icons and category in categories_icons:
                if height < self.icon_size + label.height:
                    icon = SVGLibrary.get(categories_icons[category], color=color).fit(
                        label.height, label.height
                    )
                    labelled_icon = (
                        SVGNodes((icon, label))
                        .distribute_horizontally(self.icon_label_space)
                        .align_vertically(VerticalAlignment.MIDDLE)
                        .move(
                            anchor.x + self.column_width / 2,
                            anchor.y + height / 2,
                        )
                    )
                else:
                    icon = SVGLibrary.get(categories_icons[category], color=color).fit(
                        self.icon_size, self.icon_size
                    )
                    labelled_icon = (
                        SVGNodes((icon, label))
                        .distribute_vertically(self.icon_label_space)
                        .align_horizontally(HorizontalAlignment.CENTER)
                        .move(
                            anchor.x + self.column_width / 2,
                            anchor.y + height / 2,
                        )
                    )
                nodes.append(labelled_icon)
            else:
                # No icon, just show the label
                positioned_label = label.move(
                    anchor.x + self.column_width / 2,
                    anchor.y + height / 2,
                    horizontal_align=HorizontalAlignment.CENTER,
                    vertical_align=VerticalAlignment.MIDDLE,
                )
                nodes.append(positioned_label)
        return SVGNodes(tuple(nodes))

    def make_flow_shapes(
        self,
        flow: Flow[T],
        anchors_from: Dict[T, Tuple[SVGPoint, float]],
        anchors_to: Dict[T, Tuple[SVGPoint, float]],
        colors: Dict[T, str],
        categories_order: List[T],
    ) -> SVGNode:
        current_from_delta = {cat: 0.0 for cat in anchors_from}
        current_to_delta = {cat: 0.0 for cat in anchors_to}
        nodes = []
        for origin in categories_order:
            if origin not in flow.flow_from_to:
                continue
            for destination in categories_order:
                if destination not in flow.flow_from_to[origin]:
                    continue
                volume = flow.flow_from_to[origin][destination]
                anchor_from, _ = anchors_from[origin]
                anchor_to, _ = anchors_to[destination]
                A = SVGPoint(
                    anchor_from.x + self.column_width,
                    anchor_from.y + current_from_delta[origin],
                )
                B = SVGPoint(anchor_to.x, anchor_to.y + current_to_delta[destination])
                Ah = SVGPoint(
                    anchor_from.x + self.column_width + self.flow_width / 2,
                    anchor_from.y + current_from_delta[origin],
                )
                Bh = SVGPoint(
                    anchor_to.x - self.flow_width / 2,
                    anchor_to.y + current_to_delta[destination],
                )
                current_from_delta[origin] += volume * flow.height_factor
                current_to_delta[destination] += volume * flow.height_factor
                D = SVGPoint(
                    anchor_from.x + self.column_width,
                    anchor_from.y + current_from_delta[origin],
                )
                C = SVGPoint(anchor_to.x, anchor_to.y + current_to_delta[destination])
                Dh = SVGPoint(
                    anchor_from.x + self.column_width + self.flow_width / 2,
                    anchor_from.y + current_from_delta[origin],
                )
                Ch = SVGPoint(
                    anchor_to.x - self.flow_width / 2,
                    anchor_to.y + current_to_delta[destination],
                )
                segments = [
                    SVGPathCurve(A, B, Ah, Bh),
                    SVGPathSegment(B, C),
                    SVGPathCurve(C, D, Ch, Dh),
                    SVGPathSegment(D, A),
                ]
                color_o = (
                    colors[origin] if origin in colors else self.colorer.pick(origin)
                )
                color_d = (
                    colors[destination]
                    if destination in colors
                    else self.colorer.pick(destination)
                )
                gradient = SVGLinearGradient(
                    origin=SVGPoint(0, 0),
                    destination=SVGPoint(1, 0),
                    colors=[color_o, color_d],
                    offsets=[0.0, 1.0],
                )
                nodes.append(
                    SVGPath(
                        path=segments, fill_gradient=gradient, opacity=self.flow_opacity
                    )
                )
        return SVGNodes(tuple(nodes))


def create_flow_chart_from_csv(
    csv_path: str,
    output_dir: str,
    file_name: str = "flow_chart",
    title: str = "Flow Chart",
    colors: Dict[str, str] = None,
    categories_labels: Dict[str, str] = None,
    categories_icons: Dict[str, str] = None,
) -> str:
    df = pd.read_csv(csv_path)

    required_columns = {"origin_zone", "destination_zone", "count"}
    if not required_columns.issubset(df.columns):
        missing = required_columns - set(df.columns)
        raise ValueError(f"Missing required columns: {missing}")

    categories = set(df["origin_zone"]).union(set(df["destination_zone"]))
    categories_order = sorted(list(categories))

    expanded_flows = []
    for _, row in df.iterrows():
        expanded_flows.extend(
            [(row["origin_zone"], row["destination_zone"])] * row["count"]
        )

    if colors is None:
        colors = {}

    if categories_labels is None:
        categories_labels = {cat: str(cat) for cat in categories}

    if categories_icons is None:
        categories_icons = {cat: "circle" for cat in categories}

    grapher = FlowChartGrapher[str](output_dir)

    return grapher.make_chart(
        file_name=file_name,
        title=title,
        flows=expanded_flows,
        titles=("Origin", "Destination"),
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons=categories_icons,
    )


def create_multilevel_flow_chart_from_csv(
    csv_path: str,
    output_dir: str,
    level_columns: List[str],
    file_name: str = "multilevel_flow_chart",
    title: str = "Multilevel Flow Chart",
    colors: Dict[str, str] = None,
    categories_labels: Dict[str, str] = None,
    categories_icons: Dict[str, str] = None,
) -> str:
    df = pd.read_csv(csv_path)

    required_columns = set(level_columns + ["count"])
    if not required_columns.issubset(df.columns):
        missing = required_columns - set(df.columns)
        raise ValueError(f"Missing required columns: {missing}")

    categories = set()
    for col in level_columns:
        categories.update(df[col].unique())
    categories_order = sorted(list(categories))

    expanded_flows = []
    for _, row in df.iterrows():
        flow_tuple = tuple(row[col] for col in level_columns)
        expanded_flows.extend([flow_tuple] * row["count"])

    if colors is None:
        colors = {}

    if categories_labels is None:
        categories_labels = {cat: str(cat) for cat in categories}

    if categories_icons is None:
        categories_icons = {cat: "circle" for cat in categories}

    grapher = FlowChartGrapher[str](output_dir)

    return grapher.make_multilevel_chart(
        file_name=file_name,
        title=title,
        flows=expanded_flows,
        level_titles=level_columns,
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons=categories_icons,
    )


def main():
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    csv_path = input("Enter CSV file path: ")

    output_file = create_flow_chart_from_csv(
        csv_path=csv_path,
        output_dir=output_dir,
        file_name="zone_flow_chart",
        title="Zone Flow Analysis",
    )

    print(f"Flow chart generated: {output_file}")


if __name__ == "__main__":
    main()

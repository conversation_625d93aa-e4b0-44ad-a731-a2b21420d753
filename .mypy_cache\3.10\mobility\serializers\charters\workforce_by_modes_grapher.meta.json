{"data_mtime": 1752154446, "dep_lines": [7, 10, 15, 26, 11, 12, 14, 27, 5, 6, 13, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.ideal_scenario_indicator", "mobility.ir.indicators.scenario_indicators", "mobility.serializers.charters.svg_interface", "mobility.serializers.charters.svg_library", "mobility.ir.transport", "mobility.ir.work_mode", "mobility.serializers.chart_writer", "mobility.workers.color_picker", "mobility.constants", "mobility.funky", "mobility.quantity", "os", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "configparser", "enum", "mobility.ir", "mobility.ir.indicators", "mobility.workers", "types", "typing_extensions"], "hash": "690046f0c011306a9679df7c4fbbe8d305b08c62", "id": "mobility.serializers.charters.workforce_by_modes_grapher", "ignore_all": false, "interface_hash": "6993d4b10b2db9684e9d5d1a0091fde9367b84af", "mtime": 1722327418, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\charters\\workforce_by_modes_grapher.py", "plugin_data": null, "size": 13009, "suppressed": [], "version_id": "1.16.1"}
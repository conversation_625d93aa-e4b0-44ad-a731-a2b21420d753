{"data_mtime": 1752154445, "dep_lines": [9, 11, 12, 13, 14, 15, 16, 10, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 4, 5, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 10], "dependencies": ["mobility.builders.zfe_data", "mobility.ir.framing_strategies", "mobility.ir.geo_study", "mobility.serializers.chart_writer", "mobility.serializers.plotly_map_maker", "mobility.serializers.plotly_theme", "mobility.workers.color_picker", "mobility.funky", "json", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.map_elements", "mobility.serializers", "mobility.serializers.base_map_maker", "mobility.serializers.charters", "mobility.serializers.charters.svg_map_addons", "mobility.workers", "os"], "hash": "340d9e35f943379b9a3b5b105736c1a6323121c3", "id": "audit.draw_zfe_perimeters", "ignore_all": false, "interface_hash": "bb7634a51c87114b637457fddaef7a4f592c0605", "mtime": 1739465729, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "audit\\draw_zfe_perimeters.py", "plugin_data": null, "size": 5151, "suppressed": ["shapely.geometry", "fiona", "g<PERSON><PERSON><PERSON>", "geopandas"], "version_id": "1.16.1"}
{"data_mtime": 1753087662, "dep_lines": [6, 4, 5, 10, 11, 13, 3, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.staggered_hours_indicators", "mobility.ir.commute_data", "mobility.ir.cost", "mobility.ir.study", "mobility.ir.transport", "mobility.workers.cost_computer", "mobility.funky", "mobility.quantity", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "fractions", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "mobility.workers", "mobility.workers.health_cost_calculator", "numbers", "typing_extensions"], "hash": "db7b6a1f396d7acfefa759a673c77a1a2a4d8024", "id": "mobility.converters.indicators.staggered_hours", "ignore_all": false, "interface_hash": "9fe6a4d0e1a731704a9c8e9a2eed0fd1779a344f", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\converters\\indicators\\staggered_hours.py", "plugin_data": null, "size": 6884, "suppressed": [], "version_id": "1.16.1"}
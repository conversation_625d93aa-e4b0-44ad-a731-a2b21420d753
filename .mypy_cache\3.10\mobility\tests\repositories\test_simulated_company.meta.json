{"data_mtime": 1751444477, "dep_lines": [4, 5, 2, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.crit_air", "mobility.repositories.simulated_company", "unittest.mock", "typing", "unittest", "builtins", "_frozen_importlib", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.geocode_api", "api_abstraction.api.travel_time_api", "enum", "mobility.ir", "mobility.ir.company_potential", "mobility.ir.zfe", "mobility.repositories", "types"], "hash": "9b9c492ae28b23212f53b3cfaa6cce88d0f17677", "id": "mobility.tests.repositories.test_simulated_company", "ignore_all": false, "interface_hash": "b9d36bda78d9fef94c50b0faf91b23444e198385", "mtime": 1722327432, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\repositories\\test_simulated_company.py", "plugin_data": null, "size": 9140, "suppressed": [], "version_id": "1.16.1"}
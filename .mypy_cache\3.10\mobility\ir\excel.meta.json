{"data_mtime": 1753176803, "dep_lines": [8, 9, 6, 7, 12, 20, 4, 5, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.error", "mobility.ir.geo_study", "mobility.ir.study", "mobility.ir.transport", "mobility.constants", "mobility.funky", "dataclasses", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.carpool_indicators", "mobility.ir.indicators.clustering_indicators", "mobility.ir.indicators.coworking_indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.indicators.remote_scenario_indicators", "mobility.ir.scenario_data", "mobility.ir.site", "types", "typing_extensions"], "hash": "fe1780fb0312e105220b12c5c58ac0fbc89c4446", "id": "mobility.ir.excel", "ignore_all": false, "interface_hash": "75d04fea0eb2f8eabce7957684fc03a6b09598d3", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\excel.py", "plugin_data": null, "size": 31122, "suppressed": [], "version_id": "1.16.1"}
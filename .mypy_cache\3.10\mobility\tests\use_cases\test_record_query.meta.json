{"data_mtime": 1751444419, "dep_lines": [5, 6, 7, 3, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.mode_shift_readiness_classification", "mobility.repositories.abstract_repositories", "mobility.use_cases.record_query", "unittest.mock", "json", "typing", "unittest", "builtins", "_frozen_importlib", "abc", "enum", "json.encoder", "mobility.ir", "mobility.repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "typing_extensions"], "hash": "281b3a4652e72c8768bb63e08f54a7cf70398361", "id": "mobility.tests.use_cases.test_record_query", "ignore_all": false, "interface_hash": "1c3c14bbc4e6a7e4a87f69abb94066800f06dfaa", "mtime": 1722327434, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\use_cases\\test_record_query.py", "plugin_data": null, "size": 1408, "suppressed": [], "version_id": "1.16.1"}
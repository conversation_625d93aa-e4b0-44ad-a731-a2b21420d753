{"data_mtime": 1752154451, "dep_lines": [21, 7, 8, 9, 10, 20, 2, 11, 19, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["webservice.apps.routes.simone", "mobility.ir.cost", "mobility.ir.geo_study", "mobility.ir.site", "mobility.ir.transport", "webservice.apps.cartographer", "unittest.mock", "mobility.quantity", "webservice.apps", "typing", "unittest", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "abc", "enum", "flask", "flask.app", "flask.helpers", "flask.testing", "flask.wrappers", "mobility", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.indicators", "mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.webstudy", "typing_extensions", "webservice.apps.routes", "werkzeug", "werkzeug.test", "werkzeug.wrappers"], "hash": "2a76f9175a4ba05acd0421a07b25f72737e280fd", "id": "webservice.tests.apps.routes.test_simone", "ignore_all": false, "interface_hash": "c3cd09d40bf837130091b334a8b1380b15fe4f1a", "mtime": 1722327437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\tests\\apps\\routes\\test_simone.py", "plugin_data": null, "size": 48037, "suppressed": ["plotly"], "version_id": "1.16.1"}
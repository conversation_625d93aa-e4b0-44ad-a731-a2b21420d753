{"data_mtime": 1752154447, "dep_lines": [6, 8, 9, 5, 10, 11, 7, 12, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.converters.indicators.scores", "mobility.ir.indicators.critical_cases", "mobility.ir.indicators.scenario_indicators", "mobility.converters.accessibility", "mobility.ir.study", "mobility.ir.transport", "mobility.funky", "mobility.quantity", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.converters", "mobility.converters.indicators", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators.scores", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.study_types", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "typing_extensions"], "hash": "72c8f6d4b86142103ba5c15731dd258af9eddc4e", "id": "mobility.ir.indicators.inter_scenario_indicators", "ignore_all": false, "interface_hash": "b7f71eb0118ce0f6a778a4e9e114d5ee039332e5", "mtime": 1742296761, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\inter_scenario_indicators.py", "plugin_data": null, "size": 15684, "suppressed": [], "version_id": "1.16.1"}
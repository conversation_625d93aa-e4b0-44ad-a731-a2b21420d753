{".class": "MypyFile", "_fullname": "api_abstraction.navitia.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiFail": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiFail", "kind": "Gdef"}, "ApiInapt": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiInapt", "kind": "Gdef"}, "ApiTimeout": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.api.ApiTimeout", "kind": "Gdef"}, "DateTimeInterval": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.date_iterators.DateTimeInterval", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "GeoCoordinates": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.geo_study.GeoCoordinates", "kind": "Gdef"}, "JOURNEY_BICYCLE_PATH": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.navitia.settings.JOURNEY_BICYCLE_PATH", "kind": "Gdef"}, "JOURNEY_DIRECT_PATH": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.navitia.settings.JOURNEY_DIRECT_PATH", "kind": "Gdef"}, "JOURNEY_FORCE_PT_PATH": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.navitia.settings.JOURNEY_FORCE_PT_PATH", "kind": "Gdef"}, "JourneyAttribute": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.JourneyAttribute", "kind": "Gdef"}, "LineString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.LineString", "name": "LineString", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.navitia.api.LineString", "source_any": null, "type_of_any": 3}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MAX_DURATION_TO_PT": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.navitia.settings.MAX_DURATION_TO_PT", "kind": "Gdef"}, "NavitiaTravelTimeAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["api_abstraction.api.travel_time_api.TravelTimeApi"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI", "name": "NavitiaTravelTimeAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.navitia.api", "mro": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "api_abstraction.api.travel_time_api.TravelTimeApi", "api_abstraction.api.api.AbstractAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "reporter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "reporter"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "builtins.str", "api_abstraction.api.event_reporter.EventReporter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NavitiaTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_journey_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["params", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._add_journey_mode", "name": "_add_journey_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "mode"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_journey_mode of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._add_journey_mode", "name": "_add_journey_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "mode"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_journey_mode of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_call_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "params", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._call_requests", "name": "_call_requests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "params", "headers"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_call_requests of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_choose_fastest_journey": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._choose_fastest_journey", "name": "_choose_fastest_journey", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_choose_fastest_journey of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_choose_request_date_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "coords", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._choose_request_date_param", "name": "_choose_request_date_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "coords", "mode"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_choose_request_date_param of NavitiaTravelTimeAPI", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_isochrone_geojson": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "boundary", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._extract_isochrone_geojson", "name": "_extract_isochrone_geojson", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "boundary", "response"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_isochrone_geojson of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_coverage_validity_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_coverage_validity_range", "name": "_get_coverage_validity_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_coverage_validity_range of NavitiaTravelTimeAPI", "ret_type": {".class": "TupleType", "implicit": false, "items": ["datetime.datetime", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_coverage_validity_range", "name": "_get_coverage_validity_range", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["datetime.datetime", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_get_isochrone_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "destination", "boundaries", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_isochrone_params", "name": "_get_isochrone_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "destination", "boundaries", "mode"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "mobility.ir.transport.TransportMode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_isochrone_params of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_journey_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_journey_distance", "name": "_get_journey_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_journey_distance of NavitiaTravelTimeAPI", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_journey_emissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_journey_emissions", "name": "_get_journey_emissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_journey_emissions of NavitiaTravelTimeAPI", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_journey_total_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_journey_total_duration", "name": "_get_journey_total_duration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "journey"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_journey_total_duration of NavitiaTravelTimeAPI", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "mode", "arrival_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_params", "name": "_get_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "origin", "destination", "mode", "arrival_time"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_params of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_section_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._get_section_distance", "name": "_get_section_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_section_distance of NavitiaTravelTimeAPI", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_inaptitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._identify_inaptitude", "name": "_identify_inaptitude", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_identify_inaptitude of NavitiaTravelTimeAPI", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_error_if_no_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["response", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._raise_error_if_no_key", "name": "_raise_error_if_no_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["response", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raise_error_if_no_key of NavitiaTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._raise_error_if_no_key", "name": "_raise_error_if_no_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["response", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raise_error_if_no_key of NavitiaTravelTimeAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_request_coverage_validity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._request_coverage_validity", "name": "_request_coverage_validity", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["url", "params", "headers"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request_isochrone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._request_isochrone", "name": "_request_isochrone", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["url", "params", "headers"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request_travel_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._request_travel_time", "name": "_request_travel_time", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["url", "params", "headers"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI._time", "name": "_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "origin", "destination", "mode", "arrival_time", "max_transfers"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_time of NavitiaTravelTimeAPI", "ret_type": "api_abstraction.api.travel_time_api.JourneyAttribute", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_isochrone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.compute_isochrone", "name": "compute_isochrone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "territory", "destination", "transport_mode", "boundary"], "arg_types": ["api_abstraction.navitia.api.NavitiaTravelTimeAPI", "mobility.ir.territory.Territory", "mobility.ir.geo_study.GeoCoordinates", "mobility.ir.transport.TransportMode", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_isochrone of NavitiaTravelTimeAPI", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "coverage_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.coverage_url", "name": "coverage_url", "setter_type": null, "type": "builtins.str"}}, "get_region_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["territory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.get_region_id", "name": "get_region_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["territory"], "arg_types": ["mobility.ir.territory.Territory"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_region_id of NavitiaTravelTimeAPI", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.get_region_id", "name": "get_region_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["territory"], "arg_types": ["mobility.ir.territory.Territory"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_region_id of NavitiaTravelTimeAPI", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isochrone_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.isochrone_url", "name": "isochrone_url", "setter_type": null, "type": "builtins.str"}}, "journey_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.journey_url", "name": "journey_url", "setter_type": null, "type": "builtins.str"}}, "minimum_validity_range_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.minimum_validity_range_duration", "name": "minimum_validity_range_duration", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "request_date_param": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.request_date_param", "name": "request_date_param", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "unhandled_modes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.unhandled_modes", "name": "unhandled_modes", "setter_type": null, "type": {".class": "Instance", "args": ["mobility.ir.transport.TransportMode"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.navitia.api.NavitiaTravelTimeAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.navitia.api.NavitiaTravelTimeAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Territory": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.territory.Territory", "kind": "Gdef"}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.navitia.api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "closest_tuesday_8_30_in_interval": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.hypotheses.closest_tuesday_8_30_in_interval", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "geopy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "api_abstraction.navitia.api.geopy", "name": "geopy", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "api_abstraction.navitia.api.geopy", "source_any": null, "type_of_any": 3}}}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "api_abstraction\\navitia\\api.py"}
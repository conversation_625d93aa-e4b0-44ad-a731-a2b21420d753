Changelog
=========

3.23.0 (Unreleased)
-------------------

Features
~~~~~~~~

- Add HERE Travel Time APIs for routing and isochrone computation
  with intermodality capabilities
- Add "here" travel timer in factories
- Add script to compute BP2M indicators
- Enable flash diagnosis by default and remove old mobility report
- Compute ground emissions for upgraded flight travel timer
- Change versionning system for JSON schemas to use vM only with a single consolidated schema
- Replace Navitia api with Here for PT by default
- Parametrize max acceptable durations (Walk, Bicycle, Public Transport) for employee transport mode assignment
- Parametrize the max number of public transport transfers
- Parametrize parking costs in input file
- Add script to update PT journey with multimodal journeys
- Add employee site to the excel implantation study and diagnosis output
- Add four new modes including two intermodal
- Parametrize isochrone api by mode

Bug Fixes
~~~~~~~~~

- Use declared mode unconditionally when assigning mode with od_matrix form assigner
- Fix old json format support when building study from json
- Fix retime taking into account the study arrival time
- Fix HERE token passing in travel timer when running excel study
- Fix HERE travel time to take in wait times during transit
- Fix mas transfers passing to composed travel time APIs
- Fix geocoder configuration in clustering
- Separate infrastructure and commute costs in parking cost computation

3.22.0 (2024-03-13)
-------------------

Features
~~~~~~~~

- Add Google Routes API to compute travel times
- Add airplane mode and flight traveltimer
- Add rule based transport mode assigner with mode shares
- Add entry point for carbon emission computation

Bug Fixes
~~~~~~~~~

- Fix json schema use correct employee definitions in study v13 & v14
- Fix modal share display
- Fix recomputation of already chosen car duration when iterating on carpooling
- Make accessibility slides single-site
- Update ZFE data in Lyon
- Adjust cost computation in carpooling
- Fix mode flows representation in ideal scenario synthesis

3.21.0 (2024-12-18)
-------------------

Features
~~~~~~~~

- Display scale and north arrow in maps
- Remove employee by nickname or distance in script
- Add carbon objective source for FR

Bug Fixes
~~~~~~~~~

- Prevent RTE if POI timing are missing

Internal changes
~~~~~~~~~~~~~~~~

- Update API choice wrt PT
- Remove ZFE definition for Le Mans

3.20.0 (2024-11-18)
-------------------

Features
~~~~~~~~

- Parametrize study data and POIs as input for the diagnosis report

Internal changes
~~~~~~~~~~~~~~~~

- Remove the most ambitious scenario for PT and Bike shift
- Add missing wording in indicators

3.19.0 (2024-10-24)
-------------------

Features
~~~~~~~~

- Display mission id from study data in pdf reports
- Script comparison between OTP and various Google traffic flavours
- Script blackbox testing of OTP to compare duration computation vs ways parameters
- Add ArcGIS engine for producing vector maps
  - Integrate the framing strategies to the ArcGIS Map maker engine
  - Add annotations to the ArcGIS Map maker engine
  - Integrate the ArcGIS Map maker engine to the chart tracer
  - Integrate legends and titles to arcgis maps
  - Use svg symbols recorded in library for amenities
- Add new modes carpooling, electric bike, electric car and motorcycle in run_study
  - Adapt travel timers and emission computer for new modes
  - Count global motor emissions as part of reported TIM emissions
- Include POIs for Neuchatel (CH)
- Reduce number of carpooler found in carpooling indicator
  - Increase available parameters for carpool computer
  - Base potentials carpoolers on their current bike or car duration
- Add circle/donut/slice to svg interface
- Make pretty pie charts for emissions and modal share with svg interface
- Switch between euros and chf for currency based on territory's country
- Add style for each layer in geopackage
- Improve framing of fragmented isochrones by allowing removal of small islands

Bug Fixes
~~~~~~~~~

- Fix deprecation warnings in flask sqlalchemy
- Fix discarded conditional formatting not compatible with openpyxl
- Fix carpool emission and duration computation
- Use values types in geopackage schema definition
- Fix wrong measurements of aggregated line in SVGTextBlock
- Make pt commute as map commute if map is faster than pt
- Output only one geopackage feature per couple poi/site
- Fix output of modal share geopackage features in multi scenarios
- Choose pt stops position based on important related lines from OSM
- Restrict displayed POIs to the one in Territory, not in study
- Remove zoomed carpool map with few carpool groups
- Draw green bicycle lines and lanes
- Improve PT infrastructure map's readability

3.18.1 (2024-09-05)
-------------------

Bug Fixes
~~~~~~~~~

- Separate multiple scenarios time travel violins in location study

3.18.0 (2024-09-04)
-------------------

Features
~~~~~~~~

- Define a new diagnosis report for preliminary mobility study analysis
- Integrate ideal scenario computation in geopackage and diagnosis pdf report
- Add territories for Sonceboz and Boncourt

Bug Fixes
~~~~~~~~~

- Fix text block splitting too long first word
- Prevent non remote employees to cowork

3.17.1 (2024-07-11)
-------------------

Bug Fixes
~~~~~~~~~

- Fix poi accessibility table when many POIs make it too long for the page

3.17.0 (2024-07-10)
-------------------

Features
~~~~~~~~

- Add script to compute number of points close to features
- Add script to graph reachable number of people from train stops in line
- Add script to modify the transport mode used by employees in scenarios in consolidated study
- Add script to compute population inside and outside of modal polygons
- Add script to compute number of similar addresses in csv address listing
- Add script to geocode addresses contained in a csv file
- Update POIs map
- Add option to regeocode employees by id in regeocode script
- Update Geneve POIs for OIM

Bug Fixes
~~~~~~~~~

- Fix excel templates default positioning
- Remove all employee's commute data when regeocoding their address

3.16.0 (2024-06-10)
-------------------

Features
~~~~~~~~

- Add svg graph maker for bar graphs with threshold line
- Update look of carbon comparison graph
- Add territory definitions for La Chaux-de-Fonds

Bug Fixes
~~~~~~~~~

- Fix look of diagnosis report mainly related to issues with many sites in scenario
- Fudge boundary of bike and walk isochrones to compensate static error in OTP algorithm

3.15.0 (2024-05-29)
-------------------

Features
~~~~~~~~

- Add Delemont territory definition and POIs
- Improve carbon emission slide with comparison of current and future emission obj
- Add Countries and link them to territories in the study
- Add walk mode shift section and accessibility map in diagnosis
- Add bicycle accessibility map in diagnosis in bike shift scenario section
- Add pt accessibility map in diagnosis in pt shift scenario section

Bug Fixes
~~~~~~~~~

- Fix Poi updating when building json study with an empty POI scenarios list
- Fix line amenities construction with multilinestrings

3.14.1 (2024-05-16)
-------------------

Bug Fixes
~~~~~~~~~

- Remove flat penalty in distance travel timer when computing detours
  since it's added later in the carpooling computer

3.14.0 (2024-05-16)
-------------------

Features
~~~~~~~~

- Add survey-based transport mode assigner
- Add information to the GIS geopackage serializer (v2)

Bug Fixes
~~~~~~~~~

- Fix building of internal graph when speed is zero

3.13.0 (2024-04-19)
-------------------

Features
~~~~~~~~

- Add geocoder api for Luxembourg
- Modify study and diagnosis reports with a more citec compliant format
- Add script to compare studies employees (adresses and modes) to check application of diffs
- Add HERE geocoder
- Add Luxembourg territory definition and POIs
- Add possibility to time only a certain amount of commutes
- Add script to make sub studies based on employees grouped by address
- Add script to filter out employees too far from their current site
- Add script to compute time diffs based on employees address and teams groupings
- Add script to report and fix different commute data on identical ODs
- Add script to create single scenario diagnosis jsons from location study scenarios
- Add script to make study where all employees use public transport
- Add script to make geojson file containing a study's isochrones
- Add script for study validation
- Add script to update transport mode used by employees
- Add script to remove commute data in json study
- Add script to produce a CSV summary of study by city and mode
- Add Clermont-Ferrand territory definition and POIs
- Rename modal shift scenarios
- Add script to change address of employees
- Add script to compute isochrones and save it as geojson
- Include stops from osm when they are not connected to lines
- Add Geopackage serializer for outputting study data for GIS software
- Improve itinerary selection with fastest in OTPTravelTimeAPI
- Add script to recompute geocoding failures in a study
- Add manual geocoder
- Add Geneve territory definition and POIs
- Adapt each app in webservice to abiBOX interface

Bug Fixes
~~~~~~~~~

- Fix failure in json study parser when sites were removed manually in json
- Fix excel templates incompatibility with new excel versions
- Fix wrong escaping of html sequence in svg interface
- Fix google API failure to narrow departure time when morning peak is too high
- Fix parsing of FailedGeoEmployee with no transport mode
- Fix parsing of GeoCoordinates formats not recognized
- Fix scenario names in excel template causing cost computation to fail
- Fix public transport time by bounding it with walking time

Internal changes
~~~~~~~~~~~~~~~~

- Change default travel timer to include OTP API call for PT

3.12.5 (2023-10-23)
-------------------

Bug Fixes
~~~~~~~~~

- Fix typo in car dependency study

3.12.4 (2023-09-14)
-------------------

Bug Fixes
~~~~~~~~~

- Fix display in costs graph
- Add citation in car dependency study

3.12.3 (2023-05-30)
-------------------

Bug Fixes
~~~~~~~~~

- Use correct soft link to simone from simone-preview
- Update sitemap to include onboarding and car dependency study

3.12.2 (2023-05-14)
-------------------

Features
~~~~~~~~

- Record queries to car dependency study

Bug Fixes
~~~~~~~~~

- Handle from_json failures in ModeShiftReadiness repository

3.12.1 (2023-05-12)
-------------------

Tweaks
~~~~~~

- Rework presentation of car dependency study

3.12.0 (2023-05-11)
-------------------

Features
~~~~~~~~

- Display results for car dependency study
- Record and display statistics for car dependency study
- Add main page for apps

Bug Fix
~~~~~~~

- Prevent access to onboarding results without email

3.11.0 (2023-04-06)
------------------

Features
~~~~~~~~

- Evaluate car dependency with new app
- Ask for email before onboarding results
- Prettify queries dashboard

3.10.1 (2023-02-14)
------------------

Bug Fix
~~~~~~~

- Geocode company address in the first step of onboarding form
- Display geocoding error when it happens
- Adjust display width of onboarding

3.10.0 (2023-02-10)
------------------

Features
~~~~~~~~

- Conclude diagnosis report with ideal scenario description
- Add onboarding app webservice
- Add scripts for creating and modifying GTFS

3.9.0 (2022-09-05)
------------------

Features
~~~~~~~~

- Remove useless pages from diagnosis report
- Display executive summary in diagnosis report
- Add scripts to update or modify studies from json

Bug Fix
~~~~~~~

- Validate api_key correctly against list of authorized keys

3.8.0 (2022-08-29)
------------------

Features
~~~~~~~~

- Add cache to company potential simulation API

3.7.0 (2022-08-11)
------------------

Features
~~~~~~~~

- Display number of employees with inferred modes in reports
- Provide company simulation API computing carbon oriented potential

Internal Changes
~~~~~~~~~~~~~~~~

- Compute ideal scenario from current diagnosis indicators

3.6.1 (2022-06-08)
------------------

Bug Fix
~~~~~~~

- Fix chart tracer missing its zfe_computer

3.6.0 (2022-06-08)
------------------

Features
~~~~~~~~

- Compute ZFE impact on company in diagnosis

Bug Fix
~~~~~~~

- Fix wording and spacing

Internal Changes
~~~~~~~~~~~~~~~~

- Add assembler webservice for managing territories deployment
- Add journey computation in assembler

3.5.2 (2022-04-06)
------------------

Internal Changes
~~~~~~~~~~~~~~~~

- Allow cross origin requests to use session
- Prevent disconnect issue on pythonanywhere with db pooling delay

3.5.1 (2022-04-06)
------------------

Internal Changes
~~~~~~~~~~~~~~~~

- Use config from os environ in demonstrator
- Update db v1 to use compatible collate format

3.5.0 (2022-04-06)
------------------

Features
~~~~~~~~

- Record demonstrator queries
- Provide a UI in demonstrator to consult those queries

3.4.0 (2022-03-31)
------------------

Features
~~~~~~~~

- Compute costs of various scenarios in diag and study
- Create SVG graphs with internal lib SVGInterface
- Add costs data to pdf and excel reports
- Use new computed costs in Simone car km costs
- Provide ZFE impact API and worker

3.3.0 (2022-01-07)
------------------

Features
~~~~~~~~

- Allow addition of custom public transport lines
- Add bicycle lines to infrastructure
- Add OURA Api
- Compute Crit'Air category of vehicle of employees
- Update SimOne calls to action

Bug Fix
~~~~~~~

- Fix bicycle ways extraction missing some from OSM data
- Consider employees that have no change in their travel time only as gaining time

Internal Changes
~~~~~~~~~~~~~~~~

- Allow addition of 0 (adimensional) to any Quantity
- Add computers of data for Lyon ZFE impacts
- Simplify infrastructure lines storage to speedup map generation

3.2.0 (2021-09-22)
------------------

Features
~~~~~~~~

- Add coworking page in diagnosis

Bug Fix
~~~~~~~

- Compute journey distances based on geojson path in Navitia API
- Fix some wording in "from departments"

Internal Changes
~~~~~~~~~~~~~~~~

- Add sitemap served as static file in SimOne
- Reorganize jinja templates
- Reorganize json schemas
- Refactor indicators and integrate coworking scenario in normal process

3.1.0 (2021-06-18)
------------------

Features
~~~~~~~~

- Store default web study for direct access through "app.modelity.fr/?preview"

Bug Fix
~~~~~~~

- Fix not initializing site address in study request form

3.0.0 (2021-06-17)
------------------

Features
~~~~~~~~

- Compute webstudy for pre-study webservice
- Add internal API and scripts for making network graphs
- Integrate SimOne in available webservices
- Compute coworking scenarios based on coworking sites list

Internal Changes
~~~~~~~~~~~~~~~~

- Use database to store buildings and commute territory data
- Rework carpooling algorithm with computation of path detours

Bug Fix
~~~~~~~

- Fix stats mode assigner so it respects the declared employee transport mode.


2.4.0 (2021-03-26)
------------------

Features
~~~~~~~~

- Add OSM data to reports for pt, bike and car amenities
- Create hooker report
- Create study from single site address
- Infer transport modes from statistical data

2.3.1 (2020-01-28)
------------------

Bug Fix
~~~~~~~

- Remove unreliable outlines on map points
- Fix display of isochrones unions from 30 to 60 minutes intersecting with isochrones unions <30min

2.3.0 (2020-01-28)
------------------

Features
~~~~~~~~

- Add multiplexing API to select fastest route returned by several APIs
- Rework location report `valorisation` part with valuable data from `implantation`

Bug Fix
~~~~~~~

- Fix OTP API returning journeys with low walking distances
- Fix typos in report
- Remove locations comparison in location report when only one scenario is studied

2.2.0 (2020-01-14)
------------------

Features
~~~~~~~~

- Interface study process with flask webservice
- Automate map charts with MapMaker
- Adapt excel input and reports format to multi-site scenarios
- Add BeautifulSoup API
- Add Arras territory

2.1.1 (2020-01-09)
------------------

Bug Fix
~~~~~~~

- Switch to openpyxl to read excel following xlrd dropping support of xlsx format
- Fix openpyxl usage through pandas to read excel

2.1.0 (2020-10-20)
------------------

Features
~~~~~~~~

- Include 10 largest cities in control commutes
- Interface OpenTripPlanner local server through new TravelTimeAPI

Bug Fix
~~~~~~~

- Recompute every travel data if any is missing (with recompute_times)
- Fix edge cases of crash during report generation

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Delay indicators creation to when they are used

2.0.1 (2020-09-07)
------------------

Features
~~~~~~~~

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Improve Excel Diagnosis style
- Divide by 2 the size of the report by decreasing images quality

2.0.0 (2020-09-04)
------------------

Features
~~~~~~~~

- Add diagnosis report ability in Modelity Report Maker

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Use Ubuntu docker image in CI
- Restructure transport modes
- Implement output json version v7

1.3.2 (2020-06-15)
------------------

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Mention public transports and times in isochrone title legends
- Add link to customer satisfaction survey
- Add Aix TGV station to Marseille POI

1.3.1 (2020-05-28)
------------------

Bug Fixes
~~~~~~~~~

- Fix legend of graph diff_times_stacked_bars
- Compute emission from distance for public transport journeys
- Construct dedicated map title in chart tracer
- Reword HR risk to HR score where relevant

1.3.0 (2020-05-19)
------------------

Features
~~~~~~~~

- Add control commutes to check API timings on territory
- Add isochrones to report
- Add GHG emission audit to report


1.2.0 (2020-04-20)
------------------

Features
~~~~~~~~

- Add accessibility section in report
- Refine automatic transport mode choosing model

Bug Fixes
~~~~~~~~~

- Fix Json schema 1.0 to accept failed geocode sites and employees
- Fix misleading display of percentage close to 100
- Display two decimal numbers in Excel percentages
- Modify contact email in report last page

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Add generate_territory_addresses script
- Homogenize percent formatting
- Create Json V2 format

1.1.1 (2020-04-01)
------------------

Bug Fixes
~~~~~~~~~

- Display times in minutes in Excel report

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Keep company name if it is upper case
- Lighten report size by replacing SVG map images by JPEG

1.1.0 (2020-04-01)
------------------

Features
~~~~~~~~

- Allow optional transport mode in input file
- Add present site section in PDF report
- Add access time for 90% of employees indicator
- Show site address as chapter subtitle
- Plot employees and sites maps
- Add median indicator

Trivial / Internal Changes
~~~~~~~~~~~~~~~~~~~~~~~~~~

- Refactor study types with structural Scenarios objects
- Dump internal data as json along user dedicated outputs
- Add versioning to outputs
- Do not automatically choose bike mode when travel time is too long


1.0.0 (2020-03-18)
------------------

Features
~~~~~~~~

- Generate PDF Mobility Report
- Generate Excel Mobility Report

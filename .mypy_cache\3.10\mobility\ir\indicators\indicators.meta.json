{"data_mtime": 1752154448, "dep_lines": [7, 10, 13, 14, 15, 18, 19, 4, 6, 20, 21, 22, 23, 5, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.bicycle_infrastructure_indicators", "mobility.ir.indicators.car_infrastructure_indicators", "mobility.ir.indicators.inter_scenario_indicators", "mobility.ir.indicators.poi_scenario_indicators", "mobility.ir.indicators.pt_infrastructure_indicators", "mobility.ir.indicators.scenario_indicators", "mobility.ir.indicators.study_indicators", "mobility.converters.accessibility", "mobility.ir.cost", "mobility.ir.infrastructure", "mobility.ir.scenario_data", "mobility.ir.study", "mobility.ir.transport", "mobility.funky", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.converters", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.indicators.critical_cases", "mobility.ir.poi", "mobility.ir.site", "mobility.ir.study_types", "mobility.quantity", "typing_extensions"], "hash": "d5e5218c16e349286d29140952f1232b271e2bdc", "id": "mobility.ir.indicators.indicators", "ignore_all": false, "interface_hash": "2bd1840c52c2b6ed181939b3f04cfa94bfbb37ec", "mtime": 1730103305, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\ir\\indicators\\indicators.py", "plugin_data": null, "size": 17144, "suppressed": [], "version_id": "1.16.1"}
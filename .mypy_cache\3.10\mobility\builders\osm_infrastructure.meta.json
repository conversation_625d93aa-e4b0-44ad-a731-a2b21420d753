{"data_mtime": 1752056503, "dep_lines": [14, 15, 24, 7, 8, 13, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 6, 9, 10, 11, 5], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5, 5, 5, 20], "dependencies": ["mobility.ir.geo_study", "mobility.ir.infrastructure", "mobility.ir.transport", "osmium._osmium", "osmium.geom", "mobility.funky", "collections", "typing", "osmium", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "mobility.ir", "osmium.osm", "osmium.osm._osm", "osmium.osm.types", "osmium.simple_handler", "typing_extensions"], "hash": "dc2372b624cf21b72736a3f0286e1390d0672348", "id": "mobility.builders.osm_infrastructure", "ignore_all": false, "interface_hash": "52255d59602884961518c9debcaadc62d7eb9f31", "mtime": 1752053388, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\builders\\osm_infrastructure.py", "plugin_data": null, "size": 29119, "suppressed": ["shapely.wkb", "geopy.distance", "scipy.spatial", "shapely.geometry", "shapely.prepared", "shapely"], "version_id": "1.16.1"}
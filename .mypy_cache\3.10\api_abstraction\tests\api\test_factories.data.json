{".class": "MypyFile", "_fullname": "api_abstraction.tests.api.test_factories", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EventReporter": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.event_reporter.EventReporter", "kind": "Gdef"}, "FastestChoiceTravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.fastest_choice_travel_time_api.FastestChoiceTravelTimeApi", "kind": "Gdef"}, "FlightComputerApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.flight.flight_travel_time.FlightComputerApi", "kind": "Gdef"}, "GoogleTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.google.api.GoogleTravelTimeAPI", "kind": "Gdef"}, "HereTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.here.api.HereTravelTimeAPI", "kind": "Gdef"}, "ModalTravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.modal_travel_time_api.ModalTravelTimeApi", "kind": "Gdef"}, "OTPTravelTimeAPI": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.otp.api.OTPTravelTimeAPI", "kind": "Gdef"}, "TestTravelTimeApiMaker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", "name": "TestTravelTimeApiMaker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "api_abstraction.tests.api.test_factories", "mro": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", "builtins.object"], "names": {".class": "SymbolTable", "test_can_make_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_can_make_default", "name": "test_can_make_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_can_make_default of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_can_make_distance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_can_make_distance", "name": "test_can_make_distance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_can_make_distance of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_can_make_here": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_can_make_here", "name": "test_can_make_here", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_can_make_here of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_can_make_trivial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_can_make_trivial", "name": "test_can_make_trivial", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_can_make_trivial of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_can_make_with_flight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sample_csv_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_can_make_with_flight", "name": "test_can_make_with_flight", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sample_csv_path"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_can_make_with_flight of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_default_defines_an_api_for_each_modes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_default_defines_an_api_for_each_modes", "name": "test_default_defines_an_api_for_each_modes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_default_defines_an_api_for_each_modes of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_default_has_correct_type_for_each_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_default_has_correct_type_for_each_mode", "name": "test_default_has_correct_type_for_each_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_default_has_correct_type_for_each_mode of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_default_public_transport_is_fastest_of_main_apis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_default_public_transport_is_fastest_of_main_apis", "name": "test_default_public_transport_is_fastest_of_main_apis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_default_public_transport_is_fastest_of_main_apis of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_factory_fails_on_unvalid_kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_factory_fails_on_unvalid_kind", "name": "test_factory_fails_on_unvalid_kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_factory_fails_on_unvalid_kind of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_isochrone_api_is_correct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_isochrone_api_is_correct", "name": "test_isochrone_api_is_correct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_isochrone_api_is_correct of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_with_flight_has_correct_type_for_each_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sample_csv_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_with_flight_has_correct_type_for_each_mode", "name": "test_with_flight_has_correct_type_for_each_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sample_csv_path"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_with_flight_has_correct_type_for_each_mode of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_with_flight_should_raise_on_missing_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_with_flight_should_raise_on_missing_file", "name": "test_with_flight_should_raise_on_missing_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_with_flight_should_raise_on_missing_file of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_with_flight_should_raise_on_missing_google_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.test_with_flight_should_raise_on_missing_google_key", "name": "test_with_flight_should_raise_on_missing_google_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_with_flight_should_raise_on_missing_google_key of TestTravelTimeApiMaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "api_abstraction.tests.api.test_factories.TestTravelTimeApiMaker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportMode": {".class": "SymbolTableNode", "cross_ref": "mobility.ir.transport.TransportMode", "kind": "Gdef"}, "TravelTimeApi": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.travel_time_api.TravelTimeApi", "kind": "Gdef"}, "TravelTimeApiMaker": {".class": "SymbolTableNode", "cross_ref": "api_abstraction.api.factories.TravelTimeApiMaker", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "api_abstraction.tests.api.test_factories.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "sample_csv_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "api_abstraction.tests.api.test_factories.sample_csv_path", "name": "sample_csv_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tmp_path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sample_csv_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "api_abstraction.tests.api.test_factories.sample_csv_path", "name": "sample_csv_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tmp_path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sample_csv_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "api_abstraction\\tests\\api\\test_factories.py"}
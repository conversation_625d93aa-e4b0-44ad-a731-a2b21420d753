import datetime
from typing import Dict

import pytest

import api_abstraction.tests.otp_plan_response as otp_plan
from mobility.ir.transport import TransportMode


@pytest.fixture
def navitia_inapt_response() -> Dict:
    return {
        "message": "cannot find a region with 2.3752;48.845 and 4.8236;45.7685 in the same time",
        "error": {
            "message": "cannot find a region with 2.3752;48.845 and 4.8236;45.7685 in the same time",
            "id": "unknown_object",
        },
    }


@pytest.fixture
def navitia_inapt_response_negative_coordinates():
    return {
        "message": "cannot find a region with -2.3752;-48.845 and -4.8236;-45.7685 in the same time",
        "error": {
            "message": "cannot find a region with -2.3752;-48.845 and -4.8236;-45.7685 in the same time",
            "id": "unknown_object",
        },
    }


@pytest.fixture
def navitia_out_period_response() -> Dict:
    return {
        "tickets": [],
        "links": [],
        "disruptions": [],
        "notes": [],
        "feed_publishers": [],
        "context": {
            "timezone": "Europe/Paris",
            "current_datetime": datetime.datetime.now().strftime("%Y%m%dT%H%M%S"),
        },
        "error": {
            "message": "date is not in data production period",
            "id": "date_out_of_bounds",
        },
        "exceptions": [],
    }


@pytest.fixture
def navitia_coverage_strikes_in_idf_response() -> Dict:
    return {
        "regions": [
            {
                "status": "running",
                "dataset_created_at": "20200103T181839",
                "name": None,
                "start_production_date": "20191229",
                "shape": "MULTIPOLYGON()",
                "end_production_date": "20200126",
                "last_load_at": "20200103T201624",
                "id": "fr-idf",
            }
        ],
        "links": [],
        "context": {
            "timezone": "Africa/Abidjan",
            "current_datetime": "20200107T175446",
        },
    }


@pytest.fixture
def navitia_coverage_idf_response() -> Dict:
    return {
        "regions": [
            {
                "status": "running",
                "dataset_created_at": "20200103T181839",
                "name": None,
                "start_production_date": "20191229",
                "shape": "MULTIPOLYGON()",
                "end_production_date": "20201226",
                "last_load_at": "20200103T201624",
                "id": "fr-idf",
            }
        ],
        "links": [],
        "context": {
            "timezone": "Africa/Abidjan",
            "current_datetime": "20200107T175446",
        },
    }


@pytest.fixture
def navitia_response() -> Dict:
    return {
        "tickets": [],
        "links": [
            {
                "href": "https://api.navitia.io/v1/coverage/fr-se/journeys?from=4.84826;45.74389&max_duration_to_pt=3600&datetime=20191112T083103&to=4.85679;45.76694&datetime_represents=departure&journey_qualification_process=best",
                "type": "next",
                "rel": "next",
                "templated": False,
            },
            {
                "href": "https://api.navitia.io/v1/coverage/fr-se/journeys?from=4.84826;45.74389&max_duration_to_pt=3600&datetime=20191112T085232&to=4.85679;45.76694&datetime_represents=arrival&journey_qualification_process=best",
                "type": "prev",
                "rel": "prev",
                "templated": False,
            },
            {
                "href": "https://api.navitia.io/v1/coverage/fr-se/journeys?from=4.84826;45.74389&max_duration_to_pt=3600&datetime=20191112T000000&to=4.85679;45.76694&datetime_represents=departure&journey_qualification_process=best",
                "type": "first",
                "rel": "first",
                "templated": False,
            },
            {
                "href": "https://api.navitia.io/v1/coverage/fr-se/journeys?from=4.84826;45.74389&max_duration_to_pt=3600&datetime=20191112T235959&to=4.85679;45.76694&datetime_represents=arrival&journey_qualification_process=best",
                "type": "last",
                "rel": "last",
                "templated": False,
            },
        ],
        "journeys": [
            {
                "status": "",
                "distances": {
                    "taxi": 0,
                    "car": 0,
                    "walking": 974,
                    "bike": 0,
                    "ridesharing": 0,
                },
                "links": [
                    {
                        "href": "https://api.navitia.io/v1/journeys?first_section_mode%5B%5D=walking&direct_path=none&to=4.85679%3B45.76694&max_duration_to_pt=3600&datetime_represents=departure&is_journey_schedules=True&datetime=20191112T083000&last_section_mode%5B%5D=walking&min_nb_journeys=5&min_nb_transfers=0&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A46023&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A46029&from=4.84826%3B45.74389&journey_qualification_process=best",
                        "type": "journeys",
                        "rel": "same_journey_schedules",
                        "templated": False,
                    }
                ],
                "tags": ["walking", "ecologic"],
                "nb_transfers": 0,
                "durations": {
                    "taxi": 0,
                    "walking": 871,
                    "car": 0,
                    "ridesharing": 0,
                    "bike": 0,
                    "total": 1291,
                },
                "arrival_date_time": "20191112T085233",
                "calendars": [
                    {
                        "active_periods": [{"begin": "20191112", "end": "20191127"}],
                        "week_pattern": {
                            "monday": True,
                            "tuesday": True,
                            "friday": False,
                            "wednesday": False,
                            "thursday": False,
                            "sunday": False,
                            "saturday": False,
                        },
                    }
                ],
                "departure_date_time": "20191112T083102",
                "requested_date_time": "20191112T083000",
                "fare": {"found": False, "total": {"value": "0.0"}, "links": []},
                "co2_emission": {"value": 8.829, "unit": "gEC"},
                "type": "best",
                "duration": 1291,
                "sections": [
                    {
                        "from": {
                            "embedded_type": "address",
                            "distance": "21",
                            "name": "Rue Marcel Teppaz (Lyon)",
                            "address": {
                                "name": "Rue Marcel Teppaz",
                                "house_number": 0,
                                "coord": {"lat": "45.7438786", "lon": "4.8479779"},
                                "label": "Rue Marcel Teppaz (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.7457551",
                                            "lon": "4.84189",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10682",
                                        "zip_code": "69007",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                ],
                                "id": "4.8479779;45.7438786",
                            },
                            "quality": 0,
                            "id": "4.8479779;45.7438786",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T084000",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "commercial_modes": [
                                    {"id": "commercial_mode:metro", "name": "Metro"}
                                ],
                                "name": "Jean Macé.",
                                "links": [],
                                "physical_modes": [
                                    {"id": "physical_mode:Metro", "name": "Métro"}
                                ],
                                "coord": {"lat": "45.746206", "lon": "4.842358"},
                                "label": "Jean Macé. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46029",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "administrative_regions": [
                                        {
                                            "insee": "69123",
                                            "name": "Lyon",
                                            "level": 8,
                                            "coord": {
                                                "lat": "45.757813",
                                                "lon": "4.832011",
                                            },
                                            "label": "Lyon",
                                            "id": "admin:fr:69123",
                                            "zip_code": "",
                                        },
                                        {
                                            "insee": "69387",
                                            "name": "Lyon 7e Arrondissement",
                                            "level": 9,
                                            "coord": {
                                                "lat": "45.745755",
                                                "lon": "4.841889",
                                            },
                                            "label": "Lyon 7e Arrondissement (69007)",
                                            "id": "admin:fr:69387",
                                            "zip_code": "69007",
                                        },
                                    ],
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé. (Lyon)",
                            "id": "stop_point:DGL:SP:46029",
                        },
                        "departure_date_time": "20191112T083102",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 602}],
                            "coordinates": [
                                [4.8479779908, 45.7438783041],
                                [4.8479779908, 45.7438783041],
                                [4.847977, 45.743878],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847761, 45.744022],
                                [4.847761, 45.744022],
                                [4.847657, 45.744062],
                                [4.847657, 45.744062],
                                [4.847793, 45.744216],
                                [4.847793, 45.744216],
                                [4.847713, 45.744256],
                                [4.84754, 45.7448],
                                [4.847378, 45.744868],
                                [4.847378, 45.744868],
                                [4.846626, 45.745145],
                                [4.846626, 45.745145],
                                [4.84654, 45.745176],
                                [4.845291, 45.745636],
                                [4.845291, 45.745636],
                                [4.84441, 45.745971],
                                [4.844381, 45.745982],
                                [4.844381, 45.745982],
                                [4.843835, 45.746187],
                                [4.843752, 45.746216],
                                [4.843752, 45.746216],
                                [4.843263, 45.746412],
                                [4.843226, 45.746427],
                                [4.843226, 45.746427],
                                [4.84294, 45.7465],
                                [4.84294, 45.7465],
                                [4.842906, 45.74651],
                                [4.842773, 45.74655],
                                [4.842687, 45.746577],
                                [4.842687, 45.746577],
                                [4.84264, 45.746504],
                                [4.84264, 45.746504],
                                [4.842581, 45.746416],
                                [4.842581, 45.746416],
                                [4.842446, 45.746455],
                                [4.842446, 45.746455],
                                [4.8423139011, 45.7462321393],
                                [4.842358, 45.746206],
                            ],
                        },
                        "duration": 538,
                        "path": [
                            {
                                "duration": 20,
                                "direction": 0,
                                "length": 22,
                                "name": "Rue Marcel Teppaz",
                            },
                            {
                                "duration": 4,
                                "direction": 0,
                                "length": 4,
                                "name": "Rue Garibaldi",
                            },
                            {"duration": 8, "direction": -92, "length": 9, "name": ""},
                            {"duration": 17, "direction": 93, "length": 19, "name": ""},
                            {
                                "duration": 440,
                                "direction": -56,
                                "length": 493,
                                "name": "Rue Jaboulay",
                            },
                            {
                                "duration": 16,
                                "direction": -90,
                                "length": 18,
                                "name": "Avenue Jean Jaurès",
                            },
                            {"duration": 10, "direction": 87, "length": 11, "name": ""},
                            {"duration": 23, "direction": 0, "length": 26, "name": ""},
                        ],
                        "type": "street_network",
                        "id": "section_6_0",
                        "mode": "walking",
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Jean Macé.",
                                "links": [],
                                "coord": {"lat": "45.746206", "lon": "4.842358"},
                                "label": "Jean Macé. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46029",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé. (Lyon)",
                            "id": "stop_point:DGL:SP:46029",
                        },
                        "links": [
                            {
                                "type": "vehicle_journey",
                                "id": "vehicle_journey:DGL:302A_6_2_027CT_511022-1",
                            },
                            {"type": "line", "id": "line:DGL:302Aa1"},
                            {"type": "route", "id": "route:DGL:302Ar2"},
                            {"type": "commercial_mode", "id": "commercial_mode:metro"},
                            {"type": "physical_mode", "id": "physical_mode:Metro"},
                            {"type": "network", "id": "network:tcl"},
                        ],
                        "arrival_date_time": "20191112T084700",
                        "additional_informations": ["regular"],
                        "co2_emission": {"value": 8.829, "unit": "gEC"},
                        "display_informations": {
                            "direction": "Charpennes Charles Hernu . (Villeurbanne)",
                            "code": "B",
                            "network": "TCL Sytral",
                            "links": [],
                            "color": "00FFFB",
                            "name": "Charpennes Charles Hernu . --> Gare d'Oullins",
                            "physical_mode": "Métro",
                            "headsign": "Charpennes Charles Hernu",
                            "label": "B",
                            "equipments": [],
                            "text_color": "",
                            "commercial_mode": "Metro",
                            "description": "",
                        },
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Brotteaux.",
                                "links": [],
                                "coord": {"lat": "45.766861", "lon": "4.859363"},
                                "label": "Brotteaux. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768308",
                                            "lon": "4.849432",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006)",
                                        "id": "admin:fr:69386",
                                        "zip_code": "69006",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46023",
                                "stop_area": {
                                    "name": "Brotteaux",
                                    "links": [],
                                    "coord": {"lat": "45.76683", "lon": "4.859315"},
                                    "label": "Brotteaux (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5004",
                                },
                            },
                            "quality": 0,
                            "name": "Brotteaux. (Lyon)",
                            "id": "stop_point:DGL:SP:46023",
                        },
                        "base_arrival_date_time": "20191112T084700",
                        "base_departure_date_time": "20191112T084000",
                        "departure_date_time": "20191112T084000",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 2943}],
                            "coordinates": [
                                [4.842358, 45.746206],
                                [4.846801, 45.753694],
                                [4.847564, 45.759438],
                                [4.857255, 45.761561],
                                [4.859363, 45.766861],
                            ],
                        },
                        "duration": 420,
                        "type": "public_transport",
                        "id": "section_7_0",
                        "data_freshness": "base_schedule",
                        "stop_date_times": [
                            {
                                "stop_point": {
                                    "name": "Jean Macé.",
                                    "links": [],
                                    "coord": {"lat": "45.746206", "lon": "4.842358"},
                                    "label": "Jean Macé. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46029",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084000",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084000",
                                "base_arrival_date_time": "20191112T084000",
                                "base_departure_date_time": "20191112T084000",
                            },
                            {
                                "stop_point": {
                                    "name": "Saxe - Gambetta.",
                                    "links": [],
                                    "coord": {"lat": "45.753694", "lon": "4.846801"},
                                    "label": "Saxe - Gambetta. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46040",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084200",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084200",
                                "base_arrival_date_time": "20191112T084200",
                                "base_departure_date_time": "20191112T084200",
                            },
                            {
                                "stop_point": {
                                    "name": "Place.Guichard Bourse du Travail.",
                                    "links": [],
                                    "coord": {"lat": "45.759438", "lon": "4.847564"},
                                    "label": "Place.Guichard Bourse du Travail. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46027",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084300",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084300",
                                "base_arrival_date_time": "20191112T084300",
                                "base_departure_date_time": "20191112T084300",
                            },
                            {
                                "stop_point": {
                                    "name": "Part-Dieu.",
                                    "links": [],
                                    "coord": {"lat": "45.761561", "lon": "4.857255"},
                                    "label": "Part-Dieu. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46024",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084500",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084500",
                                "base_arrival_date_time": "20191112T084500",
                                "base_departure_date_time": "20191112T084500",
                            },
                            {
                                "stop_point": {
                                    "name": "Brotteaux.",
                                    "links": [],
                                    "coord": {"lat": "45.766861", "lon": "4.859363"},
                                    "label": "Brotteaux. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46023",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084700",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084700",
                                "base_arrival_date_time": "20191112T084700",
                                "base_departure_date_time": "20191112T084700",
                            },
                        ],
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Brotteaux.",
                                "links": [],
                                "coord": {"lat": "45.766861", "lon": "4.859363"},
                                "label": "Brotteaux. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768308",
                                            "lon": "4.849432",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006)",
                                        "id": "admin:fr:69386",
                                        "zip_code": "69006",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46023",
                                "stop_area": {
                                    "name": "Brotteaux",
                                    "links": [],
                                    "coord": {"lat": "45.76683", "lon": "4.859315"},
                                    "label": "Brotteaux (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5004",
                                },
                            },
                            "quality": 0,
                            "name": "Brotteaux. (Lyon)",
                            "id": "stop_point:DGL:SP:46023",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T085233",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "address",
                            "distance": "12",
                            "name": "38 Boulevard des Brotteaux (Lyon)",
                            "address": {
                                "name": "38 Boulevard des Brotteaux",
                                "house_number": 38,
                                "coord": {"lat": "45.766885", "lon": "4.856651"},
                                "label": "38 Boulevard des Brotteaux (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768309",
                                            "lon": "4.8494324",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10679",
                                        "zip_code": "69006",
                                    },
                                ],
                                "id": "4.856651;45.766885",
                            },
                            "quality": 0,
                            "id": "4.856651;45.766885",
                        },
                        "departure_date_time": "20191112T084700",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 372}],
                            "coordinates": [
                                [4.859363, 45.766861],
                                [4.8594182525, 45.7668638168],
                                [4.859429, 45.766653],
                                [4.859442, 45.76629],
                                [4.859442, 45.76629],
                                [4.859341, 45.76629],
                                [4.859341, 45.76629],
                                [4.859343, 45.766239],
                                [4.859343, 45.766239],
                                [4.85922, 45.766242],
                                [4.85922, 45.766242],
                                [4.859075, 45.766243],
                                [4.859075, 45.766243],
                                [4.859029, 45.766244],
                                [4.859029, 45.766244],
                                [4.858979, 45.766243],
                                [4.858924, 45.766242],
                                [4.858924, 45.766242],
                                [4.85824, 45.766231],
                                [4.858136, 45.766229],
                                [4.858136, 45.766229],
                                [4.858091, 45.766225],
                                [4.858054, 45.766222],
                                [4.857586, 45.766183],
                                [4.857586, 45.766183],
                                [4.857098, 45.766142],
                                [4.856947, 45.766129],
                                [4.856947, 45.766129],
                                [4.856933, 45.766202],
                                [4.856859, 45.766588],
                                [4.856871, 45.766785],
                                [4.856871, 45.766785],
                                [4.856754, 45.766672],
                                [4.856754, 45.766672],
                                [4.856754, 45.766672],
                                [4.856743, 45.766746],
                                [4.8567085664, 45.7668980377],
                                [4.8567085664, 45.7668980377],
                            ],
                        },
                        "duration": 333,
                        "path": [
                            {"duration": 57, "direction": 0, "length": 64, "name": ""},
                            {"duration": 7, "direction": 0, "length": 8, "name": ""},
                            {"duration": 5, "direction": -92, "length": 6, "name": ""},
                            {
                                "duration": 164,
                                "direction": 94,
                                "length": 184,
                                "name": "Rue Vauban",
                            },
                            {
                                "duration": 65,
                                "direction": 89,
                                "length": 73,
                                "name": "Boulevard des Brotteaux",
                            },
                            {
                                "duration": 13,
                                "direction": -140,
                                "length": 15,
                                "name": "Rue Juliette Récamier",
                            },
                            {
                                "duration": 22,
                                "direction": 0,
                                "length": 25,
                                "name": "Rue Professeur Weill",
                            },
                        ],
                        "type": "street_network",
                        "id": "section_8_0",
                        "mode": "walking",
                    },
                ],
            },
            {
                "status": "",
                "distances": {
                    "taxi": 0,
                    "car": 0,
                    "walking": 494,
                    "bike": 0,
                    "ridesharing": 0,
                },
                "links": [
                    {
                        "href": "https://api.navitia.io/v1/journeys?first_section_mode%5B%5D=walking&direct_path=none&to=4.85679%3B45.76694&max_duration_to_pt=3600&datetime_represents=departure&is_journey_schedules=True&datetime=20191112T083000&last_section_mode%5B%5D=walking&min_nb_journeys=5&min_nb_transfers=1&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A32145&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A46023&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A32143&allowed_id%5B%5D=stop_point%3ADGL%3ASP%3A46029&from=4.84826%3B45.74389&journey_qualification_process=best",
                        "type": "journeys",
                        "rel": "same_journey_schedules",
                        "templated": False,
                    }
                ],
                "tags": ["walking", "ecologic"],
                "nb_transfers": 1,
                "durations": {
                    "taxi": 0,
                    "walking": 628,
                    "car": 0,
                    "ridesharing": 0,
                    "bike": 0,
                    "total": 1342,
                },
                "arrival_date_time": "20191112T085233",
                "calendars": [
                    {
                        "active_periods": [{"begin": "20191112", "end": "20191127"}],
                        "week_pattern": {
                            "monday": True,
                            "tuesday": True,
                            "friday": False,
                            "wednesday": False,
                            "thursday": False,
                            "sunday": False,
                            "saturday": False,
                        },
                    }
                ],
                "departure_date_time": "20191112T083011",
                "requested_date_time": "20191112T083000",
                "fare": {"found": False, "total": {"value": "0.0"}, "links": []},
                "co2_emission": {"value": 10.397, "unit": "gEC"},
                "type": "less_fallback_walk",
                "duration": 1342,
                "sections": [
                    {
                        "from": {
                            "embedded_type": "address",
                            "distance": "21",
                            "name": "Rue Marcel Teppaz (Lyon)",
                            "address": {
                                "name": "Rue Marcel Teppaz",
                                "house_number": 0,
                                "coord": {"lat": "45.7438786", "lon": "4.8479779"},
                                "label": "Rue Marcel Teppaz (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.7457551",
                                            "lon": "4.84189",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10682",
                                        "zip_code": "69007",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                ],
                                "id": "4.8479779;45.7438786",
                            },
                            "quality": 0,
                            "id": "4.8479779;45.7438786",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T083200",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "commercial_modes": [
                                    {"id": "commercial_mode:tramway", "name": "Tramway"}
                                ],
                                "name": "Garibaldi - Berthelot",
                                "links": [],
                                "physical_modes": [
                                    {"id": "physical_mode:Tramway", "name": "Tramway"}
                                ],
                                "coord": {"lat": "45.743715", "lon": "4.846903"},
                                "label": "Garibaldi - Berthelot (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:32145",
                                "stop_area": {
                                    "name": "Garibaldi - Berthelot",
                                    "links": [],
                                    "coord": {"lat": "45.743586", "lon": "4.84712"},
                                    "label": "Garibaldi - Berthelot (Lyon)",
                                    "administrative_regions": [
                                        {
                                            "insee": "69123",
                                            "name": "Lyon",
                                            "level": 8,
                                            "coord": {
                                                "lat": "45.757813",
                                                "lon": "4.832011",
                                            },
                                            "label": "Lyon",
                                            "id": "admin:fr:69123",
                                            "zip_code": "",
                                        },
                                        {
                                            "insee": "69387",
                                            "name": "Lyon 7e Arrondissement",
                                            "level": 9,
                                            "coord": {
                                                "lat": "45.745755",
                                                "lon": "4.841889",
                                            },
                                            "label": "Lyon 7e Arrondissement (69007)",
                                            "id": "admin:fr:69387",
                                            "zip_code": "69007",
                                        },
                                    ],
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5307",
                                },
                            },
                            "quality": 0,
                            "name": "Garibaldi - Berthelot (Lyon)",
                            "id": "stop_point:DGL:SP:32145",
                        },
                        "departure_date_time": "20191112T083011",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 122}],
                            "coordinates": [
                                [4.8479779908, 45.7438783041],
                                [4.8479779908, 45.7438783041],
                                [4.847977, 45.743878],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847604, 45.743894],
                                [4.847219, 45.743465],
                                [4.847219, 45.743465],
                                [4.847167, 45.743483],
                                [4.847167, 45.743483],
                                [4.8468599808, 45.7435832436],
                                [4.846903, 45.743715],
                            ],
                        },
                        "duration": 109,
                        "path": [
                            {
                                "duration": 20,
                                "direction": 0,
                                "length": 22,
                                "name": "Rue Marcel Teppaz",
                            },
                            {
                                "duration": 62,
                                "direction": 0,
                                "length": 69,
                                "name": "Rue Garibaldi",
                            },
                            {
                                "duration": 27,
                                "direction": 84,
                                "length": 30,
                                "name": "Avenue Berthelot",
                            },
                        ],
                        "type": "street_network",
                        "id": "section_0_0",
                        "mode": "walking",
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Garibaldi - Berthelot",
                                "links": [],
                                "coord": {"lat": "45.743715", "lon": "4.846903"},
                                "label": "Garibaldi - Berthelot (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:32145",
                                "stop_area": {
                                    "name": "Garibaldi - Berthelot",
                                    "links": [],
                                    "coord": {"lat": "45.743586", "lon": "4.84712"},
                                    "label": "Garibaldi - Berthelot (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5307",
                                },
                            },
                            "quality": 0,
                            "name": "Garibaldi - Berthelot (Lyon)",
                            "id": "stop_point:DGL:SP:32145",
                        },
                        "links": [
                            {
                                "type": "vehicle_journey",
                                "id": "vehicle_journey:DGL:T2_76_2_230AM_031008-1",
                            },
                            {"type": "line", "id": "line:DGL:T2a1"},
                            {"type": "route", "id": "route:DGL:T2r2"},
                            {
                                "type": "commercial_mode",
                                "id": "commercial_mode:tramway",
                            },
                            {"type": "physical_mode", "id": "physical_mode:Tramway"},
                            {"type": "network", "id": "network:tcl"},
                        ],
                        "arrival_date_time": "20191112T083400",
                        "additional_informations": ["regular"],
                        "co2_emission": {"value": 1.568, "unit": "gEC"},
                        "display_informations": {
                            "direction": "Perrache (Lyon)",
                            "code": "T2",
                            "network": "TCL Sytral",
                            "links": [],
                            "color": "00FFF0",
                            "name": "Perrache. --> Saint-Priest Bel Air",
                            "physical_mode": "Tramway",
                            "headsign": "Perrache",
                            "label": "T2",
                            "equipments": [],
                            "text_color": "",
                            "commercial_mode": "Tramway",
                            "description": "",
                        },
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Jean Macé",
                                "links": [],
                                "coord": {"lat": "45.745185", "lon": "4.842301"},
                                "label": "Jean Macé (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:32143",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé (Lyon)",
                            "id": "stop_point:DGL:SP:32143",
                        },
                        "base_arrival_date_time": "20191112T083400",
                        "base_departure_date_time": "20191112T083200",
                        "departure_date_time": "20191112T083200",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 392}],
                            "coordinates": [
                                [4.846903, 45.743715],
                                [4.842301, 45.745185],
                            ],
                        },
                        "duration": 120,
                        "type": "public_transport",
                        "id": "section_1_0",
                        "data_freshness": "base_schedule",
                        "stop_date_times": [
                            {
                                "stop_point": {
                                    "name": "Garibaldi - Berthelot",
                                    "links": [],
                                    "coord": {"lat": "45.743715", "lon": "4.846903"},
                                    "label": "Garibaldi - Berthelot (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:32145",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T083200",
                                "additional_informations": [],
                                "departure_date_time": "20191112T083200",
                                "base_arrival_date_time": "20191112T083200",
                                "base_departure_date_time": "20191112T083200",
                            },
                            {
                                "stop_point": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745185", "lon": "4.842301"},
                                    "label": "Jean Macé (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:32143",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T083400",
                                "additional_informations": [],
                                "departure_date_time": "20191112T083400",
                                "base_arrival_date_time": "20191112T083400",
                                "base_departure_date_time": "20191112T083400",
                            },
                        ],
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Jean Macé",
                                "links": [],
                                "coord": {"lat": "45.745185", "lon": "4.842301"},
                                "label": "Jean Macé (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:32143",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé (Lyon)",
                            "id": "stop_point:DGL:SP:32143",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T083706",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Jean Macé.",
                                "links": [],
                                "coord": {"lat": "45.746206", "lon": "4.842358"},
                                "label": "Jean Macé. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46029",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé. (Lyon)",
                            "id": "stop_point:DGL:SP:46029",
                        },
                        "departure_date_time": "20191112T083400",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 113}],
                            "coordinates": [
                                [4.842301, 45.745185],
                                [4.842358, 45.746206],
                            ],
                        },
                        "duration": 186,
                        "transfer_type": "walking",
                        "type": "transfer",
                        "id": "section_2_0",
                    },
                    {
                        "links": [],
                        "arrival_date_time": "20191112T084000",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "departure_date_time": "20191112T083706",
                        "duration": 174,
                        "type": "waiting",
                        "id": "section_3_0",
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Jean Macé.",
                                "links": [],
                                "coord": {"lat": "45.746206", "lon": "4.842358"},
                                "label": "Jean Macé. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.745755",
                                            "lon": "4.841889",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007)",
                                        "id": "admin:fr:69387",
                                        "zip_code": "69007",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46029",
                                "stop_area": {
                                    "name": "Jean Macé",
                                    "links": [],
                                    "coord": {"lat": "45.745983", "lon": "4.842339"},
                                    "label": "Jean Macé (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5160",
                                },
                            },
                            "quality": 0,
                            "name": "Jean Macé. (Lyon)",
                            "id": "stop_point:DGL:SP:46029",
                        },
                        "links": [
                            {
                                "type": "vehicle_journey",
                                "id": "vehicle_journey:DGL:302A_6_2_027CT_511022-1",
                            },
                            {"type": "line", "id": "line:DGL:302Aa1"},
                            {"type": "route", "id": "route:DGL:302Ar2"},
                            {"type": "commercial_mode", "id": "commercial_mode:metro"},
                            {"type": "physical_mode", "id": "physical_mode:Metro"},
                            {"type": "network", "id": "network:tcl"},
                        ],
                        "arrival_date_time": "20191112T084700",
                        "additional_informations": ["regular"],
                        "co2_emission": {"value": 8.829, "unit": "gEC"},
                        "display_informations": {
                            "direction": "Charpennes Charles Hernu . (Villeurbanne)",
                            "code": "B",
                            "network": "TCL Sytral",
                            "links": [],
                            "color": "00FFFB",
                            "name": "Charpennes Charles Hernu . --> Gare d'Oullins",
                            "physical_mode": "Métro",
                            "headsign": "Charpennes Charles Hernu",
                            "label": "B",
                            "equipments": [],
                            "text_color": "",
                            "commercial_mode": "Metro",
                            "description": "",
                        },
                        "to": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Brotteaux.",
                                "links": [],
                                "coord": {"lat": "45.766861", "lon": "4.859363"},
                                "label": "Brotteaux. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768308",
                                            "lon": "4.849432",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006)",
                                        "id": "admin:fr:69386",
                                        "zip_code": "69006",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46023",
                                "stop_area": {
                                    "name": "Brotteaux",
                                    "links": [],
                                    "coord": {"lat": "45.76683", "lon": "4.859315"},
                                    "label": "Brotteaux (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5004",
                                },
                            },
                            "quality": 0,
                            "name": "Brotteaux. (Lyon)",
                            "id": "stop_point:DGL:SP:46023",
                        },
                        "base_arrival_date_time": "20191112T084700",
                        "base_departure_date_time": "20191112T084000",
                        "departure_date_time": "20191112T084000",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 2943}],
                            "coordinates": [
                                [4.842358, 45.746206],
                                [4.846801, 45.753694],
                                [4.847564, 45.759438],
                                [4.857255, 45.761561],
                                [4.859363, 45.766861],
                            ],
                        },
                        "duration": 420,
                        "type": "public_transport",
                        "id": "section_4_0",
                        "data_freshness": "base_schedule",
                        "stop_date_times": [
                            {
                                "stop_point": {
                                    "name": "Jean Macé.",
                                    "links": [],
                                    "coord": {"lat": "45.746206", "lon": "4.842358"},
                                    "label": "Jean Macé. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46029",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084000",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084000",
                                "base_arrival_date_time": "20191112T084000",
                                "base_departure_date_time": "20191112T084000",
                            },
                            {
                                "stop_point": {
                                    "name": "Saxe - Gambetta.",
                                    "links": [],
                                    "coord": {"lat": "45.753694", "lon": "4.846801"},
                                    "label": "Saxe - Gambetta. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46040",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084200",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084200",
                                "base_arrival_date_time": "20191112T084200",
                                "base_departure_date_time": "20191112T084200",
                            },
                            {
                                "stop_point": {
                                    "name": "Place.Guichard Bourse du Travail.",
                                    "links": [],
                                    "coord": {"lat": "45.759438", "lon": "4.847564"},
                                    "label": "Place.Guichard Bourse du Travail. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46027",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084300",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084300",
                                "base_arrival_date_time": "20191112T084300",
                                "base_departure_date_time": "20191112T084300",
                            },
                            {
                                "stop_point": {
                                    "name": "Part-Dieu.",
                                    "links": [],
                                    "coord": {"lat": "45.761561", "lon": "4.857255"},
                                    "label": "Part-Dieu. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46024",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084500",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084500",
                                "base_arrival_date_time": "20191112T084500",
                                "base_departure_date_time": "20191112T084500",
                            },
                            {
                                "stop_point": {
                                    "name": "Brotteaux.",
                                    "links": [],
                                    "coord": {"lat": "45.766861", "lon": "4.859363"},
                                    "label": "Brotteaux. (Lyon)",
                                    "equipments": [],
                                    "fare_zone": {"name": "0"},
                                    "id": "stop_point:DGL:SP:46023",
                                },
                                "links": [],
                                "arrival_date_time": "20191112T084700",
                                "additional_informations": [],
                                "departure_date_time": "20191112T084700",
                                "base_arrival_date_time": "20191112T084700",
                                "base_departure_date_time": "20191112T084700",
                            },
                        ],
                    },
                    {
                        "from": {
                            "embedded_type": "stop_point",
                            "stop_point": {
                                "name": "Brotteaux.",
                                "links": [],
                                "coord": {"lat": "45.766861", "lon": "4.859363"},
                                "label": "Brotteaux. (Lyon)",
                                "equipments": [],
                                "administrative_regions": [
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768308",
                                            "lon": "4.849432",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006)",
                                        "id": "admin:fr:69386",
                                        "zip_code": "69006",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.757813",
                                            "lon": "4.832011",
                                        },
                                        "label": "Lyon",
                                        "id": "admin:fr:69123",
                                        "zip_code": "",
                                    },
                                ],
                                "fare_zone": {"name": "0"},
                                "id": "stop_point:DGL:SP:46023",
                                "stop_area": {
                                    "name": "Brotteaux",
                                    "links": [],
                                    "coord": {"lat": "45.76683", "lon": "4.859315"},
                                    "label": "Brotteaux (Lyon)",
                                    "timezone": "Europe/Paris",
                                    "id": "stop_area:DGL:SA:S5004",
                                },
                            },
                            "quality": 0,
                            "name": "Brotteaux. (Lyon)",
                            "id": "stop_point:DGL:SP:46023",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T085233",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "address",
                            "distance": "12",
                            "name": "38 Boulevard des Brotteaux (Lyon)",
                            "address": {
                                "name": "38 Boulevard des Brotteaux",
                                "house_number": 38,
                                "coord": {"lat": "45.766885", "lon": "4.856651"},
                                "label": "38 Boulevard des Brotteaux (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768309",
                                            "lon": "4.8494324",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10679",
                                        "zip_code": "69006",
                                    },
                                ],
                                "id": "4.856651;45.766885",
                            },
                            "quality": 0,
                            "id": "4.856651;45.766885",
                        },
                        "departure_date_time": "20191112T084700",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 372}],
                            "coordinates": [
                                [4.859363, 45.766861],
                                [4.8594182525, 45.7668638168],
                                [4.859429, 45.766653],
                                [4.859442, 45.76629],
                                [4.859442, 45.76629],
                                [4.859341, 45.76629],
                                [4.859341, 45.76629],
                                [4.859343, 45.766239],
                                [4.859343, 45.766239],
                                [4.85922, 45.766242],
                                [4.85922, 45.766242],
                                [4.859075, 45.766243],
                                [4.859075, 45.766243],
                                [4.859029, 45.766244],
                                [4.859029, 45.766244],
                                [4.858979, 45.766243],
                                [4.858924, 45.766242],
                                [4.858924, 45.766242],
                                [4.85824, 45.766231],
                                [4.858136, 45.766229],
                                [4.858136, 45.766229],
                                [4.858091, 45.766225],
                                [4.858054, 45.766222],
                                [4.857586, 45.766183],
                                [4.857586, 45.766183],
                                [4.857098, 45.766142],
                                [4.856947, 45.766129],
                                [4.856947, 45.766129],
                                [4.856933, 45.766202],
                                [4.856859, 45.766588],
                                [4.856871, 45.766785],
                                [4.856871, 45.766785],
                                [4.856754, 45.766672],
                                [4.856754, 45.766672],
                                [4.856754, 45.766672],
                                [4.856743, 45.766746],
                                [4.8567085664, 45.7668980377],
                                [4.8567085664, 45.7668980377],
                            ],
                        },
                        "duration": 333,
                        "path": [
                            {"duration": 57, "direction": 0, "length": 64, "name": ""},
                            {"duration": 7, "direction": 0, "length": 8, "name": ""},
                            {"duration": 5, "direction": -92, "length": 6, "name": ""},
                            {
                                "duration": 164,
                                "direction": 94,
                                "length": 184,
                                "name": "Rue Vauban",
                            },
                            {
                                "duration": 65,
                                "direction": 89,
                                "length": 73,
                                "name": "Boulevard des Brotteaux",
                            },
                            {
                                "duration": 13,
                                "direction": -140,
                                "length": 15,
                                "name": "Rue Juliette Récamier",
                            },
                            {
                                "duration": 22,
                                "direction": 0,
                                "length": 25,
                                "name": "Rue Professeur Weill",
                            },
                        ],
                        "type": "street_network",
                        "id": "section_5_0",
                        "mode": "walking",
                    },
                ],
            },
            {
                "status": "",
                "distances": {
                    "taxi": 0,
                    "car": 0,
                    "walking": 2889,
                    "bike": 0,
                    "ridesharing": 0,
                },
                "tags": ["walking", "non_pt", "non_pt_walking", "ecologic"],
                "nb_transfers": 0,
                "durations": {
                    "taxi": 0,
                    "walking": 2580,
                    "car": 0,
                    "ridesharing": 0,
                    "bike": 0,
                    "total": 2580,
                },
                "arrival_date_time": "20191112T091300",
                "departure_date_time": "20191112T083000",
                "requested_date_time": "20191112T083000",
                "fare": {"found": False, "links": []},
                "co2_emission": {"value": 0.0, "unit": ""},
                "type": "non_pt_walk",
                "duration": 2580,
                "sections": [
                    {
                        "from": {
                            "embedded_type": "address",
                            "distance": "21",
                            "name": "Rue Marcel Teppaz (Lyon)",
                            "address": {
                                "name": "Rue Marcel Teppaz",
                                "house_number": 0,
                                "coord": {"lat": "45.7438786", "lon": "4.8479779"},
                                "label": "Rue Marcel Teppaz (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69387",
                                        "name": "Lyon 7e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.7457551",
                                            "lon": "4.84189",
                                        },
                                        "label": "Lyon 7e Arrondissement (69007), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10682",
                                        "zip_code": "69007",
                                    },
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                ],
                                "id": "4.8479779;45.7438786",
                            },
                            "quality": 0,
                            "id": "4.8479779;45.7438786",
                        },
                        "links": [],
                        "arrival_date_time": "20191112T091300",
                        "co2_emission": {"value": 0.0, "unit": ""},
                        "to": {
                            "embedded_type": "address",
                            "distance": "12",
                            "name": "38 Boulevard des Brotteaux (Lyon)",
                            "address": {
                                "name": "38 Boulevard des Brotteaux",
                                "house_number": 38,
                                "coord": {"lat": "45.766885", "lon": "4.856651"},
                                "label": "38 Boulevard des Brotteaux (Lyon)",
                                "administrative_regions": [
                                    {
                                        "insee": "69123",
                                        "name": "Lyon",
                                        "level": 8,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:fr:69123",
                                    },
                                    {
                                        "insee": "69M",
                                        "name": "Métropole de Lyon",
                                        "level": 6,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:4850450",
                                    },
                                    {
                                        "insee": "84",
                                        "name": "Auvergne-Rhône-Alpes",
                                        "level": 4,
                                        "coord": {
                                            "lat": "45.7578137",
                                            "lon": "4.8320114",
                                        },
                                        "label": "Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:3792877",
                                    },
                                    {
                                        "insee": "",
                                        "name": "France",
                                        "level": 2,
                                        "coord": {
                                            "lat": "48.8566969",
                                            "lon": "2.3514616",
                                        },
                                        "label": "France",
                                        "id": "admin:osm:relation:2202162",
                                    },
                                    {
                                        "insee": "69386",
                                        "name": "Lyon 6e Arrondissement",
                                        "level": 9,
                                        "coord": {
                                            "lat": "45.768309",
                                            "lon": "4.8494324",
                                        },
                                        "label": "Lyon 6e Arrondissement (69006), Lyon, Métropole de Lyon, Auvergne-Rhône-Alpes, France",
                                        "id": "admin:osm:relation:10679",
                                        "zip_code": "69006",
                                    },
                                ],
                                "id": "4.856651;45.766885",
                            },
                            "quality": 0,
                            "id": "4.856651;45.766885",
                        },
                        "departure_date_time": "20191112T083000",
                        "geojson": {
                            "type": "LineString",
                            "properties": [{"length": 2889}],
                            "coordinates": [
                                [4.8479779908, 45.7438783041],
                                [4.847977, 45.743878],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847728, 45.743984],
                                [4.847761, 45.744022],
                                [4.847761, 45.744022],
                                [4.847899, 45.744171],
                                [4.847899, 45.744171],
                                [4.848178, 45.744472],
                                [4.848178, 45.744472],
                                [4.848642, 45.744983],
                                [4.848642, 45.744983],
                                [4.848742, 45.745104],
                                [4.848742, 45.745104],
                                [4.848772, 45.745138],
                                [4.848772, 45.745138],
                                [4.8488, 45.745169],
                                [4.8488, 45.745169],
                                [4.848857, 45.745234],
                                [4.848857, 45.745234],
                                [4.848945, 45.745317],
                                [4.848945, 45.745317],
                                [4.848982, 45.745357],
                                [4.849074, 45.745459],
                                [4.849074, 45.745459],
                                [4.849161, 45.745554],
                                [4.849161, 45.745554],
                                [4.84923, 45.745627],
                                [4.84923, 45.745627],
                                [4.849322, 45.745723],
                                [4.849486, 45.745895],
                                [4.849486, 45.745895],
                                [4.850046, 45.746482],
                                [4.850046, 45.746482],
                                [4.850167, 45.746585],
                                [4.850167, 45.746585],
                                [4.850695, 45.746959],
                                [4.851416, 45.74746],
                                [4.851416, 45.74746],
                                [4.851542, 45.747545],
                                [4.85167, 45.747634],
                                [4.852119, 45.747946],
                                [4.852119, 45.747946],
                                [4.852258, 45.748051],
                                [4.852258, 45.748051],
                                [4.852488, 45.748224],
                                [4.852488, 45.748224],
                                [4.852525, 45.748256],
                                [4.853407, 45.74902],
                                [4.853483, 45.749084],
                                [4.853483, 45.749084],
                                [4.853565, 45.749167],
                                [4.853614, 45.749247],
                                [4.853709, 45.749409],
                                [4.853871, 45.749688],
                                [4.853871, 45.749688],
                                [4.853905, 45.749879],
                                [4.85391, 45.750087],
                                [4.85391, 45.750087],
                                [4.853731, 45.750611],
                                [4.853706, 45.750735],
                                [4.853706, 45.750735],
                                [4.853612, 45.751196],
                                [4.853612, 45.751196],
                                [4.8536, 45.75126],
                                [4.853575, 45.751379],
                                [4.853589, 45.751629],
                                [4.853589, 45.751629],
                                [4.853564, 45.751869],
                                [4.853546, 45.752041],
                                [4.853546, 45.752041],
                                [4.853397, 45.75281],
                                [4.85337, 45.752937],
                                [4.85337, 45.752937],
                                [4.853349, 45.753014],
                                [4.853327, 45.753101],
                                [4.853327, 45.753101],
                                [4.853234, 45.753653],
                                [4.853234, 45.753653],
                                [4.853183, 45.753982],
                                [4.853205, 45.754183],
                                [4.853205, 45.754183],
                                [4.853123, 45.754718],
                                [4.853123, 45.754718],
                                [4.853107, 45.754829],
                                [4.853107, 45.754829],
                                [4.853101, 45.754868],
                                [4.852918, 45.7561],
                                [4.852918, 45.7561],
                                [4.85275, 45.757003],
                                [4.85275, 45.757003],
                                [4.852733, 45.757113],
                                [4.852733, 45.757113],
                                [4.852644, 45.757577],
                                [4.852644, 45.757577],
                                [4.852541, 45.758111],
                                [4.852541, 45.758111],
                                [4.852442, 45.758621],
                                [4.852442, 45.758621],
                                [4.852333, 45.759229],
                                [4.852333, 45.759229],
                                [4.85232, 45.75929],
                                [4.85232, 45.75929],
                                [4.852313, 45.75933],
                                [4.852313, 45.75933],
                                [4.852301, 45.75939],
                                [4.852202, 45.75991],
                                [4.852202, 45.75991],
                                [4.852184, 45.760014],
                                [4.852184, 45.760014],
                                [4.852098, 45.7605],
                                [4.852098, 45.7605],
                                [4.852072, 45.760643],
                                [4.852072, 45.760643],
                                [4.85205, 45.760768],
                                [4.85205, 45.760768],
                                [4.851942, 45.761374],
                                [4.851942, 45.761374],
                                [4.851811, 45.762101],
                                [4.851811, 45.762101],
                                [4.851775, 45.762307],
                                [4.851717, 45.762605],
                                [4.85167, 45.76274],
                                [4.85167, 45.76274],
                                [4.851805, 45.762756],
                                [4.851805, 45.762756],
                                [4.852013, 45.762778],
                                [4.852013, 45.762778],
                                [4.851933, 45.762836],
                                [4.851886, 45.762949],
                                [4.851886, 45.762949],
                                [4.851922, 45.763043],
                                [4.852538, 45.763551],
                                [4.852651, 45.763635],
                                [4.852651, 45.763635],
                                [4.852697, 45.763673],
                                [4.852697, 45.763673],
                                [4.852765, 45.763728],
                                [4.853311, 45.764128],
                                [4.853377, 45.764178],
                                [4.853377, 45.764178],
                                [4.853426, 45.764215],
                                [4.853749, 45.764444],
                                [4.853749, 45.764444],
                                [4.853803, 45.764483],
                                [4.853987, 45.764618],
                                [4.853987, 45.764618],
                                [4.854808, 45.765214],
                                [4.854939, 45.765292],
                                [4.854939, 45.765292],
                                [4.855101, 45.765422],
                                [4.855775, 45.765928],
                                [4.855775, 45.765928],
                                [4.855761, 45.765941],
                                [4.855752, 45.765955],
                                [4.855747, 45.765971],
                                [4.855747, 45.765987],
                                [4.855752, 45.766003],
                                [4.855762, 45.766018],
                                [4.855762, 45.766018],
                                [4.855776, 45.76603],
                                [4.855793, 45.766041],
                                [4.855813, 45.766049],
                                [4.855835, 45.766053],
                                [4.855835, 45.766053],
                                [4.85586, 45.766055],
                                [4.855885, 45.766052],
                                [4.855908, 45.766046],
                                [4.855908, 45.766046],
                                [4.855986, 45.766103],
                                [4.856049, 45.766149],
                                [4.856049, 45.766149],
                                [4.856663, 45.766598],
                                [4.856717, 45.766637],
                                [4.856754, 45.766672],
                                [4.856754, 45.766672],
                                [4.856743, 45.766746],
                                [4.8567085664, 45.7668980377],
                            ],
                        },
                        "duration": 2580,
                        "path": [
                            {
                                "duration": 20,
                                "direction": 0,
                                "length": 22,
                                "name": "Rue Marcel Teppaz",
                            },
                            {
                                "duration": 1994,
                                "direction": 0,
                                "length": 2233,
                                "name": "Rue Garibaldi",
                            },
                            {
                                "duration": 23,
                                "direction": 94,
                                "length": 26,
                                "name": "Boulevard Eugène Deruelle",
                            },
                            {
                                "duration": 106,
                                "direction": -109,
                                "length": 119,
                                "name": "Rue Moncey",
                            },
                            {
                                "duration": 310,
                                "direction": -3,
                                "length": 347,
                                "name": "Rue Juliette Récamier",
                            },
                            {
                                "duration": 20,
                                "direction": -49,
                                "length": 22,
                                "name": "Place René Deroudille",
                            },
                            {
                                "duration": 85,
                                "direction": -67,
                                "length": 95,
                                "name": "Rue Juliette Récamier",
                            },
                            {
                                "duration": 22,
                                "direction": 0,
                                "length": 25,
                                "name": "Rue Professeur Weill",
                            },
                        ],
                        "type": "street_network",
                        "id": "section_9_0",
                        "mode": "walking",
                    }
                ],
            },
        ],
        "disruptions": [],
        "notes": [],
        "feed_publishers": [
            {
                "url": "navitia.io",
                "id": "fr-se",
                "license": "OpenData",
                "name": "France South East Quarter",
            },
            {
                "url": "https://www.openstreetmap.org/copyright",
                "id": "osm",
                "license": "ODbL",
                "name": "openstreetmap",
            },
            {
                "url": "https://data.grandlyon.com/",
                "id": "DGL",
                "license": "Licence engagée",
                "name": "DGL - Grand Lyon",
            },
        ],
        "context": {
            "timezone": "Europe/Paris",
            "current_datetime": "20191106T135313",
            "car_direct_path": {
                "co2_emission": {"value": 657.**********, "unit": "gEC"}
            },
        },
        "exceptions": [],
    }


@pytest.fixture
def otp_default_router_response() -> Dict:
    return {
        "routerId": "default",
        "polygon": {
            "type": "Polygon",
            "coordinates": [
                [
                    [0.0, 0.0],
                    [-9.167649, 38.739677],
                    [-6.341491, 52.251306],
                    [10.011099, 53.552107],
                    [13.368687, 52.526699],
                    [13.512894, 52.390319],
                    [49.368986, 5.901062],
                    [0.0, 0.0],
                ]
            ],
        },
        "buildTime": 1600706852269,
        "transitServiceStarts": 1597010400,
        "transitServiceEnds": 1601244000,
        "transitModes": ["RAIL", "SUBWAY", "FUNICULAR", "TRAM", "FERRY", "BUS"],
        "centerLatitude": 46.35476,
        "centerLongitude": 2.7203365,
        "hasParkRide": False,
        "travelOptions": [
            {"value": "TRANSIT,WALK", "name": "TRANSIT"},
            {"value": "RAIL,WALK", "name": "RAIL"},
            {"value": "SUBWAY,WALK", "name": "SUBWAY"},
            {"value": "FUNICULAR,WALK", "name": "FUNICULAR"},
            {"value": "TRAM,WALK", "name": "TRAM"},
            {"value": "FERRY,WALK", "name": "FERRY"},
            {"value": "BUS,WALK", "name": "BUS"},
            {"value": "WALK", "name": "WALK"},
            {"value": "BICYCLE", "name": "BICYCLE"},
            {"value": "CAR", "name": "CAR"},
            {"value": "TRANSIT,BICYCLE", "name": "TRANSIT_BICYCLE"},
            {"value": "CAR,WALK,TRANSIT", "name": "KISSRIDE"},
        ],
        "lowerLeftLongitude": -9.167649,
        "lowerLeftLatitude": 0.0,
        "upperRightLongitude": 49.368986,
        "upperRightLatitude": 53.552107,
        "hasBikeSharing": False,
        "hasBikePark": False,
    }


@pytest.fixture
def otp_plan_response() -> Dict:
    return {
        TransportMode.PUBLIC_TRANSPORT: otp_plan.tc_response,
        TransportMode.WALK: otp_plan.walk_response,
        TransportMode.CAR: otp_plan.car_response,
        TransportMode.BICYCLE: otp_plan.bicycle_response,
        TransportMode.ELECTRIC_BICYCLE: otp_plan.ebike_response,
        TransportMode.CARPOOLING: otp_plan.car_response,
        TransportMode.ELECTRIC_CAR: otp_plan.car_response,
        TransportMode.MOTORCYCLE: otp_plan.car_response,
        TransportMode.ELECTRIC_MOTORCYCLE: otp_plan.car_response,
        TransportMode.FAST_BICYCLE: otp_plan.fast_bike_response,
        TransportMode.CAR_PUBLIC_TRANSPORT: otp_plan.tc_response,
        TransportMode.BICYCLE_PUBLIC_TRANSPORT: otp_plan.tc_response,
    }


@pytest.fixture
def otp_one_isochrone_response():
    return {
        "type": "FeatureCollection",
        "crs": {"type": "name", "properties": {"name": "EPSG:4326"}},
        "features": [
            {
                "type": "Feature",
                "geometry": {
                    "type": "MultiPolygon",
                    "coordinates": [
                        [
                            [
                                [3.0938, 45.7478],
                                [3.0944, 45.7478],
                                [3.0964, 45.7476],
                                [3.0938, 45.7478],
                            ],
                            [
                                [3.1207, 45.7535],
                                [3.1196, 45.7545],
                                [3.1222, 45.7558],
                                [3.1207, 45.7535],
                            ],
                            [
                                [3.1298, 45.7814],
                                [3.1298, 45.7815],
                                [3.1299, 45.7815],
                                [3.1298, 45.7814],
                            ],
                            [
                                [3.0651, 45.7687],
                                [3.065, 45.7689],
                                [3.0654, 45.77],
                                [3.0651, 45.7687],
                            ],
                        ],
                        [
                            [
                                [3.1706, 45.7451],
                                [3.1712, 45.7441],
                                [3.1716, 45.7441],
                                [3.1706, 45.7451],
                            ]
                        ],
                        [
                            [
                                [3.1557, 45.7779],
                                [3.1634, 45.7768],
                                [3.1613, 45.7797],
                                [3.1557, 45.7779],
                            ]
                        ],
                        [
                            [
                                [3.1574, 45.7935],
                                [3.1634, 45.7932],
                                [3.1634, 45.7964],
                                [3.1574, 45.7935],
                            ]
                        ],
                        [
                            [
                                [3.1657, 45.7381],
                                [3.1686, 45.7375],
                                [3.1694, 45.7407],
                                [3.1657, 45.7381],
                            ]
                        ],
                        [
                            [
                                [3.1874, 45.7227],
                                [3.1892, 45.7225],
                                [3.191, 45.7239],
                                [3.1874, 45.7227],
                            ]
                        ],
                    ],
                },
                "properties": {"time": 1800},
                "id": "fid-2c9c99f4_174c6658e38_-8000",
            }
        ],
    }


@pytest.fixture
def otp_two_isochrones_response():
    return {
        "type": "FeatureCollection",
        "crs": {"type": "name", "properties": {"name": "EPSG:4326"}},
        "features": [
            {
                "type": "Feature",
                "geometry": {
                    "type": "MultiPolygon",
                    "coordinates": [
                        [
                            [
                                [3.0938, 45.7478],
                                [3.0944, 45.7478],
                                [3.0964, 45.7476],
                                [3.0938, 45.7478],
                            ],
                            [
                                [3.1207, 45.7535],
                                [3.1196, 45.7545],
                                [3.1222, 45.7558],
                                [3.1207, 45.7535],
                            ],
                            [
                                [3.1298, 45.7814],
                                [3.1298, 45.7815],
                                [3.1299, 45.7815],
                                [3.1298, 45.7814],
                            ],
                            [
                                [3.0651, 45.7687],
                                [3.065, 45.7689],
                                [3.0654, 45.77],
                                [3.0651, 45.7687],
                            ],
                        ],
                    ],
                },
                "properties": {"time": 1800},
                "id": "fid-2c9c99f4_174c6658e38_-8000",
            },
            {
                "type": "Feature",
                "geometry": {
                    "type": "MultiPolygon",
                    "coordinates": [
                        [
                            [
                                [3.1938, 45.7478],
                                [3.1944, 45.7478],
                                [3.1964, 45.7476],
                                [3.1938, 45.7478],
                            ],
                            [
                                [3.1217, 45.7535],
                                [3.1196, 45.7545],
                                [3.1222, 45.7558],
                                [3.1217, 45.7535],
                            ],
                            [
                                [3.1298, 45.7814],
                                [3.1298, 45.7815],
                                [3.1299, 45.7815],
                                [3.1298, 45.7814],
                            ],
                            [
                                [3.1651, 45.7687],
                                [3.165, 45.7689],
                                [3.1654, 45.77],
                                [3.1651, 45.7687],
                            ],
                        ],
                    ],
                },
                "properties": {"time": 3600},
                "id": "fid-XXXXXXXXXXXXXXXXXX",
            },
        ],
    }

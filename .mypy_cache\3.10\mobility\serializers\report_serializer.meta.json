{"data_mtime": 1752154451, "dep_lines": [27, 12, 13, 14, 15, 16, 28, 29, 30, 31, 32, 10, 11, 17, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 6, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["mobility.ir.indicators.indicators", "mobility.ir.country", "mobility.ir.map_elements", "mobility.ir.new_indicators", "mobility.ir.study", "mobility.ir.table_of_contents", "mobility.serializers.chart_tracer", "mobility.serializers.illustrator", "mobility.serializers.phraser", "mobility.serializers.relative_templates", "mobility.serializers.report_quantity_formatters", "mobility.enumerations", "mobility.funky", "mobility.quantity", "os", "shutil", "typing", "jinja2", "builtins", "_frozen_importlib", "_io", "abc", "enum", "io", "jinja2.bccache", "jinja2.environment", "jinja2.ext", "jinja2.loaders", "jinja2.runtime", "mobility.ir", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.ir.territory"], "hash": "7d78a99a7530f61e6430985f18e61fc06cacc897", "id": "mobility.serializers.report_serializer", "ignore_all": false, "interface_hash": "50401e6d9c4f54c099b9c536fd97f42339e56627", "mtime": 1747931728, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\serializers\\report_serializer.py", "plugin_data": null, "size": 8821, "suppressed": ["weasyprint.document", "setuptools_scm", "weasyprint"], "version_id": "1.16.1"}
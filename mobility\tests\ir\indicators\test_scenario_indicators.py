from typing import Any

import pytest

from mobility.converters.accessibility import AccessibilitySlice
from mobility.funky import ImmutableDict, UnorderedList
from mobility.ir.indicators.scenario_indicators import (
    convert_carbon_emission_gec_per_commute_to_teq_per_year,
    gather_alternative_arrival_time,
)
from mobility.ir.study import ConsolidatedScenario, ModalCommuteData
from mobility.ir.transport import TransportMode
from mobility.quantity import gramEC, trip


class TestScenarioIndicators:
    def test_should_return_scenario_name(
        self, consolidated_scenario, scenario_indicators
    ):
        indicators = scenario_indicators(consolidated_scenario)

        assert indicators.nickname == "trivial"

    def test_should_return_average_time_when_present_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 20, TransportMode.CAR: 10}
                            ),
                        ),
                    )
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        assert indicators.average_travel_time == 20.0

    def test_should_fail_building_when_scenario_has_no_commutes(
        self, consolidated_scenario_factory: Any, scenario_indicators
    ) -> None:
        scenario = consolidated_scenario_factory(commutes=frozenset())

        with pytest.raises(ValueError):
            scenario_indicators(scenario)

    def test_compute_mean_time_for_one_employee(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        walk_time = [(TransportMode.WALK, {TransportMode.WALK: 3200})]
        scenario = scenario_with_modes(walk_time)
        indicators = scenario_indicators(scenario)

        average_time_iso_mode = indicators._compute_average_time(*TransportMode)

        assert average_time_iso_mode == 3200

    def test_compute_mean_time_for_two_employees(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
            ],
        )
        indicators = scenario_indicators(scenario)

        average_time_iso_mode = indicators._compute_average_time(*TransportMode)

        assert average_time_iso_mode == 1500

    def test_should_return_none_average_time_when_no_time_for_requested_mode(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [(TransportMode.WALK, {TransportMode.WALK: 1000})],
        )
        indicators = scenario_indicators(scenario)

        average_time_iso_mode = indicators._compute_average_time(TransportMode.BICYCLE)

        assert average_time_iso_mode is None

    def test_should_return_average_time_for_one_mode(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (
                    TransportMode.WALK,
                    {TransportMode.WALK: 2000, TransportMode.CAR: 5000},
                ),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 5000}),
            ],
        )
        indicators = scenario_indicators(scenario)

        average_walk_time = indicators._compute_average_time(TransportMode.WALK)

        assert average_walk_time == 1500

    def test_compute_median_time(self, scenario_with_modes, scenario_indicators: Any):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 500}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
            ],
        )
        indicators = scenario_indicators(scenario)

        median_time = indicators.compute_median_time(*TransportMode)

        assert median_time == 2000

    def test_compute_median_time_per_transport_mode(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 500}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
            ],
        )
        indicators = scenario_indicators(scenario)

        median_time = indicators.compute_median_time()
        median_walk_time = indicators.compute_median_time(TransportMode.WALK)
        median_bike_time = indicators.compute_median_time(TransportMode.BICYCLE)
        median_car_time = indicators.compute_median_time(TransportMode.CAR)

        assert median_time == 2000
        assert median_walk_time == 1500
        assert median_bike_time == 2000
        assert median_car_time is None

    def test_compute_distribution(self, scenario_with_modes, scenario_indicators: Any):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 500}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 1000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
            ],
        )
        indicators = scenario_indicators(scenario)

        distribution = indicators._compute_distribution(
            10 * 60, 80 * 60, *TransportMode
        )

        assert distribution == {
            (0, 600): 1,
            (600, 1200): 2,
            (1200, 1800): 0,
            (1800, 2400): 2,
            (2400, 3000): 0,
            (3000, 3600): 0,
            (3600, 4200): 1,
            (4200, 4800): 0,
        }

    def test_compute_distribution_with_out_of_range_times(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 600}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 4500}),
            ],
        )
        indicators = scenario_indicators(scenario)

        distribution = indicators._compute_distribution(
            10 * 60, 40 * 60, *TransportMode
        )

        assert distribution == {
            (0, 600): 0,
            (600, 1200): 2,
            (1200, 1800): 0,
            (1800, 2400): 2,
            (2400, 4500): 2,
        }

    def test_compute_distribution_with_filter(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 600}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 4500}),
            ],
        )
        indicators = scenario_indicators(scenario)

        distribution = indicators._compute_distribution(
            10 * 60, 40 * 60, TransportMode.WALK
        )

        assert distribution == {
            (0, 600): 0,
            (600, 1200): 2,
            (1200, 1800): 0,
            (1800, 2400): 1,
            (2400, 4000): 1,
        }

    def test_compute_distribution_with_no_time_in_requested_mode(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        scenario = scenario_with_modes(
            [
                (TransportMode.WALK, {TransportMode.WALK: 600}),
                (TransportMode.WALK, {TransportMode.WALK: 1000}),
                (TransportMode.WALK, {TransportMode.WALK: 2000}),
                (TransportMode.WALK, {TransportMode.WALK: 4000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 2000}),
                (TransportMode.BICYCLE, {TransportMode.BICYCLE: 4500}),
            ],
        )
        indicators = scenario_indicators(scenario)

        distribution = indicators._compute_distribution(
            10 * 60, 40 * 60, TransportMode.CAR
        )

        assert distribution == {
            (0, 600): 0,
            (600, 1200): 0,
            (1200, 1800): 0,
            (1800, 2400): 0,
        }

    def test_should_return_addresses_from_mono_site_scenario(
        self, consolidated_scenario: ConsolidatedScenario, scenario_indicators
    ) -> None:
        indicators = scenario_indicators(consolidated_scenario)

        assert indicators.addresses == {"site_address_01"}

    def test_should_return_addresses_from_two_sites_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        geo_site_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        indicators = scenario_indicators(
            consolidated_scenario_factory(
                commutes=frozenset(
                    {
                        consolidated_commute_factory(
                            site=geo_site_factory(address="address 01")
                        ),
                        consolidated_commute_factory(
                            site=geo_site_factory(address="address 02")
                        ),
                    }
                )
            )
        )

        assert indicators.addresses == {"address 01", "address 02"}

    def test_should_get_single_address_from_mono_site_scenario(
        self, consolidated_scenario: ConsolidatedScenario, scenario_indicators: Any
    ) -> None:
        indicators = scenario_indicators(consolidated_scenario)

        assert indicators.get_single_address() == "site_address_01"

    def test_should_fail_to_get_single_address_from_two_sites_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        geo_site_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        indicators = scenario_indicators(
            consolidated_scenario_factory(
                commutes=frozenset(
                    {
                        consolidated_commute_factory(
                            site=geo_site_factory(address="address 01")
                        ),
                        consolidated_commute_factory(
                            site=geo_site_factory(address="address 02")
                        ),
                    }
                )
            )
        )

        with pytest.raises(ValueError):
            indicators.get_single_address()


class TestGetNumberEmployeeInInterval:
    def test_should_count_one_employee_in_one_commute_scenario(
        self, consolidated_scenario, scenario_indicators: Any
    ):
        indicators = scenario_indicators(consolidated_scenario)
        acc_slice = AccessibilitySlice(None, None)

        assert indicators.get_nb_employees_in(acc_slice) == 1

    def test_should_count_three_employee_in_three_commute_scenario(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        walk_employee = geo_employee_factory(id=1, transport_mode=TransportMode.WALK)
        car_employee = geo_employee_factory(id=2, transport_mode=TransportMode.CAR)
        pt_employee = geo_employee_factory(
            id=3, transport_mode=TransportMode.PUBLIC_TRANSPORT
        )
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=walk_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 30}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=car_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 10}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=pt_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {TransportMode.PUBLIC_TRANSPORT: 10}
                            ),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)
        acc_slice = AccessibilitySlice(None, None)

        assert indicators.get_nb_employees_in(acc_slice) == 3

    def test_should_count_employees(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        walk_employee = geo_employee_factory(id=1, transport_mode=TransportMode.WALK)
        car_employee = geo_employee_factory(id=2, transport_mode=TransportMode.CAR)
        pt_employee = geo_employee_factory(
            id=3, transport_mode=TransportMode.PUBLIC_TRANSPORT
        )
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=walk_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 30}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=car_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 10}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=pt_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {TransportMode.PUBLIC_TRANSPORT: 10}
                            ),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)
        acc_slice = AccessibilitySlice(0, 20)

        assert indicators.get_nb_employees_in(acc_slice) == 2


class TestComputePercentile:
    def test_compute_90_percentile(self, scenario_with_modes, scenario_indicators: Any):
        indicators = scenario_indicators(
            scenario_with_modes(
                [
                    (TransportMode.WALK, {TransportMode.WALK: 1000}),
                    (TransportMode.WALK, {TransportMode.WALK: 1000}),
                    (TransportMode.WALK, {TransportMode.WALK: 2000}),
                    (TransportMode.WALK, {TransportMode.WALK: 3000}),
                ],
            )
        )

        percentile_90 = indicators.compute_percentile(90)

        assert percentile_90 == 2700

    def test_compute_90_percentile_in_only_one_mode(
        self, scenario_with_modes, scenario_indicators: Any
    ):
        indicators = scenario_indicators(
            scenario_with_modes(
                [
                    (TransportMode.WALK, {TransportMode.WALK: 1000}),
                    (TransportMode.WALK, {TransportMode.WALK: 1000}),
                    (TransportMode.WALK, {TransportMode.WALK: 2000}),
                    (TransportMode.WALK, {TransportMode.WALK: 3000}),
                    (TransportMode.CAR, {TransportMode.CAR: 2000}),
                    (TransportMode.CAR, {TransportMode.CAR: 3000}),
                ],
            )
        )

        percentile_90 = indicators.compute_percentile(90, TransportMode.WALK)

        assert percentile_90 == 2700


class TestCountEmployee:
    def test_count_employee_per_mode_in_present_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.CAR: 10}
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=2,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 30,
                                    TransportMode.PUBLIC_TRANSPORT: 10,
                                }
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=3,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.BICYCLE: 10}
                            ),
                        ),
                    ),
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        nb_walker = indicators.count_employees_per_mode(TransportMode.WALK)
        nb_biker = indicators.count_employees_per_mode(TransportMode.BICYCLE)
        nb_driver = indicators.count_employees_per_mode(TransportMode.CAR)
        nb_greener = indicators.count_employees_per_mode(TransportMode.PUBLIC_TRANSPORT)

        assert nb_walker == 3
        assert nb_biker == 0
        assert nb_greener == 0
        assert nb_driver == 0

    def test_count_employee_repartition_per_mode_in_present_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.CAR: 10}
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=2,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 30,
                                    TransportMode.PUBLIC_TRANSPORT: 10,
                                }
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=3,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.BICYCLE: 10}
                            ),
                        ),
                    ),
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        repartition = indicators.count_employees_repartition_per_mode()

        assert repartition == {
            TransportMode.WALK: 3,
            TransportMode.BICYCLE: 0,
            TransportMode.PUBLIC_TRANSPORT: 0,
            TransportMode.CAR: 0,
            TransportMode.CARPOOLING: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.AIRPLANE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }

    def test_count_employee_per_mode_in_alternative_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            id=2,
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.CAR: 10}
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=2,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 30,
                                    TransportMode.PUBLIC_TRANSPORT: 10,
                                }
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=3,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.BICYCLE,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.BICYCLE: 10}
                            ),
                        ),
                    ),
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        nb_walker = indicators.count_employees_per_mode(TransportMode.WALK)
        nb_biker = indicators.count_employees_per_mode(TransportMode.BICYCLE)
        nb_driver = indicators.count_employees_per_mode(TransportMode.CAR)
        nb_greener = indicators.count_employees_per_mode(TransportMode.PUBLIC_TRANSPORT)

        assert nb_walker == 0
        assert nb_biker == 1
        assert nb_greener == 1
        assert nb_driver == 1

    def test_count_employee_repartition_per_mode_in_alternative_scenario(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            id=2,
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.CAR: 10}
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=2,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.WALK: 30,
                                    TransportMode.PUBLIC_TRANSPORT: 10,
                                }
                            ),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=3,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.BICYCLE,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.BICYCLE: 10}
                            ),
                        ),
                    ),
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        repartition = indicators.count_employees_repartition_per_mode()

        assert repartition == {
            TransportMode.WALK: 0,
            TransportMode.BICYCLE: 1,
            TransportMode.PUBLIC_TRANSPORT: 1,
            TransportMode.CAR: 1,
            TransportMode.CARPOOLING: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.AIRPLANE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }


class TestComputeTravelTimeScore:
    def test_should_have_travel_time_score_to_1_when_less_than_35_percent_travel_in_less_than_40_min(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 40 * 60}),
                        ),
                    )
                }
            )
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.transport_time == 0

    def test_should_have_travel_time_score_to_1_when_more_than_35_percent_travel_in_less_than_40_min(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        higher_40m = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 40 * 60}),
                ),
            )
            for i in range(1, 4)
        ]
        lower_40m = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 10 * 60}),
                ),
            )
            for i in range(4, 6)
        ]
        scenario = consolidated_scenario_factory(
            commutes=frozenset(higher_40m + lower_40m)
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.transport_time == 1

    def test_should_have_travel_time_score_to_2_when_more_than_50_percent_travel_in_less_than_40_min(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        higher_40m = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 40 * 60}),
                ),
            )
            for i in range(1, 3)
        ]
        lower_40m = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 10 * 60}),
                ),
            )
            for i in range(3, 6)
        ]
        scenario = consolidated_scenario_factory(
            commutes=frozenset(higher_40m + lower_40m)
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.transport_time == 2

    def test_should_have_travel_time_score_to_3_when_more_than_65_percent_travel_in_less_than_40_min(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 30 * 60}),
                        ),
                    )
                }
            )
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.transport_time == 3


class TestComputeCriticalityScore:
    def test_should_have_criticality_score_to_1_when_more_than_15_percent_critical_cases(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 60 * 60}),
                        ),
                    )
                }
            )
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.criticality == 0

    def test_should_have_criticality_score_to_1_when_more_than_10_percent_critical_cases(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        critical_cases = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 70 * 60}),
                ),
            )
            for i in range(1, 2)
        ]
        commutes = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 10 * 60}),
                ),
            )
            for i in range(2, 11)
        ]
        scenario = consolidated_scenario_factory(
            commutes=frozenset(critical_cases + commutes)
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.criticality == 1

    def test_should_have_criticality_score_to_2_when_more_than_5_percent_critical_cases(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        critical_cases = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 70 * 60}),
                ),
            )
            for i in range(1, 2)
        ]
        commutes = [
            consolidated_commute_factory(
                employee=geo_employee_factory(id=i, transport_mode=TransportMode.WALK),
                data=modal_commute_data_factory(
                    best_mode=TransportMode.WALK,
                    duration=ImmutableDict({TransportMode.WALK: 10 * 60}),
                ),
            )
            for i in range(2, 21)
        ]
        scenario = consolidated_scenario_factory(
            commutes=frozenset(critical_cases + commutes)
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.criticality == 2

    def test_should_have_criticality_score_to_3_when_less_than_5_percent_critical_cases(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        modal_commute_data_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=geo_employee_factory(
                            id=1,
                            transport_mode=TransportMode.WALK,
                        ),
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 10 * 60}),
                        ),
                    )
                }
            )
        )

        indicators = scenario_indicators(scenario)

        assert indicators.scores.criticality == 3


class TestCarbonAuditScore:
    def test_should_set_carbon_audit_score_to_2(
        self, consolidated_scenario_factory: Any, scenario_indicators: Any
    ) -> None:
        indicators = scenario_indicators(consolidated_scenario_factory())

        carbon_audit_score = indicators.scores.carbon_audit

        assert carbon_audit_score == 3


class TestGetCarbonEmission:
    def test_should_return_carbon_emission(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            nickname="My Study",
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.CAR: 600, TransportMode.WALK: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.CAR: 2, TransportMode.WALK: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.CAR: 300, TransportMode.WALK: 0}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.BICYCLE,
                            duration=ImmutableDict(
                                {TransportMode.BICYCLE: 500, TransportMode.CAR: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.BICYCLE: 2, TransportMode.CAR: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.BICYCLE: 0, TransportMode.CAR: 400}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 500,
                                    TransportMode.CAR: 800,
                                }
                            ),
                            distance=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 2,
                                    TransportMode.CAR: 2,
                                }
                            ),
                            emission=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 50,
                                    TransportMode.CAR: 400,
                                }
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        ges = indicators.get_teq_carbon_emission()

        assert ges == 0.2

    def test_should_return_carbon_emission_of_public_transport(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            nickname="My Study",
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {
                                    TransportMode.CAR: 600,
                                    TransportMode.PUBLIC_TRANSPORT: 800,
                                }
                            ),
                            distance=ImmutableDict(
                                {
                                    TransportMode.CAR: 2,
                                    TransportMode.PUBLIC_TRANSPORT: 2,
                                }
                            ),
                            emission=ImmutableDict(
                                {
                                    TransportMode.CAR: 300,
                                    TransportMode.PUBLIC_TRANSPORT: 200,
                                }
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.CAR: 500,
                                    TransportMode.PUBLIC_TRANSPORT: 800,
                                }
                            ),
                            distance=ImmutableDict(
                                {
                                    TransportMode.CAR: 2,
                                    TransportMode.PUBLIC_TRANSPORT: 2,
                                }
                            ),
                            emission=ImmutableDict(
                                {
                                    TransportMode.CAR: 200,
                                    TransportMode.PUBLIC_TRANSPORT: 150,
                                }
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        ges = indicators.get_teq_carbon_emission(TransportMode.PUBLIC_TRANSPORT)

        assert ges == 0.1

    def test_should_return_carbon_emission_quantity(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            nickname="My Study",
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.CAR: 600, TransportMode.WALK: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.CAR: 2, TransportMode.WALK: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.CAR: 300, TransportMode.WALK: 0}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.BICYCLE,
                            duration=ImmutableDict(
                                {TransportMode.BICYCLE: 500, TransportMode.CAR: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.BICYCLE: 2, TransportMode.CAR: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.BICYCLE: 0, TransportMode.CAR: 400}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 500,
                                    TransportMode.CAR: 800,
                                }
                            ),
                            distance=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 2,
                                    TransportMode.CAR: 2,
                                }
                            ),
                            emission=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 50,
                                    TransportMode.CAR: 400,
                                }
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        ges = indicators.get_carbon_emission()

        assert ges == 350 * gramEC / trip

    def test_should_return_mean_carbon_emission_quantity(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        scenario_indicators: Any,
    ) -> None:
        scenario = consolidated_scenario_factory(
            nickname="My Study",
            is_present=False,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.CAR: 600, TransportMode.WALK: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.CAR: 2, TransportMode.WALK: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.CAR: 300, TransportMode.WALK: 0}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.BICYCLE,
                            duration=ImmutableDict(
                                {TransportMode.BICYCLE: 500, TransportMode.CAR: 800}
                            ),
                            distance=ImmutableDict(
                                {TransportMode.BICYCLE: 2, TransportMode.CAR: 2}
                            ),
                            emission=ImmutableDict(
                                {TransportMode.BICYCLE: 0, TransportMode.CAR: 400}
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                    consolidated_commute_factory(
                        data=ModalCommuteData(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 500,
                                    TransportMode.CAR: 800,
                                }
                            ),
                            distance=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 2,
                                    TransportMode.CAR: 2,
                                }
                            ),
                            emission=ImmutableDict(
                                {
                                    TransportMode.PUBLIC_TRANSPORT: 50,
                                    TransportMode.CAR: 400,
                                }
                            ),
                            alternative_arrival_time=ImmutableDict(),
                        )
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        ges = indicators.get_yearly_carbon_emission_per_employee()

        assert ges == (350 * gramEC / 3) * 2 * 5 * 47


class TestCarbonEmissionUnitConverter:
    def test_should_convert_carbon_emission_gec_per_commute_to_teq_per_year(self):
        assert convert_carbon_emission_gec_per_commute_to_teq_per_year(2000) == 0.9

    def test_should_return_zero_when_no_emission(self):
        assert convert_carbon_emission_gec_per_commute_to_teq_per_year(0) == 0


class TestGatherAlternativeArrivalTime:
    def test_should_return_empty_list_when_no_commutes(
        self, consolidated_scenario_factory
    ) -> None:
        scenario = consolidated_scenario_factory(commutes=frozenset())

        hours = gather_alternative_arrival_time(scenario)

        assert hours == []

    def test_should_return_empty_list_when_no_commute_alternate_times(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
    ) -> None:
        commutes = {
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    alternative_arrival_time=ImmutableDict()
                )
            ),
        }
        scenario = consolidated_scenario_factory(commutes=frozenset(commutes))

        hours = gather_alternative_arrival_time(scenario)

        assert hours == []

    def test_should_list_some_hours(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
    ) -> None:
        commutes = {
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    alternative_arrival_time=ImmutableDict(
                        {
                            (10, 0): ImmutableDict(),
                            (8, 30): ImmutableDict(),
                            (8, 20): ImmutableDict(),
                            (9, 30): ImmutableDict(),
                            (7, 30): ImmutableDict(),
                        }
                    )
                )
            ),
        }
        scenario = consolidated_scenario_factory(commutes=frozenset(commutes))

        hours = gather_alternative_arrival_time(scenario)

        assert hours == [(7, 30), (8, 20), (8, 30), (9, 30), (10, 0)]

    def test_should_fail_if_uneven_alternate_timings(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory,
    ) -> None:
        commutes = {
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    alternative_arrival_time=ImmutableDict(
                        {
                            (10, 0): ImmutableDict(),
                            (8, 30): ImmutableDict(),
                            (8, 20): ImmutableDict(),
                            (9, 30): ImmutableDict(),
                            (7, 30): ImmutableDict(),
                        }
                    )
                )
            ),
            consolidated_commute_factory(
                data=modal_commute_data_factory(
                    alternative_arrival_time=ImmutableDict({(5, 0): ImmutableDict()})
                )
            ),
        }
        scenario = consolidated_scenario_factory(commutes=frozenset(commutes))

        with pytest.raises(ValueError):
            gather_alternative_arrival_time(scenario)


class TestComputeCosts:
    def test_should_compute_costs_in_some_scenario(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        walk_employee = geo_employee_factory(id=1, transport_mode=TransportMode.WALK)
        car_employee = geo_employee_factory(id=2, transport_mode=TransportMode.CAR)
        pt_employee = geo_employee_factory(
            id=3, transport_mode=TransportMode.PUBLIC_TRANSPORT
        )
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=walk_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 30}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=car_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 10}),
                            distance=ImmutableDict({TransportMode.CAR: 1700}),
                        ),
                    ),
                    consolidated_commute_factory(
                        employee=pt_employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.PUBLIC_TRANSPORT,
                            duration=ImmutableDict(
                                {TransportMode.PUBLIC_TRANSPORT: 10}
                            ),
                        ),
                    ),
                }
            ),
        )

        indicators = scenario_indicators(scenario)

        assert all(
            e in indicators.costs.cost_by_employee
            for e in [walk_employee, car_employee, pt_employee]
        )


class TestComputeNbEmployeesInIntervalByForcedMode:
    def test_should_count_employee_with_favourite_mode(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        employee = geo_employee_factory(id=1, transport_mode=TransportMode.WALK)
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.WALK,
                            duration=ImmutableDict({TransportMode.WALK: 30}),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        count = indicators.get_nb_employees_in_interval_by_forced_mode(
            29, 31, TransportMode.WALK
        )

        assert count == 1

    def test_should_count_employee_without_favourite_mode(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        employee = geo_employee_factory(id=1, transport_mode=TransportMode.CAR)
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.WALK: 30, TransportMode.CAR: 10}
                            ),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        count = indicators.get_nb_employees_in_interval_by_forced_mode(
            29, 31, TransportMode.WALK
        )

        assert count == 1

    def test_should_not_count_employee_without_time(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        employee = geo_employee_factory(id=1, transport_mode=TransportMode.CAR)
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict({TransportMode.CAR: 10}),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        count = indicators.get_nb_employees_in_interval_by_forced_mode(
            29, 31, TransportMode.WALK
        )

        assert count == 0

    def test_should_not_count_employee_out_of_bounds(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
    ):
        employee = geo_employee_factory(id=1, transport_mode=TransportMode.CAR)
        scenario = consolidated_scenario_factory(
            id=1,
            commutes=frozenset(
                {
                    consolidated_commute_factory(
                        employee=employee,
                        data=modal_commute_data_factory(
                            best_mode=TransportMode.CAR,
                            duration=ImmutableDict(
                                {TransportMode.CAR: 10, TransportMode.WALK: 60}
                            ),
                        ),
                    ),
                }
            ),
        )
        indicators = scenario_indicators(scenario)

        count = indicators.get_nb_employees_in_interval_by_forced_mode(
            None, 59, TransportMode.WALK
        )

        assert count == 0

    def test_should_group_employees_by_postcode_and_transport_mode(
        self,
        consolidated_scenario_factory,
        consolidated_commute_factory,
        modal_commute_data_factory: Any,
        geo_employee_factory,
        scenario_indicators: Any,
        duration_full: Any,
        address: Any,
    ):
        city = address(postcode="12123")
        other_city = address(postcode="01000")
        employee1 = geo_employee_factory(id=1, address_details=city)
        employee2 = geo_employee_factory(id=2, address_details=city)
        employee3 = geo_employee_factory(id=3, address_details=other_city)
        employee4 = geo_employee_factory(id=4, address_details=other_city)
        employee5 = geo_employee_factory(id=5, address_details=city)
        employee6 = geo_employee_factory(id=6, address_details=city)
        car_commute = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=duration_full,
        )
        bike_commute = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=duration_full,
        )
        walk_commute = modal_commute_data_factory(
            best_mode=TransportMode.WALK,
            duration=duration_full,
        )
        commutes = [
            consolidated_commute_factory(employee=employee1, data=car_commute),
            consolidated_commute_factory(employee=employee2, data=car_commute),
            consolidated_commute_factory(employee=employee3, data=car_commute),
            consolidated_commute_factory(employee=employee4, data=bike_commute),
            consolidated_commute_factory(employee=employee5, data=bike_commute),
            consolidated_commute_factory(employee=employee6, data=walk_commute),
        ]
        scenario = consolidated_scenario_factory(
            id=1, commutes=frozenset(set(commutes))
        )
        indicators = scenario_indicators(scenario)

        count = indicators.compute_employees_by_postcode_and_mode()

        assert count == {
            "12123": {
                TransportMode.CAR: UnorderedList([employee1, employee2]),
                TransportMode.BICYCLE: [employee5],
                TransportMode.WALK: [employee6],
            },
            "01000": {
                TransportMode.CAR: [employee3],
                TransportMode.BICYCLE: [employee4],
            },
        }


class TestGetCitiesWithEmployeesOrSites:
    def test_get_cities_with_most_employees(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        geo_employee_factory: Any,
        scenario_indicators: Any,
        address_factory: Any,
    ) -> None:
        employees = [
            geo_employee_factory(id=1, address_details=address_factory(city="CityA")),
            geo_employee_factory(id=2, address_details=address_factory(city="CityA")),
            geo_employee_factory(id=3, address_details=address_factory(city="CityB")),
            geo_employee_factory(id=4, address_details=address_factory(city="CityC")),
            geo_employee_factory(id=5, address_details=address_factory(city="CityC")),
            geo_employee_factory(id=6, address_details=address_factory(city="CityC")),
        ]
        commutes = frozenset(
            consolidated_commute_factory(employee=employee) for employee in employees
        )
        scenario = consolidated_scenario_factory(commutes=commutes)
        indicators = scenario_indicators(scenario)

        cities = indicators.get_cities_with_most_employees(limit=2)
        print(cities)

        assert cities == {"CityC": 3, "CityA": 2}

    def test_get_sites_by_commune(
        self,
        consolidated_scenario_factory: Any,
        consolidated_commute_factory: Any,
        geo_site_factory: Any,
        scenario_indicators: Any,
        address_factory: Any,
    ) -> None:
        site1 = geo_site_factory(address_details=address_factory(city="CityA"))
        site2 = geo_site_factory(address_details=address_factory(city="CityB"))
        scenario = consolidated_scenario_factory(
            commutes=frozenset(
                {
                    consolidated_commute_factory(site=site1),
                    consolidated_commute_factory(site=site2),
                }
            )
        )
        indicators = scenario_indicators(scenario)

        sites_by_commune = indicators.get_sites_by_commune()

        assert sites_by_commune == {
            "CityA": [site1.nickname],
            "CityB": [site2.nickname],
        }

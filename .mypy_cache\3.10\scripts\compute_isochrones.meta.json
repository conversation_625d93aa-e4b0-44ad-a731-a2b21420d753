{"data_mtime": 1751444475, "dep_lines": [7, 8, 10, 11, 12, 1, 2, 3, 4, 5, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.event_reporter", "api_abstraction.api.factories", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "<PERSON><PERSON><PERSON><PERSON>", "datetime", "json", "os", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "api_abstraction.api.travel_time_api", "configparser", "enum", "io", "json.encoder", "mobility.ir", "mobility.ir.country", "ntpath", "typing_extensions"], "hash": "366fcd1f78891a2f4a9932b8599ce9657e5251f8", "id": "scripts.compute_isochrones", "ignore_all": false, "interface_hash": "eec532cf095e66e4cada972f7b7fe10e0f1b4f8a", "mtime": 1747127616, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\compute_isochrones.py", "plugin_data": null, "size": 3848, "suppressed": [], "version_id": "1.16.1"}
{"data_mtime": 1752154448, "dep_lines": [6, 7, 9, 10, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["mobility.ir.crit_air", "mobility.ir.zfe", "mobility.repositories.simulated_company", "mobility.use_cases.simulated_company_potential", "mobility.quantity", "typing", "flask", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "flask.blueprints", "flask.helpers", "mobility", "mobility.ir", "mobility.ir.company_potential", "mobility.repositories", "mobility.repositories.abstract_repositories", "mobility.use_cases", "mobility.use_cases.base_use_case", "typing_extensions"], "hash": "a36d4d1824cf1da54b446394609405c8ef0a6211", "id": "webservice.apis.routes.company_simulation", "ignore_all": false, "interface_hash": "c59ced612c61b4458c3988a3ec8d6670e1f80801", "mtime": 1722327435, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\apis\\routes\\company_simulation.py", "plugin_data": null, "size": 12009, "suppressed": ["flask_restx"], "version_id": "1.16.1"}
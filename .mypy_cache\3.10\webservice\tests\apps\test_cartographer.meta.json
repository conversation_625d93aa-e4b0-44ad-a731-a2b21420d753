{"data_mtime": 1752154445, "dep_lines": [8, 7, 13, 14, 15, 17, 18, 2, 6, 16, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.ir.indicators.mode_shift_scenario_indicators", "mobility.ir.employee", "mobility.ir.territory", "mobility.ir.transport", "mobility.ir.webstudy", "mobility.serializers.report_quantity_formatters", "webservice.apps.cartographer", "unittest.mock", "mobility.funky", "mobility.quantity", "typing", "unittest", "pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "enum", "mobility", "mobility.ir", "mobility.ir.bounding_box", "mobility.ir.geo_study", "mobility.ir.indicators", "mobility.serializers", "types", "typing_extensions", "webservice.apps"], "hash": "10ed5e40f7b18d4bb071020d18232d98c3d8ac8e", "id": "webservice.tests.apps.test_cartographer", "ignore_all": false, "interface_hash": "80223979b3cc44c68656e4ecc5c228a5bdd8ca0f", "mtime": 1722327437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "webservice\\tests\\apps\\test_cartographer.py", "plugin_data": null, "size": 28542, "suppressed": [], "version_id": "1.16.1"}
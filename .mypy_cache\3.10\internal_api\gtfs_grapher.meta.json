{"data_mtime": 1751444403, "dep_lines": [17, 18, 20, 16, 19, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 12, 13], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10], "dependencies": ["mobility.ir.geo_study", "mobility.ir.transport", "mobility.workers.distance_computer", "mobility.funky", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "csv", "dataclasses", "datetime", "io", "os", "pprint", "zipfile", "collections", "typing", "builtins", "_collections_abc", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "fractions", "genericpath", "mobility", "mobility.ir", "mobility.workers", "numbers", "typing_extensions"], "hash": "309261a8a5fe38d6d793cbed5e481c5d1692e439", "id": "internal_api.gtfs_grapher", "ignore_all": false, "interface_hash": "51acbb3cd28ac77e4ca7d534d127f010979a5b7a", "mtime": 1730103305, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "internal_api\\gtfs_grapher.py", "plugin_data": null, "size": 24246, "suppressed": ["geopy.geocoders", "geopy", "networkx"], "version_id": "1.16.1"}
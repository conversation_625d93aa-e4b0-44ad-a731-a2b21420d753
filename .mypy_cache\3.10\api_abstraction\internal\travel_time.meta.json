{"data_mtime": 1753176776, "dep_lines": [10, 11, 12, 15, 16, 17, 19, 13, 18, 1, 2, 3, 4, 5, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 7, 8], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 10], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.event_reporter", "api_abstraction.api.travel_time_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.workers.distance_computer", "internal_api.geo_graph", "mobility.quantity", "dataclasses", "pickle", "collections", "itertools", "typing", "mobility", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pickle", "_typeshed", "abc", "api_abstraction.api", "configparser", "enum", "internal_api", "io", "mobility.ir", "mobility.workers", "os", "typing_extensions"], "hash": "639ec95e132c11ee81380fafc94f856155adb760", "id": "api_abstraction.internal.travel_time", "ignore_all": false, "interface_hash": "14d79a8afd39a98ac01920402b0f8da7604eceac", "mtime": 1751895952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\internal\\travel_time.py", "plugin_data": null, "size": 18710, "suppressed": ["geopy.distance", "geopy", "networkx"], "version_id": "1.16.1"}
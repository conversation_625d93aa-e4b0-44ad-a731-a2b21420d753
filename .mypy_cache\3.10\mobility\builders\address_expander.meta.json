{"data_mtime": 1751444413, "dep_lines": [5, 6, 7, 8, 9, 10, 18, 20, 23, 19, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.geocode_api", "mobility.builders.crit_air_computer", "mobility.builders.territory_database", "mobility.ir.geo_study", "mobility.ir.mode_constraints", "mobility.ir.study", "mobility.ir.study_types", "mobility.repositories.abstract_repositories", "mobility.workers.distance_computer", "mobility.quantity", "random", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "enum", "fractions", "functools", "mobility.ir", "mobility.ir.crit_air", "mobility.ir.employee", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.site", "mobility.ir.transport", "mobility.repositories", "mobility.workers", "numbers", "typing_extensions"], "hash": "234a412d1da37b86c60021007b3667b8d7c7f721", "id": "mobility.builders.address_expander", "ignore_all": false, "interface_hash": "c82f22c3bf6aa1691c27c1e9bada746d6d7e4d98", "mtime": 1752065849, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\builders\\address_expander.py", "plugin_data": null, "size": 6291, "suppressed": [], "version_id": "1.16.1"}
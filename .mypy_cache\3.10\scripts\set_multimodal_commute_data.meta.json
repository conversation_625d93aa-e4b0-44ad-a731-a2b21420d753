{"data_mtime": 1752154110, "dep_lines": [26, 27, 29, 30, 31, 32, 34, 35, 28, 33, 18, 19, 20, 21, 22, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.travel_time_api", "mobility.builders.json_builder", "mobility.ir.commute_data", "mobility.ir.scenario_data", "mobility.ir.study", "mobility.ir.transport", "mobility.serializers.json_serializer", "mobility.workers.emission_computer", "mobility.funky", "mobility.quantity", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "json", "logging", "os", "datetime", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "enum", "io", "json.decoder", "mobility", "mobility.builders", "mobility.ir", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.site", "mobility.ir.study_types", "typing_extensions", "mobility.workers"], "hash": "7be27ee6bc39165ed2331e312b27aca577a2f937", "id": "scripts.set_multimodal_commute_data", "ignore_all": false, "interface_hash": "5606f8d252c88a6a5bfa5bffde6a179e90ea1866", "mtime": 1753175229, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "scripts\\set_multimodal_commute_data.py", "plugin_data": null, "size": 5728, "suppressed": [], "version_id": "1.16.1"}
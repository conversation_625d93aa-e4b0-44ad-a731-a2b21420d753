from datetime import datetime, time, timedelta
from typing import Any, Dict, Tu<PERSON>
from unittest.mock import patch

import pytest
import requests

from api_abstraction.api.api import ApiInapt
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import ApiFail, JourneyAttribute
from api_abstraction.otp.api import OTPTravelTimeAPI
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode

OTP_INAPT_MODES = [TransportMode.AIRPLANE]
OTP_APT_MODES = [m for m in TransportMode if m not in OTP_INAPT_MODES]


class TestOTPTravelTimeAPI:

    def test_should_init_request_date_based_on_territory_availability(
        self, otp_default_router_response, monkeypatch
    ):
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_call_requests",
            lambda _, url, params: otp_default_router_response,
        )
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_compute_request_date",
            lambda x: datetime(2020, 9, 22, 8, 30),
        )
        api = OTPTravelTimeAPI("", EventReporter())

        assert api.get_request_date() == datetime(2020, 9, 22, 8, 30)

    def test_should_fail_init_if_requests_cant_connect(self, monkeypatch):

        def fail_get(*args, **kwargs):
            raise ConnectionError()

        monkeypatch.setattr(requests, "get", fail_get)
        api = OTPTravelTimeAPI("", EventReporter())

        with pytest.raises(ApiFail):
            api.get_request_date()

    @pytest.mark.parametrize(
        "mode,expected_mode_params,expected_journey",
        [
            (
                TransportMode.PUBLIC_TRANSPORT,
                {"mode": "TRANSIT,WALK"},
                JourneyAttribute(
                    duration=889,
                    distance=3614,
                    emission=347,
                ),
            ),
            (
                TransportMode.WALK,
                {"mode": "WALK"},
                JourneyAttribute(
                    duration=3038,
                    distance=3253,
                    emission=0,
                ),
            ),
            (
                TransportMode.CAR,
                {"mode": "CAR,WALK"},
                JourneyAttribute(
                    duration=918,
                    distance=4023,
                    emission=777,
                ),
            ),
            (
                TransportMode.BICYCLE,
                {"mode": "BICYCLE", "bikeSpeed": 4.17},
                JourneyAttribute(
                    duration=910,
                    distance=3305,
                    emission=0,
                ),
            ),
            (
                TransportMode.ELECTRIC_BICYCLE,
                {"mode": "BICYCLE", "bikeSpeed": 5.6},
                JourneyAttribute(
                    duration=715,
                    distance=3305,
                    emission=7,
                ),
            ),
            (
                TransportMode.CARPOOLING,
                {"mode": "CAR,WALK"},
                JourneyAttribute(
                    duration=918,
                    distance=4023,
                    emission=386,
                ),
            ),
            (
                TransportMode.MOTORCYCLE,
                {"mode": "CAR,WALK"},
                JourneyAttribute(
                    duration=918,
                    distance=4023,
                    emission=663,
                ),
            ),
            (
                TransportMode.ELECTRIC_CAR,
                {"mode": "CAR,WALK"},
                JourneyAttribute(
                    duration=918,
                    distance=4023,
                    emission=79,
                ),
            ),
            (
                TransportMode.FAST_BICYCLE,
                {"mode": "BICYCLE", "bikeSpeed": 12.5},
                JourneyAttribute(
                    duration=385,
                    distance=3305,
                    emission=7,
                ),
            ),
        ],
    )
    def test_should_compute_journey(
        self,
        otp_plan_response,
        monkeypatch,
        mode: TransportMode,
        expected_mode_params: Dict,
        expected_journey: JourneyAttribute,
    ):

        def check_and_answer_plan(url, params):
            dt = datetime(2020, 9, 22, 8, 30)
            base_params = {
                "arriveBy": True,
                "date": dt.date(),
                "fromPlace": "45.754,3.1141",
                "toPlace": "45.7743,3.0938",
                "time": dt.time(),
                "walkSpeed": 1.12,
                "maxWalkDistance": pytest.approx(6048),
            }
            for key, value in base_params.items():
                assert key in params
                assert params[key] == value
            for key, value in expected_mode_params.items():
                assert key in params
                assert params[key] == value
            return otp_plan_response[mode]

        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_call_requests",
            lambda _, url, params: check_and_answer_plan(url, params),
        )
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_compute_request_date",
            lambda x: datetime(2020, 9, 22, 8, 30),
        )
        api = OTPTravelTimeAPI("", EventReporter())

        journey = api.time(
            GeoCoordinates(45.7540, 3.1141),
            GeoCoordinates(45.7743, 3.0938),
            mode,
            (8, 30),
        )

        assert journey == expected_journey

    @pytest.mark.parametrize("mode", OTP_INAPT_MODES)
    def test_should_fail_to_compute_unhandled_modes(
        self, monkeypatch: Any, mode: TransportMode
    ) -> None:
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_call_requests",
            lambda _, url, params: {},
        )
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_compute_request_date",
            lambda x: datetime(2020, 9, 22, 8, 30),
        )
        api = OTPTravelTimeAPI("", EventReporter())

        with pytest.raises(ApiInapt):
            api.time(
                GeoCoordinates(42.0, 2.0),
                GeoCoordinates(54.0, 5.0),
                mode,
                (8, 30),
            )

    @pytest.mark.parametrize("mode", OTP_APT_MODES)
    def test_should_not_fail_to_compute_handled_modes(
        self, monkeypatch: Any, mode: TransportMode, otp_plan_response: Any
    ) -> None:
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_call_requests",
            lambda _, url, params: otp_plan_response[mode],
        )
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_compute_request_date",
            lambda x: datetime(2020, 9, 22, 8, 30),
        )
        api = OTPTravelTimeAPI("", EventReporter())

        result = api.time(
            GeoCoordinates(42.0, 2.0),
            GeoCoordinates(54.0, 5.0),
            mode,
            (8, 30),
        )

        assert isinstance(result, JourneyAttribute)

    @pytest.mark.parametrize("mode", OTP_APT_MODES)
    def test_should_compute_one_isochrone(
        self, monkeypatch, otp_default_router_response, otp_one_isochrone_response, mode
    ):

        def check_and_answer_isochrone(*args, **kwargs):
            assert "params" in kwargs
            assert "bikeSpeed" in kwargs["params"]
            if mode == TransportMode.ELECTRIC_BICYCLE:
                assert kwargs["params"]["bikeSpeed"] == pytest.approx(5.6)
            elif mode == TransportMode.FAST_BICYCLE:
                assert kwargs["params"]["bikeSpeed"] == pytest.approx(12.5)
            else:
                assert kwargs["params"]["bikeSpeed"] == pytest.approx(4.17)
            return otp_one_isochrone_response

        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_call_requests",
            lambda _, url, params: otp_default_router_response,
        )
        monkeypatch.setattr(
            OTPTravelTimeAPI,
            "_compute_request_date",
            lambda x: datetime(2020, 8, 20),
        )
        api = OTPTravelTimeAPI("", EventReporter())
        api._call_requests = check_and_answer_isochrone

        isochrone = api.compute_isochrone(
            None,
            GeoCoordinates(45.7743, 3.0938),
            mode,
            30 * 60,
        )

        assert "type" in isochrone
        assert "coordinates" in isochrone
        assert isinstance(isochrone["coordinates"], list)

    def test_should_not_jitter_and_return_just_the_given_coordinates(self):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(4.5, 46.2)
        jitter = [0.0]

        jittered = OTPTravelTimeAPI.jitter_coordinates(origin, destination, jitter)

        assert jittered == [(origin, destination)]

    def test_should_jitter_coordinates_around_given_coords_sorted_by_proximity(self):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(4.0, 46.0)
        jitter = [1.0, 0.0, 2.0]

        jittered = OTPTravelTimeAPI.jitter_coordinates(origin, destination, jitter)

        assert jittered[0] == (origin, destination)
        assert len(jittered) == 3**4
        assert jittered[-1] == (GeoCoordinates(2.0, 3.0), GeoCoordinates(6.0, 48.0))

    def test_should_two_jitter_coordinates_around_given_coords_sorted_by_proximity(
        self,
    ):
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(4.0, 46.0)
        jitter = [1.0, 0.0]

        jittered = OTPTravelTimeAPI.jitter_coordinates(origin, destination, jitter)

        assert len(jittered) == 2**4
        assert jittered[0] == (GeoCoordinates(0.0, 1.0), GeoCoordinates(4.0, 46.0))
        assert set(jittered[1:5]) == {
            (GeoCoordinates(1.0, 1.0), GeoCoordinates(4.0, 46.0)),
            (GeoCoordinates(0.0, 2.0), GeoCoordinates(4.0, 46.0)),
            (GeoCoordinates(0.0, 1.0), GeoCoordinates(5.0, 46.0)),
            (GeoCoordinates(0.0, 1.0), GeoCoordinates(4.0, 47.0)),
        }
        assert set(jittered[5:11]) == {
            (GeoCoordinates(1.0, 2.0), GeoCoordinates(4.0, 46.0)),
            (GeoCoordinates(1.0, 1.0), GeoCoordinates(5.0, 46.0)),
            (GeoCoordinates(1.0, 1.0), GeoCoordinates(4.0, 47.0)),
            (GeoCoordinates(0.0, 2.0), GeoCoordinates(5.0, 46.0)),
            (GeoCoordinates(0.0, 2.0), GeoCoordinates(4.0, 47.0)),
            (GeoCoordinates(0.0, 1.0), GeoCoordinates(5.0, 47.0)),
        }
        assert set(jittered[11:15]) == {
            (GeoCoordinates(1.0, 2.0), GeoCoordinates(5.0, 46.0)),
            (GeoCoordinates(1.0, 2.0), GeoCoordinates(4.0, 47.0)),
            (GeoCoordinates(1.0, 1.0), GeoCoordinates(5.0, 47.0)),
            (GeoCoordinates(0.0, 2.0), GeoCoordinates(5.0, 47.0)),
        }
        assert jittered[-1] == (GeoCoordinates(1.0, 2.0), GeoCoordinates(5.0, 47.0))

    def test_get_fastest_itinerary(self) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        plan = {
            "itineraries": [
                {"duration": 2000, "startTime": 1712036583000},
                {"duration": 1000, "startTime": 1712036583000},
                {"duration": 3000, "startTime": 1712036583000},
            ]
        }

        fastest_itinerary = api._get_fastest_itinerary(plan)

        assert fastest_itinerary == {"duration": 1000, "startTime": 1712036583000}

    @patch("api_abstraction.otp.api.OTPTravelTimeAPI.get_request_date")
    def test_get_itinerary_should_return_fastest_if_less_than_3_hours(
        self, mock_request_date: Any
    ) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        reference_date = datetime(2024, 4, 2, 8, 30)
        mock_request_date.return_value = reference_date
        plan = {
            "itineraries": [
                {
                    "duration": 2000,
                    "startTime": (reference_date - timedelta(hours=2)).timestamp()
                    * 1000,
                },
                {
                    "duration": 1000,
                    "startTime": (reference_date - timedelta(hours=2)).timestamp()
                    * 1000,
                },
            ]
        }

        itinerary = api._get_itinerary(plan, (8, 30))

        assert itinerary == plan["itineraries"][1]

    @patch("api_abstraction.otp.api.OTPTravelTimeAPI.get_request_date")
    def test_get_itinerary_should_return_first_if_more_than_3_hours(
        self, mock_request_date: Any
    ) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        reference_date = datetime(2024, 4, 2, 8, 30)
        mock_request_date.return_value = reference_date
        plan = {
            "itineraries": [
                {
                    "duration": 2000,
                    "startTime": (reference_date - timedelta(hours=2)).timestamp()
                    * 1000,
                },
                {
                    "duration": 1000,
                    "startTime": (reference_date - timedelta(hours=4)).timestamp()
                    * 1000,
                },
            ]
        }

        itinerary = api._get_itinerary(plan, (8, 30))

        assert itinerary == plan["itineraries"][0]

    @pytest.mark.parametrize(
        "test_input_time, expected",
        [
            ((8, 30), time(8, 30)),
            ((0, 0), time(0, 0)),
            ((23, 59), time(23, 59)),
        ],
    )
    def test_compute_arrival_request_time(
        self, test_input_time: Tuple[int, int], expected: time
    ):
        api = OTPTravelTimeAPI("", EventReporter())

        assert api._compute_arrival_request_time(test_input_time) == expected

    def test_filter_itineraries_above_max_transfers_should_filter_out_high_transfer_routes(
        self,
    ) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        plan = {
            "itineraries": [
                {"duration": 1000, "transfers": 0},
                {"duration": 1500, "transfers": 2},
                {"duration": 2000, "transfers": 5},
                {"duration": 1200, "transfers": 1},
            ]
        }
        max_transfers = 2

        filtered_plan = api._filter_itineraries_above_max_transfers(plan, max_transfers)

        expected_itineraries = [
            {"duration": 1000, "transfers": 0},
            {"duration": 1500, "transfers": 2},
            {"duration": 1200, "transfers": 1},
        ]
        assert filtered_plan["itineraries"] == expected_itineraries

    def test_filter_itineraries_above_max_transfers_should_handle_missing_transfers_attribute(
        self,
    ) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        plan = {
            "itineraries": [
                {"duration": 1000, "transfers": 1},
                {"duration": 1500},
                {"duration": 2000, "transfers": 3},
            ]
        }
        max_transfers = 2

        filtered_plan = api._filter_itineraries_above_max_transfers(plan, max_transfers)

        expected_itineraries = [
            {"duration": 1000, "transfers": 1},
            {"duration": 1500},
        ]
        assert filtered_plan["itineraries"] == expected_itineraries

    def test_filter_itineraries_above_max_transfers_should_return_all_when_none_match(
        self,
    ) -> None:
        api = OTPTravelTimeAPI("", EventReporter())
        plan = {
            "itineraries": [
                {"duration": 1000, "transfers": 5},
                {"duration": 1500, "transfers": 6},
                {"duration": 2000, "transfers": 7},
            ]
        }
        max_transfers = 2

        filtered_plan = api._filter_itineraries_above_max_transfers(plan, max_transfers)

        assert filtered_plan["itineraries"] == plan["itineraries"]

    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._call_requests")
    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._compute_request_date")
    def test_time_should_apply_max_transfers_filter_for_public_transport(
        self, mock_compute_date: Any, mock_call_requests: Any
    ) -> None:
        mock_compute_date.return_value = datetime(2020, 9, 22, 8, 30)
        mock_call_requests.return_value = {
            "plan": {
                "itineraries": [
                    {
                        "duration": 1000,
                        "transfers": 1,
                        "startTime": 1600768200000,
                        "endTime": 1600769200000,
                        "legs": [{"distance": 1000, "mode": "BUS"}],
                    },
                    {
                        "duration": 1200,
                        "transfers": 4,
                        "startTime": 1600768200000,
                        "endTime": 1600769400000,
                        "legs": [{"distance": 1200, "mode": "BUS"}],
                    },
                ]
            }
        }
        api = OTPTravelTimeAPI("", EventReporter())

        journey = api._time(
            GeoCoordinates(45.754, 3.1141),
            GeoCoordinates(45.7743, 3.0938),
            TransportMode.PUBLIC_TRANSPORT,
            (8, 30),
            max_transfers=2,
        )

        assert journey.duration == 1000

    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._call_requests")
    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._compute_request_date")
    def test_time_should_not_apply_max_transfers_filter_for_non_public_transport(
        self, mock_compute_date: Any, mock_call_requests: Any
    ) -> None:
        mock_compute_date.return_value = datetime(2020, 9, 22, 8, 30)
        mock_call_requests.return_value = {
            "plan": {
                "itineraries": [
                    {
                        "duration": 1000,
                        "startTime": 1600768200000,
                        "endTime": 1600769200000,
                        "legs": [{"distance": 1000, "mode": "CAR"}],
                    }
                ]
            }
        }
        api = OTPTravelTimeAPI("", EventReporter())

        journey = api._time(
            GeoCoordinates(45.754, 3.1141),
            GeoCoordinates(45.7743, 3.0938),
            TransportMode.CAR,
            (8, 30),
            max_transfers=2,
        )

        assert journey.duration == 1000

    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._call_requests")
    @patch("api_abstraction.otp.api.OTPTravelTimeAPI._compute_request_date")
    def test_time_should_not_apply_filter_when_max_transfers_is_none(
        self, mock_compute_date: Any, mock_call_requests: Any
    ) -> None:
        mock_compute_date.return_value = datetime(2020, 9, 22, 8, 30)
        mock_call_requests.return_value = {
            "plan": {
                "itineraries": [
                    {
                        "duration": 1000,
                        "transfers": 5,
                        "startTime": 1600768200000,
                        "endTime": 1600769200000,
                        "legs": [{"distance": 1000, "mode": "BUS"}],
                    }
                ]
            }
        }
        api = OTPTravelTimeAPI("", EventReporter())

        journey = api._time(
            GeoCoordinates(45.754, 3.1141),
            GeoCoordinates(45.7743, 3.0938),
            TransportMode.PUBLIC_TRANSPORT,
            (8, 30),
            max_transfers=None,
        )

        assert journey.duration == 1000

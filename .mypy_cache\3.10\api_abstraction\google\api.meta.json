{"data_mtime": 1753176801, "dep_lines": [8, 9, 10, 11, 12, 13, 17, 18, 19, 20, 21, 22, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["api_abstraction.api.api", "api_abstraction.api.date_iterators", "api_abstraction.api.event_reporter", "api_abstraction.api.hypotheses", "api_abstraction.api.travel_time_api", "api_abstraction.google.base_google_api", "api_abstraction.google.distance_matrix", "api_abstraction.google.routes_api", "mobility.ir.geo_study", "mobility.ir.territory", "mobility.ir.transport", "mobility.quantity", "logging", "collections", "datetime", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "api_abstraction.api", "enum", "mobility", "mobility.ir", "types", "typing_extensions"], "hash": "503c3ec13fd22803f1108edc46331348b419aed4", "id": "api_abstraction.google.api", "ignore_all": false, "interface_hash": "62754e2336ca959488cf83951785d2f97bb3dbde", "mtime": 1753175317, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "api_abstraction\\google\\api.py", "plugin_data": null, "size": 16868, "suppressed": ["geopy.distance"], "version_id": "1.16.1"}
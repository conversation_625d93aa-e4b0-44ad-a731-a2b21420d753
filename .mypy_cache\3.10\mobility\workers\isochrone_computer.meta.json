{"data_mtime": 1751444407, "dep_lines": [4, 5, 6, 7, 8, 9, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["api_abstraction.api.travel_time_api", "mobility.ir.site", "mobility.ir.study", "mobility.ir.territory", "mobility.ir.transport", "mobility.quantity", "logging", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "api_abstraction", "api_abstraction.api", "api_abstraction.api.api", "enum", "mobility.ir", "mobility.ir.commute_data", "mobility.ir.employee", "mobility.ir.geo_study", "mobility.ir.poi", "mobility.ir.scenario_data", "mobility.ir.study_types", "types", "typing_extensions"], "hash": "b855911b509a6b4ec00dcae63c5cd34ca18799f3", "id": "mobility.workers.isochrone_computer", "ignore_all": false, "interface_hash": "8157ed8c19544516315ff3cf93ab734085c1bd6b", "mtime": 1753175318, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\workers\\isochrone_computer.py", "plugin_data": null, "size": 2608, "suppressed": [], "version_id": "1.16.1"}
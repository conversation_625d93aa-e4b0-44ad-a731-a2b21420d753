{"data_mtime": 1753087662, "dep_lines": [4, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mobility.workers.employee_cost_calculator", "mobility.quantity", "pytest", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "mobility.workers", "typing"], "hash": "0db8cd1b127865269178298681164986ea9698cc", "id": "mobility.tests.workers.test_employee_cost_calculator", "ignore_all": false, "interface_hash": "6e7ef6aad18a3a187ad75bd7424619dfb266895b", "mtime": 1753175320, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "mobility\\tests\\workers\\test_employee_cost_calculator.py", "plugin_data": null, "size": 1343, "suppressed": [], "version_id": "1.16.1"}